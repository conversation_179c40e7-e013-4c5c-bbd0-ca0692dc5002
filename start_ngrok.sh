#!/bin/bash

echo "🚀 Starting ngrok tunnel for Dating App..."

# 检查ngrok是否安装
if ! command -v ngrok &> /dev/null; then
    echo "❌ ngrok not found. Installing ngrok..."
    echo "Please install ngrok first:"
    echo "  macOS: brew install ngrok"
    echo "  Or download from: https://ngrok.com/"
    exit 1
fi

# 启动ngrok
echo "📡 Starting ngrok tunnel on port 8000..."
ngrok http 8000 --log=stdout > ngrok.log 2>&1 &
NGROK_PID=$!

# 等待ngrok启动
echo "⏳ Waiting for ngrok to start..."
sleep 5

# 获取ngrok URL
echo "🔍 Getting ngrok URL..."
NGROK_URL=$(curl -s http://localhost:4040/api/tunnels | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    for tunnel in data.get('tunnels', []):
        if tunnel.get('proto') == 'https':
            print(tunnel.get('public_url', ''))
            break
except:
    pass
")

if [ -z "$NGROK_URL" ]; then
    echo "❌ Failed to get ngrok URL. Check if ngrok is running properly."
    kill $NGROK_PID 2>/dev/null
    exit 1
fi

echo "✅ ngrok tunnel started successfully!"
echo "🌐 Public URL: $NGROK_URL"
echo ""

# 更新.env文件
echo "🔄 Updating .env file..."
if [ -f "profilerAgent/.env" ]; then
    # 备份原文件
    cp profilerAgent/.env profilerAgent/.env.backup
    
    # 更新TWILIO_WEBHOOK_URL
    if grep -q "TWILIO_WEBHOOK_URL=" profilerAgent/.env; then
        sed -i.tmp "s|TWILIO_WEBHOOK_URL=.*|TWILIO_WEBHOOK_URL=$NGROK_URL|" profilerAgent/.env
        rm profilerAgent/.env.tmp 2>/dev/null
    else
        echo "TWILIO_WEBHOOK_URL=$NGROK_URL" >> profilerAgent/.env
    fi
    
    echo "✅ Updated TWILIO_WEBHOOK_URL in .env file"
else
    echo "❌ .env file not found at profilerAgent/.env"
fi

echo ""
echo "📋 Next steps:"
echo "1. 🔧 Update your TwiML App in Twilio Console:"
echo "   - Request URL: $NGROK_URL/voice/webhook/incoming"
echo "   - Fallback URL: $NGROK_URL/voice/webhook/fallback"
echo "   - Status Callback URL: $NGROK_URL/voice/webhook/status"
echo ""
echo "2. 🚀 Start your FastAPI server:"
echo "   cd profilerAgent/backend && python -m uvicorn main:app --reload --port 8000"
echo ""
echo "3. 🌐 Access ngrok dashboard: http://localhost:4040"
echo "4. 📖 Access API docs: $NGROK_URL/docs"
echo ""
echo "Press Ctrl+C to stop ngrok tunnel"

# 保持脚本运行，直到用户按Ctrl+C
trap "echo ''; echo '🛑 Stopping ngrok...'; kill $NGROK_PID 2>/dev/null; echo '✅ ngrok stopped'; exit 0" INT

# 显示ngrok日志
echo "📊 ngrok logs (press Ctrl+C to stop):"
echo "----------------------------------------"
tail -f ngrok.log
