#!/usr/bin/env python3
"""
删除指定用户的所有相关数据
"""

import asyncio
import sys
import os
import asyncpg
from datetime import datetime

# 添加项目路径
project_root = os.path.dirname(__file__)
sys.path.insert(0, project_root)

async def delete_user_data(phone_number: str):
    """删除用户的所有相关数据"""
    
    # 数据库连接配置
    DATABASE_URL = "postgresql://dating_app_user:dating_app_password@localhost:5432/dating_app_db"
    
    conn = None
    try:
        # 连接数据库
        conn = await asyncpg.connect(DATABASE_URL)
        print(f"✅ 已连接到数据库")
        
        # 1. 首先查找用户ID
        user_query = "SELECT id, first_name, last_name FROM users WHERE phone_number = $1"
        user_record = await conn.fetchrow(user_query, phone_number)
        
        if not user_record:
            print(f"❌ 未找到手机号为 {phone_number} 的用户")
            return
        
        user_id = user_record['id']
        user_name = f"{user_record['first_name']} {user_record['last_name']}"
        print(f"🔍 找到用户: {user_name} (ID: {user_id})")
        
        # 2. 开始事务
        async with conn.transaction():
            deleted_counts = {}
            
            # 按照外键依赖顺序删除数据
            
            # 删除聊天消息 (依赖于 user_matches)
            result = await conn.execute("""
                DELETE FROM user_chat_messages 
                WHERE match_id IN (
                    SELECT id FROM user_matches 
                    WHERE user1_id = $1 OR user2_id = $1
                )
            """, user_id)
            deleted_counts['user_chat_messages'] = int(result.split()[-1])
            
            # 删除匹配推荐历史
            result = await conn.execute("""
                DELETE FROM match_recommendations 
                WHERE user_id = $1 OR recommended_user_id = $1
            """, user_id)
            deleted_counts['match_recommendations'] = int(result.split()[-1])
            
            # 删除用户反馈
            result = await conn.execute("DELETE FROM user_feedback WHERE user_id = $1", user_id)
            deleted_counts['user_feedback'] = int(result.split()[-1])
            
            # 删除用户匹配关系
            result = await conn.execute("""
                DELETE FROM user_matches 
                WHERE user1_id = $1 OR user2_id = $1
            """, user_id)
            deleted_counts['user_matches'] = int(result.split()[-1])
            
            # 删除画像卡片
            result = await conn.execute("DELETE FROM profile_cards WHERE user_id = $1", user_id)
            deleted_counts['profile_cards'] = int(result.split()[-1])
            
            # 删除用户偏好设置
            result = await conn.execute("DELETE FROM user_preferences WHERE user_id = $1", user_id)
            deleted_counts['user_preferences'] = int(result.split()[-1])
            
            # 删除用户画像
            result = await conn.execute("DELETE FROM user_profiles WHERE user_id = $1", user_id)
            deleted_counts['user_profiles'] = int(result.split()[-1])
            
            # 删除LinkedIn验证数据
            result = await conn.execute("DELETE FROM linkedin_profiles WHERE user_id = $1", user_id)
            deleted_counts['linkedin_profiles'] = int(result.split()[-1])
            
            # 删除语音会话数据
            result = await conn.execute("DELETE FROM voice_sessions WHERE user_id = $1", user_id)
            deleted_counts['voice_sessions'] = int(result.split()[-1])
            
            # 删除短信验证记录
            result = await conn.execute("DELETE FROM sms_verifications WHERE phone_number = $1", phone_number)
            deleted_counts['sms_verifications'] = int(result.split()[-1])
            
            # 最后删除用户基础信息
            result = await conn.execute("DELETE FROM users WHERE id = $1", user_id)
            deleted_counts['users'] = int(result.split()[-1])
            
            print(f"\n🗑️ 删除统计:")
            print(f"   - 用户基础信息: {deleted_counts['users']} 条")
            print(f"   - 短信验证记录: {deleted_counts['sms_verifications']} 条")
            print(f"   - 语音会话数据: {deleted_counts['voice_sessions']} 条")
            print(f"   - LinkedIn验证: {deleted_counts['linkedin_profiles']} 条")
            print(f"   - 用户画像数据: {deleted_counts['user_profiles']} 条")
            print(f"   - 用户偏好设置: {deleted_counts['user_preferences']} 条")
            print(f"   - 画像卡片: {deleted_counts['profile_cards']} 条")
            print(f"   - 用户反馈: {deleted_counts['user_feedback']} 条")
            print(f"   - 匹配关系: {deleted_counts['user_matches']} 条")
            print(f"   - 推荐历史: {deleted_counts['match_recommendations']} 条")
            print(f"   - 聊天消息: {deleted_counts['user_chat_messages']} 条")
            
            total_deleted = sum(deleted_counts.values())
            print(f"\n✅ 总共删除了 {total_deleted} 条记录")
            
    except Exception as e:
        print(f"❌ 删除过程中出错: {e}")
        raise
    finally:
        if conn:
            await conn.close()
            print("🔐 数据库连接已关闭")

async def main():
    phone_number = "+15103650664"
    
    print(f"⚠️  警告: 即将删除手机号 {phone_number} 用户的所有数据")
    print("这个操作不可逆转！")
    
    # 确认操作
    confirm = input("请输入 'DELETE' 来确认删除: ")
    if confirm != "DELETE":
        print("❌ 操作已取消")
        return
    
    print(f"\n🚀 开始删除用户数据...")
    await delete_user_data(phone_number)
    print(f"✅ 用户数据删除完成！")

if __name__ == "__main__":
    asyncio.run(main()) 