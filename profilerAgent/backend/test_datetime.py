#!/usr/bin/env python3
"""
测试datetime导入问题
"""

import sys
import os

# 添加项目根路径
project_root = os.path.dirname(os.path.dirname(__file__))
sys.path.insert(0, project_root)

print("Testing datetime import...")

try:
    from datetime import datetime
    print(f"✅ datetime imported successfully: {datetime.now()}")
except Exception as e:
    print(f"❌ datetime import failed: {e}")

try:
    from models.api_models import HealthCheckResponse
    print("✅ HealthCheckResponse imported successfully")
except Exception as e:
    print(f"❌ HealthCheckResponse import failed: {e}")

try:
    from database.connection import get_connection_status
    print("✅ get_connection_status imported successfully")
except Exception as e:
    print(f"❌ get_connection_status import failed: {e}")

# 测试在函数中使用datetime
def test_datetime_in_function():
    try:
        now = datetime.now()
        return {"timestamp": now.isoformat()}
    except Exception as e:
        return {"error": str(e)}

result = test_datetime_in_function()
print(f"Function test result: {result}")
