"""
JWT认证中间件
验证和解析JWT token
"""

import logging
from typing import Optional
from fastapi import HTTPEx<PERSON>, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import sys
import os

# 添加项目根路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
sys.path.insert(0, project_root)

logger = logging.getLogger(__name__)

security = HTTPBearer()

def get_agent():
    """获取Agent实例"""
    try:
        from main import agent_instance
        if agent_instance is None:
            raise HTTPException(status_code=503, detail="Agent service not available")
        return agent_instance
    except ImportError:
        raise HTTPException(status_code=503, detail="Agent service not initialized")

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """
    从JWT token获取当前用户
    用于需要认证的API端点
    """
    try:
        agent = get_agent()
        
        # 验证JWT token
        token_result = agent.verify_access_token(credentials.credentials)
        
        if not token_result.success:
            raise HTTPException(
                status_code=401,
                detail="Invalid or expired token",
                headers={"WWW-Authenticate": "Bearer"}
            )
        
        user_id = token_result.data["user_id"]
        
        # 获取用户信息
        user = await agent.user_manager.get_user(user_id)
        if not user:
            raise HTTPException(
                status_code=401,
                detail="User not found",
                headers={"WWW-Authenticate": "Bearer"}
            )
        
        return {"user_id": user_id, "user": user}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Token verification failed: {e}")
        raise HTTPException(
            status_code=401,
            detail="Token verification failed",
            headers={"WWW-Authenticate": "Bearer"}
        )

async def get_current_user_optional(credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)):
    """
    可选的用户认证
    用于可以匿名访问但有token时提供更多信息的API
    """
    if not credentials:
        return None
    
    try:
        return await get_current_user(credentials)
    except HTTPException:
        return None

def require_verification_status(required_status: str):
    """
    要求特定的验证状态
    用于需要特定验证级别的API端点
    """
    async def verify_status(user = Depends(get_current_user)):
        if user.verification_status.value != required_status:
            raise HTTPException(
                status_code=403,
                detail=f"Verification status '{required_status}' required"
            )
        return user
    
    return verify_status

def require_min_verification_status(min_status: str):
    """
    要求最低验证状态
    验证状态层级：pending < sms_verified < voice_completed < fully_verified
    """
    status_levels = {
        "pending": 0,
        "sms_verified": 1,
        "voice_completed": 2,
        "fully_verified": 3
    }
    
    async def verify_min_status(user = Depends(get_current_user)):
        user_level = status_levels.get(user.verification_status.value, 0)
        required_level = status_levels.get(min_status, 0)
        
        if user_level < required_level:
            raise HTTPException(
                status_code=403,
                detail=f"Minimum verification status '{min_status}' required"
            )
        return user
    
    return verify_min_status
