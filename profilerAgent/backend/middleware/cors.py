"""
CORS中间件配置
支持前端跨域请求
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import sys
import os

# 添加项目根路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
sys.path.insert(0, project_root)

from agent.config.core_config import CoreConfig

def add_cors_middleware(app: FastAPI):
    """添加CORS中间件"""
    
    # 根据环境配置CORS
    if CoreConfig.APP_ENV == "development":
        # 开发环境：允许所有来源
        allowed_origins = ["*"]
        allow_credentials = False
    else:
        # 生产环境：限制来源
        allowed_origins = [
            "https://yourdomain.com",
            "https://www.yourdomain.com",
            # 添加你的前端域名
        ]
        allow_credentials = True
    
    app.add_middleware(
        CORSMiddleware,
        allow_origins=allowed_origins,
        allow_credentials=allow_credentials,
        allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        allow_headers=["*"],
        expose_headers=["*"]
    )
