-- AI Dating App 数据库初始化脚本
-- 基于Web应用 + 手机验证的MVP技术规格设计

-- 启用UUID扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 用户基础信息表
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    phone_number VARCHAR(20) UNIQUE NOT NULL,
    email VARCHAR(255),
    first_name VA<PERSON>HAR(100) NOT NULL,
    last_name VA<PERSON><PERSON><PERSON>(100) NOT NULL,
    age INTEGER CHECK (age >= 18 AND age <= 100),
    city VARCHAR(100),
    profession VARCHAR(200),
    sms_verified BOOLEAN DEFAULT FALSE,
    voice_call_completed BOOLEAN DEFAULT FALSE,
    linkedin_verified BOOLEAN DEFAULT FALSE,
    verification_status VARCHAR(20) DEFAULT 'pending' CHECK (verification_status IN ('pending', 'sms_verified', 'voice_completed', 'linkedin_verified', 'fully_verified')),
    verification_level VARCHAR(20) DEFAULT 'unverified' CHECK (verification_level IN ('unverified', 'partial', 'verified', 'high_trust')),
    last_login TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 短信验证表
CREATE TABLE IF NOT EXISTS sms_verifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    phone_number VARCHAR(20) NOT NULL,
    verification_code VARCHAR(10) NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    verified_at TIMESTAMP WITH TIME ZONE,
    attempts INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 语音通话会话表
CREATE TABLE IF NOT EXISTS voice_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    twilio_call_sid VARCHAR(255),
    call_duration INTEGER, -- 通话时长（秒）
    analysis_data JSONB, -- AI分析结果
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- LinkedIn验证表
CREATE TABLE IF NOT EXISTS linkedin_profiles (
    user_id UUID REFERENCES users(id) ON DELETE CASCADE PRIMARY KEY,
    linkedin_url VARCHAR(500),
    profile_data JSONB, -- LinkedIn抓取的数据
    verification_status VARCHAR(20) DEFAULT 'pending' CHECK (verification_status IN ('pending', 'verified', 'inconsistent')),
    consistency_score DECIMAL(3,2), -- 与语音信息的一致性分数
    verified_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 用户完整画像表
CREATE TABLE IF NOT EXISTS user_profiles (
    user_id UUID REFERENCES users(id) ON DELETE CASCADE PRIMARY KEY,
    voice_analysis JSONB, -- 语音通话分析结果
    linkedin_data JSONB, -- LinkedIn验证数据
    final_profile JSONB, -- 综合生成的最终画像
    confidence_scores JSONB, -- 各维度置信度
    mbti_type VARCHAR(4), -- MBTI性格类型
    verification_level VARCHAR(20) CHECK (verification_level IN ('high', 'medium', 'low')),
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 用户偏好设置表
CREATE TABLE IF NOT EXISTS user_preferences (
    user_id UUID REFERENCES users(id) ON DELETE CASCADE PRIMARY KEY,
    age_range_min INTEGER DEFAULT 18,
    age_range_max INTEGER DEFAULT 45,
    max_distance INTEGER DEFAULT 50, -- 公里
    preferred_mbti_types VARCHAR(4)[],
    deal_breakers TEXT[],
    interests TEXT[],
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 用户画像卡片表
CREATE TABLE IF NOT EXISTS profile_cards (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    card_type VARCHAR(50) NOT NULL CHECK (card_type IN ('personality_mbti', 'interests', 'lifestyle', 'social_style', 'relationship_goals')),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    confidence DECIMAL(3,2) CHECK (confidence >= 0.00 AND confidence <= 1.00),
    tags TEXT[],
    evidence TEXT[], -- 来自语音对话的支持证据
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 用户反馈表
CREATE TABLE IF NOT EXISTS user_feedback (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    card_id UUID REFERENCES profile_cards(id) ON DELETE CASCADE,
    feedback_type VARCHAR(20) CHECK (feedback_type IN ('like', 'dislike', 'accurate', 'inaccurate')),
    comment TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 用户匹配关系表
CREATE TABLE IF NOT EXISTS user_matches (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user1_id UUID REFERENCES users(id) ON DELETE CASCADE,
    user2_id UUID REFERENCES users(id) ON DELETE CASCADE,
    status VARCHAR(20) DEFAULT 'recommended' CHECK (status IN ('recommended', 'interested', 'matched', 'passed', 'blocked')),
    match_score INTEGER CHECK (match_score >= 0 AND match_score <= 100),
    recommendation_reason TEXT, -- AI解释推荐原因
    user1_action VARCHAR(20) CHECK (user1_action IN ('interested', 'passed')),
    user2_action VARCHAR(20) CHECK (user2_action IN ('interested', 'passed')),
    matched_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user1_id, user2_id),
    CHECK (user1_id != user2_id)
);

-- 匹配推荐历史表
CREATE TABLE IF NOT EXISTS match_recommendations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    recommended_user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    recommendation_batch VARCHAR(50), -- 每日推荐批次标识
    sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    user_response VARCHAR(20) CHECK (user_response IN ('interested', 'passed', 'no_response')),
    response_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 用户聊天消息表（匹配成功后的站内消息）
CREATE TABLE IF NOT EXISTS user_chat_messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    match_id UUID REFERENCES user_matches(id) ON DELETE CASCADE,
    sender_id UUID REFERENCES users(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    message_type VARCHAR(20) DEFAULT 'text' CHECK (message_type IN ('text', 'image', 'emoji')),
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    read_at TIMESTAMP WITH TIME ZONE
);

-- Agent决策记录表
CREATE TABLE IF NOT EXISTS agent_decisions (
    id SERIAL PRIMARY KEY,
    call_sid VARCHAR(255) NOT NULL,
    decision_type VARCHAR(50) NOT NULL CHECK (decision_type IN ('stage_advance', 'question_generate', 'strategy_change', 'time_management', 'quality_assessment')),
    decision_data JSONB NOT NULL,
    reasoning TEXT,
    confidence DOUBLE PRECISION DEFAULT 1.0 CHECK (confidence >= 0.0 AND confidence <= 1.0),
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
);

-- Agent目标管理表
CREATE TABLE IF NOT EXISTS agent_goals (
    id SERIAL PRIMARY KEY,
    call_sid VARCHAR(255) NOT NULL,
    goal_name VARCHAR(100) NOT NULL,
    goal_status VARCHAR(50) DEFAULT 'active' CHECK (goal_status IN ('active', 'completed', 'failed', 'skipped')),
    goal_progress DOUBLE PRECISION DEFAULT 0.0 CHECK (goal_progress >= 0.0 AND goal_progress <= 1.0),
    goal_priority INTEGER DEFAULT 1,
    goal_data JSONB,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
    UNIQUE(call_sid, goal_name)
);

-- Agent记忆存储表
CREATE TABLE IF NOT EXISTS agent_memory (
    id SERIAL PRIMARY KEY,
    call_sid VARCHAR(255) NOT NULL,
    memory_type VARCHAR(50) NOT NULL CHECK (memory_type IN ('short_term', 'long_term', 'context', 'user_profile')),
    memory_data JSONB NOT NULL,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
    UNIQUE(call_sid, memory_type)
);

-- Agent性能指标表
CREATE TABLE IF NOT EXISTS agent_metrics (
    id SERIAL PRIMARY KEY,
    call_sid VARCHAR(255) NOT NULL,
    metric_name VARCHAR(100) NOT NULL,
    metric_value DOUBLE PRECISION NOT NULL,
    metric_unit VARCHAR(20),
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
    UNIQUE(call_sid, metric_name, created_at)
);

-- Agent策略配置表
CREATE TABLE IF NOT EXISTS agent_strategies (
    id SERIAL PRIMARY KEY,
    call_sid VARCHAR(255) NOT NULL,
    strategy_name VARCHAR(100) NOT NULL,
    strategy_config JSONB NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
    UNIQUE(call_sid, strategy_name)
);

-- Agent对话上下文表
CREATE TABLE IF NOT EXISTS agent_conversations (
    id SERIAL PRIMARY KEY,
    call_sid VARCHAR(255) NOT NULL,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    conversation_stage VARCHAR(50) NOT NULL CHECK (conversation_stage IN ('greeting', 'professional', 'personality', 'interests', 'relationships', 'closing', 'completed')),
    topic_transitions JSONB DEFAULT '[]'::jsonb,
    emotional_state JSONB DEFAULT '{}'::jsonb,
    engagement_score DOUBLE PRECISION DEFAULT 0.0 CHECK (engagement_score >= 0.0 AND engagement_score <= 1.0),
    conversation_quality DOUBLE PRECISION DEFAULT 0.0 CHECK (conversation_quality >= 0.0 AND conversation_quality <= 1.0),
    total_exchanges INTEGER DEFAULT 0 CHECK (total_exchanges >= 0),
    successful_responses INTEGER DEFAULT 0 CHECK (successful_responses >= 0),
    timeout_count INTEGER DEFAULT 0 CHECK (timeout_count >= 0),
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
    UNIQUE(call_sid)
);

-- Agent学习记录表
CREATE TABLE IF NOT EXISTS agent_learning (
    id SERIAL PRIMARY KEY,
    call_sid VARCHAR(255) NOT NULL,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    learning_type VARCHAR(50) NOT NULL CHECK (learning_type IN ('user_feedback', 'conversation_outcome', 'strategy_effectiveness', 'question_success', 'emotional_response')),
    trigger_event VARCHAR(100) NOT NULL,
    input_data JSONB NOT NULL,
    learning_outcome JSONB NOT NULL,
    confidence_change DOUBLE PRECISION DEFAULT 0.0 CHECK (confidence_change >= -1.0 AND confidence_change <= 1.0),
    effectiveness_score DOUBLE PRECISION CHECK (effectiveness_score >= 0.0 AND effectiveness_score <= 1.0),
    applied_immediately BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
);

-- 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_users_phone_number ON users(phone_number);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_verification_status ON users(verification_status);
CREATE INDEX IF NOT EXISTS idx_sms_verifications_phone ON sms_verifications(phone_number);
CREATE INDEX IF NOT EXISTS idx_sms_verifications_expires ON sms_verifications(expires_at);
CREATE INDEX IF NOT EXISTS idx_voice_sessions_user_id ON voice_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_linkedin_profiles_user_id ON linkedin_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_profiles_user_id ON user_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_profiles_mbti ON user_profiles(mbti_type);
CREATE INDEX IF NOT EXISTS idx_user_preferences_user_id ON user_preferences(user_id);
CREATE INDEX IF NOT EXISTS idx_profile_cards_user_id ON profile_cards(user_id);
CREATE INDEX IF NOT EXISTS idx_profile_cards_type ON profile_cards(card_type);
CREATE INDEX IF NOT EXISTS idx_user_feedback_user_id ON user_feedback(user_id);
CREATE INDEX IF NOT EXISTS idx_user_feedback_card_id ON user_feedback(card_id);
CREATE INDEX IF NOT EXISTS idx_user_matches_user1 ON user_matches(user1_id);
CREATE INDEX IF NOT EXISTS idx_user_matches_user2 ON user_matches(user2_id);
CREATE INDEX IF NOT EXISTS idx_user_matches_status ON user_matches(status);
CREATE INDEX IF NOT EXISTS idx_match_recommendations_user_id ON match_recommendations(user_id);
CREATE INDEX IF NOT EXISTS idx_match_recommendations_batch ON match_recommendations(recommendation_batch);
CREATE INDEX IF NOT EXISTS idx_user_chat_messages_match_id ON user_chat_messages(match_id);
CREATE INDEX IF NOT EXISTS idx_user_chat_messages_timestamp ON user_chat_messages(timestamp);
CREATE INDEX IF NOT EXISTS idx_agent_decisions_call_sid ON agent_decisions(call_sid);
CREATE INDEX IF NOT EXISTS idx_agent_goals_call_sid ON agent_goals(call_sid);
CREATE INDEX IF NOT EXISTS idx_agent_memory_call_sid ON agent_memory(call_sid);
CREATE INDEX IF NOT EXISTS idx_agent_metrics_call_sid ON agent_metrics(call_sid);
CREATE INDEX IF NOT EXISTS idx_agent_strategies_call_sid ON agent_strategies(call_sid);
CREATE INDEX IF NOT EXISTS idx_agent_conversations_call_sid ON agent_conversations(call_sid);
CREATE INDEX IF NOT EXISTS idx_agent_conversations_user_id ON agent_conversations(user_id);
CREATE INDEX IF NOT EXISTS idx_agent_conversations_stage ON agent_conversations(conversation_stage);
CREATE INDEX IF NOT EXISTS idx_agent_conversations_created_at ON agent_conversations(created_at);
CREATE INDEX IF NOT EXISTS idx_agent_learning_call_sid ON agent_learning(call_sid);
CREATE INDEX IF NOT EXISTS idx_agent_learning_user_id ON agent_learning(user_id);
CREATE INDEX IF NOT EXISTS idx_agent_learning_type ON agent_learning(learning_type);
CREATE INDEX IF NOT EXISTS idx_agent_learning_created_at ON agent_learning(created_at);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为相关表添加更新时间触发器
CREATE TRIGGER update_users_updated_at
    BEFORE UPDATE ON users
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_profile_cards_updated_at
    BEFORE UPDATE ON profile_cards
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_preferences_updated_at
    BEFORE UPDATE ON user_preferences
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_agent_conversations_updated_at
    BEFORE UPDATE ON agent_conversations
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 插入测试数据（可选）
-- INSERT INTO users (phone_number, first_name, last_name, age, city, sms_verified) VALUES
-- ('+1234567890', 'Test', 'User', 25, 'San Francisco', true);

COMMENT ON TABLE users IS '用户基础信息表 - 基于手机号验证';
COMMENT ON TABLE sms_verifications IS '短信验证表';
COMMENT ON TABLE voice_sessions IS '语音通话会话表';
COMMENT ON TABLE linkedin_profiles IS 'LinkedIn验证表';
COMMENT ON TABLE user_profiles IS '用户完整画像表';
COMMENT ON TABLE user_preferences IS '用户偏好设置表';
COMMENT ON TABLE profile_cards IS '用户画像卡片表';
COMMENT ON TABLE user_feedback IS '用户反馈表';
COMMENT ON TABLE user_matches IS '用户匹配关系表';
COMMENT ON TABLE match_recommendations IS '匹配推荐历史表';
COMMENT ON TABLE user_chat_messages IS '用户聊天消息表';
COMMENT ON TABLE agent_decisions IS 'Agent决策记录表';
COMMENT ON TABLE agent_goals IS 'Agent目标管理表';
COMMENT ON TABLE agent_memory IS 'Agent记忆存储表';
COMMENT ON TABLE agent_metrics IS 'Agent性能指标表';
COMMENT ON TABLE agent_strategies IS 'Agent策略配置表';
COMMENT ON TABLE agent_conversations IS 'Agent对话上下文表';
COMMENT ON TABLE agent_learning IS 'Agent学习记录表';
