"""
数据库连接管理
为Agent提供数据库和Redis连接
"""

from datetime import datetime
import logging
from typing import Optional
from sqlalchemy import create_engine, Engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.ext.asyncio import create_async_engine, AsyncEngine, async_sessionmaker, AsyncSession
import redis
import sys
import os

# 添加项目根路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
sys.path.insert(0, project_root)

from agent.config.core_config import CoreConfig

logger = logging.getLogger(__name__)

# 全局连接实例
_db_engine: Optional[Engine] = None
_async_db_engine: Optional[AsyncEngine] = None
_redis_client: Optional[redis.Redis] = None
_session_factory: Optional[sessionmaker] = None
_async_session_factory: Optional[async_sessionmaker] = None

def get_db_engine() -> Engine:
    """获取数据库引擎"""
    global _db_engine
    
    if _db_engine is None:
        try:
            db_config = CoreConfig.get_database_config()
            
            _db_engine = create_engine(
                db_config["url"],
                pool_size=db_config["pool_size"],
                max_overflow=db_config["max_overflow"],
                pool_timeout=db_config["pool_timeout"],
                pool_pre_ping=True,  # 验证连接有效性
                echo=False  # 关闭SQL日志输出
            )
            
            logger.info(f"Database engine created: {db_config['url']}")
            
        except Exception as e:
            logger.error(f"Failed to create database engine: {e}")
            raise
    
    return _db_engine

def get_async_db_engine() -> AsyncEngine:
    """获取异步数据库引擎"""
    global _async_db_engine
    
    if _async_db_engine is None:
        try:
            db_config = CoreConfig.get_database_config()
            
            # 将同步URL转换为异步URL
            async_url = db_config["url"].replace("postgresql://", "postgresql+asyncpg://")
            
            _async_db_engine = create_async_engine(
                async_url,
                pool_size=db_config["pool_size"],
                max_overflow=db_config["max_overflow"],
                pool_timeout=db_config["pool_timeout"],
                pool_pre_ping=True,  # 验证连接有效性
                echo=False  # 关闭SQL日志输出
            )
            
            logger.info(f"Async database engine created: {async_url}")
            
        except Exception as e:
            logger.error(f"Failed to create async database engine: {e}")
            raise
    
    return _async_db_engine

def get_redis_client() -> redis.Redis:
    """获取Redis客户端"""
    global _redis_client
    
    if _redis_client is None:
        try:
            redis_config = CoreConfig.get_redis_config()
            
            _redis_client = redis.from_url(
                redis_config["url"],
                max_connections=redis_config["max_connections"],
                socket_timeout=redis_config["socket_timeout"],
                decode_responses=True  # 自动解码响应
            )
            
            # 测试连接
            _redis_client.ping()
            
            logger.info(f"Redis client created: {redis_config['url']}")
            
        except Exception as e:
            logger.error(f"Failed to create Redis client: {e}")
            raise
    
    return _redis_client

def get_session_factory() -> sessionmaker:
    """获取Session工厂"""
    global _session_factory
    
    if _session_factory is None:
        engine = get_db_engine()
        _session_factory = sessionmaker(bind=engine)
        logger.info("Session factory created")
    
    return _session_factory

def get_async_session_factory() -> async_sessionmaker:
    """获取异步Session工厂"""
    global _async_session_factory
    
    if _async_session_factory is None:
        engine = get_async_db_engine()
        _async_session_factory = async_sessionmaker(bind=engine, expire_on_commit=False)
        logger.info("Async session factory created")
    
    return _async_session_factory

def get_db_session() -> Session:
    """获取数据库Session"""
    session_factory = get_session_factory()
    return session_factory()

def get_async_db_session() -> AsyncSession:
    """获取异步数据库Session"""
    session_factory = get_async_session_factory()
    return session_factory()

def test_database_connection() -> dict:
    """测试数据库连接"""
    try:
        engine = get_db_engine()
        
        # 执行简单查询测试连接
        from sqlalchemy import text
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1 as test"))
            test_value = result.fetchone()[0]
            
        return {
            "status": "connected",
            "test_query": test_value == 1,
            "url": str(engine.url).replace(engine.url.password or '', '***')
        }
        
    except Exception as e:
        logger.error(f"Database connection test failed: {e}")
        return {
            "status": "error",
            "error": str(e)
        }

async def test_async_database_connection() -> dict:
    """测试异步数据库连接"""
    try:
        engine = get_async_db_engine()
        
        # 执行简单查询测试连接
        from sqlalchemy import text
        async with engine.begin() as conn:
            result = await conn.execute(text("SELECT 1 as test"))
            test_value = result.fetchone()[0]
            
        return {
            "status": "connected",
            "test_query": test_value == 1,
            "url": str(engine.url).replace(engine.url.password or '', '***')
        }
        
    except Exception as e:
        logger.error(f"Async database connection test failed: {e}")
        return {
            "status": "error",
            "error": str(e)
        }

def test_redis_connection() -> dict:
    """测试Redis连接"""
    try:
        client = get_redis_client()
        
        # 测试基本操作
        test_key = "health_check"
        client.set(test_key, "ok", ex=10)  # 10秒过期
        test_value = client.get(test_key)
        client.delete(test_key)
        
        return {
            "status": "connected",
            "test_operation": test_value == "ok",
            "info": {
                "version": client.info().get("redis_version"),
                "memory": client.info().get("used_memory_human")
            }
        }
        
    except Exception as e:
        logger.error(f"Redis connection test failed: {e}")
        return {
            "status": "error", 
            "error": str(e)
        }

async def close_connections():
    """关闭所有连接"""
    global _db_engine, _async_db_engine, _redis_client, _session_factory, _async_session_factory
    
    try:
        if _db_engine:
            _db_engine.dispose()
            _db_engine = None
            logger.info("Database engine disposed")
            
        if _async_db_engine:
            await _async_db_engine.dispose()
            _async_db_engine = None
            logger.info("Async database engine disposed")
            
        if _redis_client:
            _redis_client.close()
            _redis_client = None
            logger.info("Redis client closed")
            
        _session_factory = None
        _async_session_factory = None
        
    except Exception as e:
        logger.error(f"Error closing connections: {e}")

async def get_connection_status() -> dict:
    """获取连接状态"""
    return {
        "database": test_database_connection(),
        "async_database": await test_async_database_connection(),
        "redis": test_redis_connection(),
        "timestamp": datetime.now().isoformat()
    }
