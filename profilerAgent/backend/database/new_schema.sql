-- 新的AI Agent数据库架构
-- 简洁高效，专注于用户画像和对话管理

-- 删除所有旧表
DROP SCHEMA IF EXISTS public CASCADE;
CREATE SCHEMA public;

-- 用户基础信息表
CREATE TABLE users (
    user_id VARCHAR(50) PRIMARY KEY,
    phone_number VARCHAR(20) UNIQUE,
    email VARCHAR(255) UNIQUE,
    name VARCHAR(100),
    status VARCHAR(20) DEFAULT 'active',
    sms_verified BOOLEAN DEFAULT FALSE,
    voice_call_completed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用户画像表 - 核心AI Agent数据
CREATE TABLE user_profiles (
    user_id VARCHAR(50) PRIMARY KEY REFERENCES users(user_id) ON DELETE CASCADE,
    profile_data JSONB NOT NULL DEFAULT '{}',  -- 结构化用户画像
    confidence_scores JSONB DEFAULT '{}',      -- 各字段的置信度
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    conversation_count INTEGER DEFAULT 0,
    total_interactions INTEGER DEFAULT 0,
    
    -- 索引优化
    CONSTRAINT valid_profile_data CHECK (jsonb_typeof(profile_data) = 'object'),
    CONSTRAINT valid_confidence_scores CHECK (jsonb_typeof(confidence_scores) = 'object')
);

-- 对话会话表
CREATE TABLE conversations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id VARCHAR(50) NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    type VARCHAR(20) NOT NULL,  -- 'voice', 'chat', 'sms'
    status VARCHAR(20) DEFAULT 'active',  -- 'active', 'completed', 'abandoned'
    summary TEXT,  -- AI生成的对话总结
    context_data JSONB DEFAULT '{}',  -- 对话上下文信息
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP
);

-- 对话消息表
CREATE TABLE conversation_messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    conversation_id UUID NOT NULL REFERENCES conversations(id) ON DELETE CASCADE,
    role VARCHAR(20) NOT NULL,  -- 'user', 'assistant', 'system'
    content TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',  -- 消息元数据（置信度、工具调用等）
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用户短期记忆缓存表 - Redis的数据库备份
CREATE TABLE user_context_cache (
    user_id VARCHAR(50) PRIMARY KEY REFERENCES users(user_id) ON DELETE CASCADE,
    short_term_memory JSONB DEFAULT '[]',  -- 最近几轮对话
    conversation_summary TEXT,  -- 当前对话总结
    active_conversation_id UUID REFERENCES conversations(id),
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 自动清理旧数据
    CONSTRAINT recent_update CHECK (updated_at > CURRENT_TIMESTAMP - INTERVAL '7 days')
);

-- 语音通话会话表 - 简化版
CREATE TABLE voice_sessions (
    twilio_call_sid VARCHAR(100) PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL REFERENCES users(user_id),
    conversation_id UUID REFERENCES conversations(id),
    session_status VARCHAR(20) DEFAULT 'initiated',
    phone_number VARCHAR(20),
    call_duration INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP
);

-- 用户匹配表 - 简化版
CREATE TABLE user_matches (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id VARCHAR(50) NOT NULL REFERENCES users(user_id),
    matched_user_id VARCHAR(50) NOT NULL REFERENCES users(user_id),
    match_score DECIMAL(3,2),
    match_reasons JSONB DEFAULT '[]',
    status VARCHAR(20) DEFAULT 'pending',  -- 'pending', 'accepted', 'rejected'
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- 确保不重复匹配
    UNIQUE(user_id, matched_user_id),
    -- 确保不自己匹配自己
    CHECK (user_id != matched_user_id)
);

-- 系统配置表
CREATE TABLE system_config (
    key VARCHAR(100) PRIMARY KEY,
    value JSONB NOT NULL,
    description TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建更新时间触发器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 应用触发器
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON user_profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_context_cache_updated_at BEFORE UPDATE ON user_context_cache
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 插入默认系统配置
INSERT INTO system_config (key, value, description) VALUES
('ai_agent_config', '{"max_short_term_memory": 10, "summary_threshold": 20, "confidence_threshold": 0.7}', 'AI Agent配置参数'),
('matching_config', '{"min_score": 0.6, "max_matches_per_day": 10}', '匹配算法配置'),
('conversation_config', '{"max_message_length": 2000, "auto_summary_enabled": true}', '对话配置参数');

-- 创建用户画像示例数据结构的注释
COMMENT ON COLUMN user_profiles.profile_data IS 
'用户画像JSON结构示例:
{
  "basic_info": {
    "name": "张三",
    "age": 28,
    "city": "北京",
    "occupation": "软件工程师"
  },
  "personality": {
    "mbti": "INTJ",
    "traits": ["内向", "理性", "计划性强"],
    "social_energy": "introvert"
  },
  "interests": {
    "hobbies": ["编程", "阅读", "爬山"],
    "music": ["古典", "爵士"],
    "sports": ["羽毛球", "游泳"]
  },
  "relationship": {
    "looking_for": "长期关系",
    "preferences": ["有共同兴趣", "性格互补"],
    "deal_breakers": ["吸烟", "不爱运动"]
  },
  "communication": {
    "style": "直接",
    "response_pattern": "深思熟虑",
    "topics_of_interest": ["技术", "哲学", "旅行"]
  }
}';

COMMENT ON COLUMN user_profiles.confidence_scores IS
'置信度分数JSON结构示例:
{
  "basic_info.occupation": 0.95,
  "personality.mbti": 0.8,
  "interests.hobbies": 0.9,
  "relationship.looking_for": 0.85
}';

-- 创建所有索引
-- 用户表索引
CREATE INDEX idx_users_phone_number ON users(phone_number);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_status ON users(status);

-- 对话表索引
CREATE INDEX idx_conversations_user_id ON conversations(user_id);
CREATE INDEX idx_conversations_type ON conversations(type);
CREATE INDEX idx_conversations_status ON conversations(status);
CREATE INDEX idx_conversations_started_at ON conversations(started_at);

-- 消息表索引
CREATE INDEX idx_messages_conversation_id ON conversation_messages(conversation_id);
CREATE INDEX idx_messages_timestamp ON conversation_messages(timestamp);
CREATE INDEX idx_messages_role ON conversation_messages(role);

-- 语音会话索引
CREATE INDEX idx_voice_sessions_user_id ON voice_sessions(user_id);
CREATE INDEX idx_voice_sessions_status ON voice_sessions(session_status);

-- 匹配表索引
CREATE INDEX idx_matches_user_id ON user_matches(user_id);
CREATE INDEX idx_matches_status ON user_matches(status);
CREATE INDEX idx_matches_score ON user_matches(match_score DESC);

-- JSONB索引用于用户画像查询
CREATE INDEX idx_user_profiles_basic_info ON user_profiles USING GIN ((profile_data->'basic_info'));
CREATE INDEX idx_user_profiles_personality ON user_profiles USING GIN ((profile_data->'personality'));
CREATE INDEX idx_user_profiles_interests ON user_profiles USING GIN ((profile_data->'interests'));
