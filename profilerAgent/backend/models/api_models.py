"""
API请求和响应模型
用于FastAPI的输入验证和响应序列化
"""

from typing import Optional, Any, Dict, List
from pydantic import BaseModel, Field, validator
from datetime import datetime
import re

# ============ 基础响应模型 ============

class BaseResponse(BaseModel):
    """基础API响应模型"""
    success: bool
    message: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.now)

class ErrorResponse(BaseResponse):
    """错误响应模型"""
    success: bool = False
    error_code: Optional[str] = None
    details: Optional[Dict[str, Any]] = None

class SuccessResponse(BaseResponse):
    """成功响应模型"""
    success: bool = True
    data: Optional[Dict[str, Any]] = None

# ============ 健康检查模型 ============

class HealthCheckResponse(BaseModel):
    """健康检查响应"""
    status: str  # "healthy" | "unhealthy"
    version: str
    timestamp: datetime
    services: Dict[str, Any]
    agent_status: Dict[str, Any]

# ============ LinkedIn相关模型 ============

class LinkedInAuthResponse(BaseResponse):
    """LinkedIn授权响应"""
    data: Optional[Dict[str, Any]] = None

class LinkedInCallbackResponse(BaseResponse):
    """LinkedIn回调响应"""
    data: Optional[Dict[str, Any]] = None

class LinkedInProfileResponse(BaseResponse):
    """LinkedIn资料响应"""
    data: Optional[Dict[str, Any]] = None

# ============ 语音相关模型 ============

class VoiceCallResponse(BaseResponse):
    """语音通话响应"""
    data: Optional[Dict[str, Any]] = None

class VoiceStatusResponse(BaseResponse):
    """语音状态响应"""
    data: Optional[Dict[str, Any]] = None

class VoiceAnalysisResponse(BaseResponse):
    """语音分析响应"""
    data: Optional[Dict[str, Any]] = None

# ============ 认证相关模型 ============

class RegisterRequest(BaseModel):
    """用户注册请求"""
    phone_number: str = Field(..., description="手机号码")
    first_name: str = Field(..., min_length=1, max_length=50, description="名字")
    last_name: str = Field(..., min_length=1, max_length=50, description="姓氏")
    
    @validator('phone_number')
    def validate_phone_number(cls, v):
        # 简单的手机号验证
        if not re.match(r'^\+\d{10,15}$', v):
            raise ValueError('Invalid phone number format. Use +1234567890')
        return v

class RegisterResponse(SuccessResponse):
    """用户注册响应"""
    data: Dict[str, Any] = Field(default_factory=lambda: {
        "user_id": None,
        "phone_number": None,
        "verification_required": True,
        "sms_sent": False,
        "sms_error": None,
        "expires_in": None,
        "can_resend": True
    })

class SendSMSRequest(BaseModel):
    """发送SMS验证码请求"""
    phone_number: str = Field(..., description="手机号码")
    
    @validator('phone_number')
    def validate_phone_number(cls, v):
        if not re.match(r'^\+\d{10,15}$', v):
            raise ValueError('Invalid phone number format')
        return v

class SendSMSResponse(SuccessResponse):
    """发送SMS验证码响应"""
    data: Dict[str, Any] = Field(default_factory=lambda: {
        "phone_number": None,
        "expires_in": 300,
        "attempts_remaining": 3
    })

class VerifySMSRequest(BaseModel):
    """验证SMS验证码请求"""
    phone_number: str = Field(..., description="手机号码")
    verification_code: str = Field(..., min_length=4, max_length=10, description="验证码")

class VerifySMSResponse(SuccessResponse):
    """验证SMS验证码响应"""
    data: Dict[str, Any] = Field(default_factory=lambda: {
        "verified": False,
        "user_id": None,
        "access_token": None,
        "token_type": "bearer",
        "expires_in": 86400,
        "user_status": None,
        "verification_status": None,
        "next_step": None
    })

class LoginRequest(BaseModel):
    """登录请求"""
    phone_number: str = Field(..., description="手机号码")
    
    @validator('phone_number')
    def validate_phone_number(cls, v):
        if not re.match(r'^\+\d{10,15}$', v):
            raise ValueError('Invalid phone number format')
        return v

class LoginResponse(SuccessResponse):
    """登录响应"""
    data: Dict[str, Any] = Field(default_factory=lambda: {
        "access_token": None,
        "token_type": "bearer",
        "expires_in": 86400,
        "user_status": None,
        "verification_status": None,
        "next_step": None
    })

class LogoutResponse(SuccessResponse):
    """登出响应"""
    data: Dict[str, Any] = Field(default_factory=lambda: {
        "logged_out": True,
        "message": "Successfully logged out"
    })

class UserInfoResponse(SuccessResponse):
    """用户信息响应"""
    data: Dict[str, Any] = Field(default_factory=lambda: {
        "user_id": None,
        "phone_number": None,
        "first_name": None,
        "last_name": None,
        "status": None,
        "verification_status": None,
        "created_at": None,
        "last_login": None
    })

# ============ 用户相关模型 ============

class UserStatusResponse(SuccessResponse):
    """用户状态响应"""
    data: Dict[str, Any] = Field(default_factory=lambda: {
        "user_id": None,
        "status": None,
        "verification_status": None,
        "current_stage": None,
        "progress": {},
        "next_actions": []
    })

class UserProfileResponse(SuccessResponse):
    """用户画像响应"""
    data: Dict[str, Any] = Field(default_factory=lambda: {
        "user_id": None,
        "profile_cards": [],
        "verification_level": None,
        "completion_percentage": 0,
        "last_updated": None
    })

# ============ 语音相关模型 ============

class VoiceStatusResponse(SuccessResponse):
    """语音面试状态响应"""
    data: Dict[str, Any] = Field(default_factory=lambda: {
        "status": None,  # "pending" | "in_progress" | "completed" | "failed"
        "phone_number": None,
        "session_id": None,
        "duration": None,
        "analysis_status": None
    })

class VoiceStartResponse(SuccessResponse):
    """开始语音面试响应"""
    data: Dict[str, Any] = Field(default_factory=lambda: {
        "phone_number": None,
        "instructions": None,
        "session_id": None,
        "estimated_duration": "5-10 minutes"
    })

# ============ 匹配相关模型 ============

class MatchFeedbackRequest(BaseModel):
    """匹配反馈请求"""
    target_user_id: str = Field(..., description="目标用户ID")
    action: str = Field(..., description="用户行为")  # "like" | "pass"
    
    @validator('action')
    def validate_action(cls, v):
        if v not in ['like', 'pass']:
            raise ValueError('Action must be "like" or "pass"')
        return v

class MatchFeedbackResponse(SuccessResponse):
    """匹配反馈响应"""
    data: Dict[str, Any] = Field(default_factory=lambda: {
        "action_recorded": True,
        "is_mutual_match": False,
        "match_id": None,
        "message": None
    })

class DailyMatchesResponse(SuccessResponse):
    """每日匹配推荐响应"""
    data: Dict[str, Any] = Field(default_factory=lambda: {
        "matches": [],
        "total_count": 0,
        "remaining_today": 0,
        "next_refresh": None
    })

# ============ Webhook模型 ============

class WebhookResponse(BaseModel):
    """Webhook响应模型"""
    content: str = Field(..., description="响应内容")
    content_type: str = Field(default="application/xml", description="内容类型")

# ============ 通用模型 ============

class PaginationParams(BaseModel):
    """分页参数"""
    page: int = Field(default=1, ge=1, description="页码")
    limit: int = Field(default=20, ge=1, le=100, description="每页数量")

class PaginatedResponse(SuccessResponse):
    """分页响应"""
    data: Dict[str, Any] = Field(default_factory=lambda: {
        "items": [],
        "total": 0,
        "page": 1,
        "limit": 20,
        "has_next": False,
        "has_prev": False
    })

# ============ Profile API 响应模型 ============

class ProfileCardsResponse(SuccessResponse):
    """画像卡片响应模型"""
    data: Optional[Dict[str, Any]] = None

class ProfileGenerateResponse(SuccessResponse):
    """画像生成响应模型"""
    data: Optional[Dict[str, Any]] = None

# ============ Matches API 响应模型 ============

class MatchRecommendationsResponse(SuccessResponse):
    """匹配推荐响应模型"""
    data: Optional[Dict[str, Any]] = None

class MatchFeedbackResponse(SuccessResponse):
    """匹配反馈响应模型"""
    data: Optional[Dict[str, Any]] = None

class MatchHistoryResponse(SuccessResponse):
    """匹配历史响应模型"""
    data: Optional[Dict[str, Any]] = None

# ============ Users API 响应模型 ============

class UserProfileResponse(SuccessResponse):
    """用户信息响应模型"""
    data: Optional[Dict[str, Any]] = None

class UserPreferencesResponse(SuccessResponse):
    """用户偏好响应模型"""
    data: Optional[Dict[str, Any]] = None
