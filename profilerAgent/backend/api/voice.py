"""
语音服务API
语音通话、录音处理、AI分析功能
"""

import logging
from fastapi import APIRouter, HTTPException, Depends, Request, Form
from fastapi.security import HTTP<PERSON>earer, HTTPAuthorizationCredentials
from fastapi.responses import Response
import sys
import os

# 添加项目根路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
sys.path.insert(0, project_root)

from models.api_models import (
    VoiceCallResponse, VoiceStatusResponse, 
    VoiceAnalysisResponse, ErrorResponse
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/voice", tags=["Voice"])
security = HTTPBearer()

def get_agent():
    """获取Agent实例"""
    try:
        from main import agent_instance
        if agent_instance is None:
            raise HTTPException(status_code=503, detail="Agent service not available")
        return agent_instance
    except ImportError:
        raise HTTPException(status_code=503, detail="Agent service not initialized")

@router.get("/status/{user_id}", response_model=VoiceStatusResponse)
async def get_voice_status(user_id: str):
    """
    获取用户语音通话状态
    """
    try:
        agent = get_agent()
        
        # 验证用户存在
        user = await agent.user_manager.get_user(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # 检查用户是否已完成SMS验证
        if not user.sms_verified:
            raise HTTPException(
                status_code=400, 
                detail="SMS verification must be completed before voice interview"
            )
        
        # 获取语音状态
        result = await agent.get_voice_call_status(user_id)
        
        if not result.success:
            raise HTTPException(status_code=400, detail=result.error)
        
        return VoiceStatusResponse(
            success=True,
            data=result.data,
            message="Voice status retrieved successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Voice status check failed: {e}")
        raise HTTPException(status_code=500, detail="Voice status check failed")

@router.post("/initiate/{user_id}", response_model=VoiceCallResponse)
async def initiate_voice_call(user_id: str):
    """
    发起语音通话
    返回Twilio通话信息
    """
    try:
        agent = get_agent()
        
        # 发起语音通话
        result = await agent.initiate_voice_call(user_id)
        
        if not result.success:
            raise HTTPException(status_code=400, detail=result.error)
        
        return VoiceCallResponse(
            success=True,
            data=result.data,
            message="Voice call initiated successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Voice call initiation failed: {e}")
        raise HTTPException(status_code=500, detail="Voice call initiation failed")

@router.post("/webhook/incoming")
async def handle_incoming_call(request: Request):
    """
    处理Twilio语音webhook - 来电处理
    """
    try:
        agent = get_agent()
        
        # 获取Twilio webhook数据
        form_data = await request.form()
        call_data = dict(form_data)
        
        logger.info(f"Incoming call webhook: {call_data}")
        
        # 提取关键信息
        call_sid = call_data.get("CallSid")
        from_number = call_data.get("From")
        to_number = call_data.get("To")
        call_status = call_data.get("CallStatus")
        
        if not call_sid or not from_number:
            raise HTTPException(status_code=400, detail="Missing required call data")
        
        # 处理来电
        webhook_data = {
            "CallSid": call_sid,
            "From": from_number,
            "To": to_number,
            "CallStatus": call_status
        }
        result = await agent.handle_voice_webhook("incoming", webhook_data)
        
        if result.success:
            return Response(
                content=result.data["twiml"],
                media_type="application/xml"
            )
        else:
            # 返回基本的TwiML响应
            return Response(
                content='<?xml version="1.0" encoding="UTF-8"?><Response><Say>Sorry, there was an error. Please try again later.</Say></Response>',
                media_type="application/xml"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Incoming call webhook failed: {e}")
        # 返回基本的TwiML响应
        return Response(
            content='<?xml version="1.0" encoding="UTF-8"?><Response><Say>Sorry, there was an error. Please try again later.</Say></Response>',
            media_type="application/xml"
        )

@router.post("/webhook/recording")
async def handle_recording_webhook(request: Request):
    """
    处理Twilio录音webhook - 录音完成处理
    """
    try:
        agent = get_agent()
        
        # 获取Twilio webhook数据
        form_data = await request.form()
        recording_data = dict(form_data)
        
        logger.info(f"Recording webhook: {recording_data}")
        
        # 提取录音信息
        call_sid = recording_data.get("CallSid")
        recording_url = recording_data.get("RecordingUrl")
        recording_duration = recording_data.get("RecordingDuration")
        
        if not call_sid or not recording_url:
            raise HTTPException(status_code=400, detail="Missing required recording data")
        
        # 处理录音
        webhook_data = {
            "CallSid": call_sid,
            "RecordingUrl": recording_url,
            "RecordingDuration": recording_duration
        }
        result = await agent.handle_voice_webhook("recording", webhook_data)
        
        if not result.success:
            logger.error(f"Recording processing failed: {result.error}")
        
        # 返回200状态码确认收到webhook
        return {"status": "received"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Recording webhook failed: {e}")
        return {"status": "error", "error": str(e)}

@router.post("/webhook/status")
async def handle_call_status_webhook(request: Request):
    """
    处理Twilio通话状态webhook
    """
    try:
        agent = get_agent()

        # 获取Twilio webhook数据
        form_data = await request.form()
        status_data = dict(form_data)

        logger.info(f"Call status webhook: {status_data}")

        # 提取状态信息
        call_sid = status_data.get("CallSid")
        call_status = status_data.get("CallStatus")
        call_duration = status_data.get("CallDuration")

        if not call_sid or not call_status:
            raise HTTPException(status_code=400, detail="Missing required status data")

        # 处理通话状态更新
        webhook_data = {
            "CallSid": call_sid,
            "CallStatus": call_status,
            "CallDuration": call_duration
        }
        result = await agent.handle_voice_webhook("status", webhook_data)

        if not result.success:
            logger.error(f"Call status update failed: {result.error}")

        # Status webhook不需要返回TwiML，只需要200状态码
        return Response(content="", status_code=200)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Call status webhook failed: {e}")
        return Response(content="", status_code=500)

@router.post("/webhook/fallback")
async def handle_fallback_webhook(request: Request):
    """
    处理Twilio fallback webhook - 当主webhook失败时调用
    """
    try:
        logger.warning("Fallback webhook called - main webhook may have failed")

        # 获取Twilio webhook数据
        form_data = await request.form()
        call_data = dict(form_data)

        logger.info(f"Fallback webhook data: {call_data}")

        # 返回简单的错误处理TwiML
        fallback_twiml = '''<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <Say voice="alice">Sorry, we're experiencing technical difficulties. Please try calling again later.</Say>
    <Hangup/>
</Response>'''

        return Response(
            content=fallback_twiml,
            media_type="application/xml"
        )

    except Exception as e:
        logger.error(f"Fallback webhook failed: {e}")
        # 即使fallback失败，也要返回基本的TwiML
        return Response(
            content='<?xml version="1.0" encoding="UTF-8"?><Response><Say>Service unavailable.</Say></Response>',
            media_type="application/xml"
        )

@router.post("/webhook/speech")
async def handle_speech_webhook(request: Request):
    """
    处理Twilio语音输入webhook - 用户语音转文字后的处理
    """
    try:
        agent = get_agent()

        # 获取Twilio webhook数据
        form_data = await request.form()
        speech_data = dict(form_data)

        logger.info(f"Speech webhook: {speech_data}")

        # 提取语音信息
        call_sid = speech_data.get("CallSid")
        speech_result = speech_data.get("SpeechResult", "")
        confidence = float(speech_data.get("Confidence", "1.0"))

        if not call_sid:
            raise HTTPException(status_code=400, detail="Missing CallSid")

        # 处理语音输入
        result = await agent.handle_voice_webhook("speech", {
            "CallSid": call_sid,
            "SpeechResult": speech_result,
            "Confidence": confidence
        })

        if result.success:
            return Response(
                content=result.data["twiml"],
                media_type="application/xml"
            )
        else:
            # 返回错误处理TwiML
            return Response(
                content='<?xml version="1.0" encoding="UTF-8"?><Response><Say>Sorry, there was an error processing your response.</Say><Hangup/></Response>',
                media_type="application/xml"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Speech webhook failed: {e}")
        return Response(
            content='<?xml version="1.0" encoding="UTF-8"?><Response><Say>Sorry, there was an error.</Say><Hangup/></Response>',
            media_type="application/xml"
        )

@router.post("/webhook/timeout")
async def handle_speech_timeout(request: Request):
    """
    处理语音输入超时 - 用户没有在规定时间内回答
    """
    try:
        agent = get_agent()

        # 获取Twilio webhook数据
        form_data = await request.form()
        call_data = dict(form_data)

        logger.info(f"Speech timeout webhook: {call_data}")

        call_sid = call_data.get("CallSid")
        if not call_sid:
            raise HTTPException(status_code=400, detail="Missing CallSid")

        # 处理超时 - 重新问一遍或给提示
        twiml_response = await agent.handle_speech_timeout(call_sid)

        return Response(
            content=twiml_response,
            media_type="application/xml"
        )

    except Exception as e:
        logger.error(f"Speech timeout handling failed: {e}")
        return Response(
            content='<?xml version="1.0" encoding="UTF-8"?><Response><Say>I didn\'t hear you. Let me ask again.</Say><Hangup/></Response>',
            media_type="application/xml"
        )

@router.post("/webhook/outgoing")
async def handle_outgoing_call(request: Request):
    """
    处理Twilio去电webhook - 系统主动拨打用户时的处理
    """
    try:
        agent = get_agent()

        # 获取Twilio webhook数据
        form_data = await request.form()
        call_data = dict(form_data)

        logger.info(f"Outgoing call webhook: {call_data}")

        # 提取关键信息
        call_sid = call_data.get("CallSid")
        to_number = call_data.get("To")  # 被叫号码（用户号码）
        from_number = call_data.get("From")  # 主叫号码（系统号码）
        call_status = call_data.get("CallStatus")

        if not call_sid or not to_number:
            raise HTTPException(status_code=400, detail="Missing required call data")

        # 处理去电 - 注意这里to_number是用户号码
        webhook_data = {
            "CallSid": call_sid,
            "From": from_number,  # 系统号码
            "To": to_number,      # 用户号码
            "CallStatus": call_status
        }
        result = await agent.handle_voice_webhook("outgoing", webhook_data)

        if result.success:
            return Response(
                content=result.data["twiml"],
                media_type="application/xml"
            )
        else:
            # 返回基本的TwiML响应
            return Response(
                content='<?xml version="1.0" encoding="UTF-8"?><Response><Say>Hello! Thank you for answering. Let me start your voice interview.</Say><Hangup/></Response>',
                media_type="application/xml"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Outgoing call webhook failed: {e}")
        # 返回基本的TwiML响应
        return Response(
            content='<?xml version="1.0" encoding="UTF-8"?><Response><Say>Hello! We will call you back shortly.</Say><Hangup/></Response>',
            media_type="application/xml"
        )

@router.get("/analysis/{user_id}", response_model=VoiceAnalysisResponse)
async def get_voice_analysis(user_id: str):
    """
    获取用户语音分析结果
    """
    try:
        agent = get_agent()
        
        # 获取语音分析结果
        result = await agent.get_voice_analysis(user_id)
        
        if not result.success:
            raise HTTPException(status_code=400, detail=result.error)
        
        return VoiceAnalysisResponse(
            success=True,
            data=result.data,
            message="Voice analysis retrieved successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Voice analysis retrieval failed: {e}")
        raise HTTPException(status_code=500, detail="Voice analysis retrieval failed")

@router.post("/retry/{user_id}")
async def retry_voice_interview(user_id: str):
    """
    重新开始语音面试
    """
    try:
        agent = get_agent()
        
        # 重置语音状态
        result = await agent.retry_voice_interview(user_id)
        
        if not result.success:
            raise HTTPException(status_code=400, detail=result.error)
        
        return {
            "success": True,
            "data": result.data,
            "message": "Voice interview reset successfully. You can now start a new interview."
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Voice interview retry failed: {e}")
        raise HTTPException(status_code=500, detail="Voice interview retry failed")
