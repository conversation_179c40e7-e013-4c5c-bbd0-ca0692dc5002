"""
WebSocket 测试端点
用于验证 WebSocket 基础功能
"""

import logging
import json
import asyncio
from datetime import datetime
from fastapi import APIRouter, WebSocket, WebSocketDisconnect
from typing import Dict, List

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/ws-test", tags=["WebSocket Test"])

class TestConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.connection_count = 0
    
    async def connect(self, websocket: WebSocket, client_id: str = None):
        """接受WebSocket连接"""
        await websocket.accept()
        
        if not client_id:
            client_id = f"client_{self.connection_count}"
            self.connection_count += 1
            
        self.active_connections[client_id] = websocket
        logger.info(f"WebSocket connected: {client_id}")
        
        # 发送欢迎消息
        welcome_msg = {
            "type": "welcome",
            "client_id": client_id,
            "message": "WebSocket connection established successfully!",
            "timestamp": datetime.now().isoformat()
        }
        await websocket.send_text(json.dumps(welcome_msg))
        
        return client_id
    
    def disconnect(self, client_id: str):
        """断开WebSocket连接"""
        if client_id in self.active_connections:
            del self.active_connections[client_id]
        logger.info(f"WebSocket disconnected: {client_id}")
    
    async def send_personal_message(self, message: str, client_id: str):
        """发送个人消息"""
        if client_id in self.active_connections:
            websocket = self.active_connections[client_id]
            try:
                await websocket.send_text(message)
                return True
            except Exception as e:
                logger.error(f"Failed to send message to {client_id}: {e}")
                return False
        return False
    
    async def broadcast(self, message: str):
        """广播消息给所有连接"""
        disconnected = []
        for client_id, websocket in self.active_connections.items():
            try:
                await websocket.send_text(message)
            except Exception as e:
                logger.error(f"Failed to broadcast to {client_id}: {e}")
                disconnected.append(client_id)
        
        # 清理断开的连接
        for client_id in disconnected:
            self.disconnect(client_id)
    
    def get_connection_info(self):
        """获取连接信息"""
        return {
            "active_connections": len(self.active_connections),
            "client_ids": list(self.active_connections.keys())
        }

# 全局连接管理器
test_manager = TestConnectionManager()

@router.websocket("/basic")
async def websocket_basic_test(websocket: WebSocket):
    """
    基础 WebSocket 测试端点
    测试连接、消息发送接收、断开等基本功能
    """
    client_id = None
    
    try:
        # 建立连接
        client_id = await test_manager.connect(websocket)
        
        # 消息循环
        async for message in websocket.iter_text():
            try:
                # 解析消息
                data = json.loads(message)
                msg_type = data.get("type", "unknown")
                
                logger.info(f"Received from {client_id}: {msg_type}")
                
                if msg_type == "ping":
                    # 心跳测试
                    response = {
                        "type": "pong",
                        "timestamp": datetime.now().isoformat(),
                        "client_id": client_id
                    }
                    await websocket.send_text(json.dumps(response))
                
                elif msg_type == "echo":
                    # 回声测试
                    response = {
                        "type": "echo_response",
                        "original_message": data.get("message", ""),
                        "timestamp": datetime.now().isoformat(),
                        "client_id": client_id
                    }
                    await websocket.send_text(json.dumps(response))
                
                elif msg_type == "broadcast":
                    # 广播测试
                    broadcast_msg = {
                        "type": "broadcast_message",
                        "from": client_id,
                        "message": data.get("message", ""),
                        "timestamp": datetime.now().isoformat()
                    }
                    await test_manager.broadcast(json.dumps(broadcast_msg))
                
                elif msg_type == "status":
                    # 状态查询
                    status = test_manager.get_connection_info()
                    response = {
                        "type": "status_response",
                        "status": status,
                        "timestamp": datetime.now().isoformat()
                    }
                    await websocket.send_text(json.dumps(response))
                
                else:
                    # 未知消息类型
                    response = {
                        "type": "error",
                        "message": f"Unknown message type: {msg_type}",
                        "timestamp": datetime.now().isoformat()
                    }
                    await websocket.send_text(json.dumps(response))
                    
            except json.JSONDecodeError:
                # JSON 解析错误
                error_response = {
                    "type": "error",
                    "message": "Invalid JSON format",
                    "timestamp": datetime.now().isoformat()
                }
                await websocket.send_text(json.dumps(error_response))
            
            except Exception as e:
                logger.error(f"Error processing message from {client_id}: {e}")
                error_response = {
                    "type": "error",
                    "message": f"Error processing message: {str(e)}",
                    "timestamp": datetime.now().isoformat()
                }
                await websocket.send_text(json.dumps(error_response))
                
    except WebSocketDisconnect:
        logger.info(f"WebSocket disconnected: {client_id}")
    except Exception as e:
        logger.error(f"WebSocket error for {client_id}: {e}")
    finally:
        if client_id:
            test_manager.disconnect(client_id)

@router.websocket("/conversation-relay-mock")
async def websocket_conversation_relay_mock(websocket: WebSocket):
    """
    模拟 ConversationRelay 的 WebSocket 交互
    用于测试 ConversationRelay 消息格式
    """
    session_id = None
    
    try:
        await websocket.accept()
        logger.info("ConversationRelay mock WebSocket connected")
        
        async for message in websocket.iter_text():
            try:
                data = json.loads(message)
                msg_type = data.get("type")
                
                logger.info(f"ConversationRelay mock received: {msg_type}")
                logger.debug(f"Message data: {data}")
                
                if msg_type == "setup":
                    # 模拟 setup 消息处理
                    session_id = data.get("sessionId", "mock_session_123")
                    call_sid = data.get("callSid", "mock_call_456")
                    
                    logger.info(f"Mock setup: session={session_id}, call={call_sid}")
                    
                    # 发送欢迎消息
                    welcome_response = {
                        "type": "text",
                        "token": "Hello! This is a mock ConversationRelay test. How are you today?"
                    }
                    await websocket.send_text(json.dumps(welcome_response))
                
                elif msg_type == "prompt":
                    # 模拟用户输入处理
                    user_text = data.get("text", "")
                    confidence = data.get("confidence", 1.0)
                    
                    logger.info(f"Mock user input: '{user_text}' (confidence: {confidence})")
                    
                    # 立即确认
                    ack_response = {
                        "type": "text",
                        "token": "I see..."
                    }
                    await websocket.send_text(json.dumps(ack_response))
                    
                    # 模拟处理延迟
                    await asyncio.sleep(1)
                    
                    # 发送回复
                    if "hello" in user_text.lower():
                        response_text = "Hello! Nice to meet you. What's your name?"
                    elif "name" in user_text.lower():
                        response_text = "That's a lovely name! Tell me about your hobbies."
                    elif "hobby" in user_text.lower() or "like" in user_text.lower():
                        response_text = "That sounds interesting! What else do you enjoy doing?"
                    else:
                        response_text = "Thank you for sharing that. Can you tell me more?"
                    
                    main_response = {
                        "type": "text",
                        "token": response_text
                    }
                    await websocket.send_text(json.dumps(main_response))
                
                elif msg_type == "interrupt":
                    # 模拟中断处理
                    logger.info("Mock interrupt received")
                    interrupt_response = {
                        "type": "text",
                        "token": "Yes? What would you like to say?"
                    }
                    await websocket.send_text(json.dumps(interrupt_response))
                
                elif msg_type == "dtmf":
                    # 模拟 DTMF 处理
                    digit = data.get("digit")
                    logger.info(f"Mock DTMF: {digit}")
                    
                    dtmf_response = {
                        "type": "text",
                        "token": f"You pressed {digit}. Thank you!"
                    }
                    await websocket.send_text(json.dumps(dtmf_response))
                
                else:
                    logger.warning(f"Unknown mock message type: {msg_type}")
                    
            except json.JSONDecodeError as e:
                logger.error(f"Mock JSON decode error: {e}")
            except Exception as e:
                logger.error(f"Mock processing error: {e}")
                
    except WebSocketDisconnect:
        logger.info("ConversationRelay mock WebSocket disconnected")
    except Exception as e:
        logger.error(f"ConversationRelay mock WebSocket error: {e}")

# HTTP 端点用于获取 WebSocket 状态
@router.get("/status")
async def get_websocket_status():
    """获取 WebSocket 连接状态"""
    return {
        "websocket_test_status": "active",
        "connections": test_manager.get_connection_info(),
        "endpoints": {
            "basic_test": "/ws-test/basic",
            "conversation_relay_mock": "/ws-test/conversation-relay-mock"
        }
    }

@router.get("/test-page")
async def get_test_page():
    """返回简单的 WebSocket 测试页面 HTML"""
    html_content = """
<!DOCTYPE html>
<html>
<head>
    <title>WebSocket Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        button { margin: 5px; padding: 10px; }
        #messages { height: 300px; overflow-y: scroll; border: 1px solid #ccc; padding: 10px; }
        input[type="text"] { width: 300px; padding: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebSocket Test Page</h1>
        
        <div class="section">
            <h3>Connection Status</h3>
            <p>Status: <span id="status">Disconnected</span></p>
            <button onclick="connect()">Connect</button>
            <button onclick="disconnect()">Disconnect</button>
        </div>
        
        <div class="section">
            <h3>Test Messages</h3>
            <button onclick="sendPing()">Send Ping</button>
            <button onclick="sendStatus()">Get Status</button>
            <br>
            <input type="text" id="echoInput" placeholder="Enter message to echo">
            <button onclick="sendEcho()">Send Echo</button>
            <br>
            <input type="text" id="broadcastInput" placeholder="Enter broadcast message">
            <button onclick="sendBroadcast()">Send Broadcast</button>
        </div>
        
        <div class="section">
            <h3>Messages</h3>
            <div id="messages"></div>
            <button onclick="clearMessages()">Clear Messages</button>
        </div>
    </div>

    <script>
        let ws = null;
        
        function connect() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws-test/basic`;
            
            ws = new WebSocket(wsUrl);
            
            ws.onopen = function(event) {
                updateStatus('Connected');
                addMessage('Connected to WebSocket');
            };
            
            ws.onmessage = function(event) {
                const data = JSON.parse(event.data);
                addMessage(`Received: ${JSON.stringify(data, null, 2)}`);
            };
            
            ws.onclose = function(event) {
                updateStatus('Disconnected');
                addMessage('WebSocket connection closed');
            };
            
            ws.onerror = function(error) {
                addMessage(`WebSocket error: ${error}`);
            };
        }
        
        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
            }
        }
        
        function sendMessage(message) {
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify(message));
                addMessage(`Sent: ${JSON.stringify(message, null, 2)}`);
            } else {
                addMessage('WebSocket not connected');
            }
        }
        
        function sendPing() {
            sendMessage({type: 'ping'});
        }
        
        function sendStatus() {
            sendMessage({type: 'status'});
        }
        
        function sendEcho() {
            const input = document.getElementById('echoInput');
            sendMessage({type: 'echo', message: input.value});
            input.value = '';
        }
        
        function sendBroadcast() {
            const input = document.getElementById('broadcastInput');
            sendMessage({type: 'broadcast', message: input.value});
            input.value = '';
        }
        
        function updateStatus(status) {
            document.getElementById('status').textContent = status;
        }
        
        function addMessage(message) {
            const messagesDiv = document.getElementById('messages');
            const messageElement = document.createElement('div');
            messageElement.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            messagesDiv.appendChild(messageElement);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }
        
        function clearMessages() {
            document.getElementById('messages').innerHTML = '';
        }
    </script>
</body>
</html>
    """
    
    from fastapi.responses import HTMLResponse
    return HTMLResponse(content=html_content)
