"""
匹配推荐API
智能匹配、反馈处理、匹配历史功能
"""

import logging
from fastapi import APIRouter, HTTPException, Depends, Query
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from pydantic import BaseModel
import sys
import os

# 添加项目根路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
sys.path.insert(0, project_root)

from models.api_models import (
    MatchRecommendationsResponse, MatchFeedbackResponse,
    MatchHistoryResponse, ErrorResponse
)
from middleware.auth import get_current_user

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/matches", tags=["Matches"])
security = HTTPBearer()

class MatchFeedbackRequest(BaseModel):
    """匹配反馈请求模型"""
    match_id: str
    action: str  # "interested" 或 "passed"

def get_agent():
    """获取Agent实例"""
    try:
        from main import agent_instance
        if agent_instance is None:
            raise HTTPException(status_code=503, detail="Agent service not available")
        return agent_instance
    except ImportError:
        raise HTTPException(status_code=503, detail="Agent service not initialized")

@router.get("/recommendations", response_model=MatchRecommendationsResponse)
async def get_match_recommendations(
    current_user: dict = Depends(get_current_user),
    limit: int = Query(default=10, ge=1, le=50, description="Number of matches to return")
):
    """
    获取匹配推荐
    需要完成完整验证流程
    """
    try:
        agent = get_agent()
        user_id = current_user.get("user_id")
        
        # 验证用户存在
        user = await agent.user_manager.get_user(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # 检查是否完成语音面试
        if not user.voice_call_completed:
            raise HTTPException(
                status_code=400, 
                detail="Voice interview must be completed before accessing matches"
            )
        
        # 检查是否完成LinkedIn验证
        if not user.linkedin_verified:
            raise HTTPException(
                status_code=400, 
                detail="LinkedIn verification must be completed before accessing matches"
            )
        
        # 获取匹配推荐
        result = await agent.get_match_recommendations(user_id, limit=limit)
        
        if not result.success:
            raise HTTPException(status_code=400, detail=result.error)
        
        return MatchRecommendationsResponse(
            success=True,
            data=result.data,
            message="Match recommendations retrieved successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Match recommendations retrieval failed: {e}")
        raise HTTPException(status_code=500, detail="Match recommendations retrieval failed")

@router.post("/feedback", response_model=MatchFeedbackResponse)
async def submit_match_feedback(
    feedback: MatchFeedbackRequest,
    current_user: dict = Depends(get_current_user)
):
    """
    提交匹配反馈
    action: "interested" 表示感兴趣, "passed" 表示跳过
    """
    try:
        agent = get_agent()
        user_id = current_user.get("user_id")
        
        # 验证action参数
        if feedback.action not in ["interested", "passed"]:
            raise HTTPException(
                status_code=400, 
                detail="Invalid action. Must be 'interested' or 'passed'"
            )
        
        # 验证用户存在
        user = await agent.user_manager.get_user(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # 检查是否完成完整验证
        if not user.voice_call_completed or not user.linkedin_verified:
            raise HTTPException(
                status_code=400, 
                detail="Complete verification required before submitting feedback"
            )
        
        # 提交反馈
        result = await agent.process_match_feedback(user_id, feedback.match_id, feedback.action)
        
        if not result.success:
            raise HTTPException(status_code=400, detail=result.error)
        
        return MatchFeedbackResponse(
            success=True,
            data=result.data,
            message="Match feedback submitted successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Match feedback submission failed: {e}")
        raise HTTPException(status_code=500, detail="Match feedback submission failed")

@router.get("/history", response_model=MatchHistoryResponse)
async def get_match_history(
    current_user: dict = Depends(get_current_user),
    page: int = Query(default=1, ge=1, description="Page number"),
    limit: int = Query(default=20, ge=1, le=100, description="Items per page")
):
    """
    获取匹配历史
    包含所有已处理的匹配记录
    """
    try:
        agent = get_agent()
        user_id = current_user.get("user_id")
        
        # 验证用户存在
        user = await agent.user_manager.get_user(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # 获取匹配历史
        result = await agent.get_match_history(user_id, page=page, limit=limit)
        
        if not result.success:
            raise HTTPException(status_code=400, detail=result.error)
        
        return MatchHistoryResponse(
            success=True,
            data=result.data,
            message="Match history retrieved successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Match history retrieval failed: {e}")
        raise HTTPException(status_code=500, detail="Match history retrieval failed")

@router.get("/mutual")
async def get_mutual_matches(current_user: dict = Depends(get_current_user)):
    """
    获取相互匹配的用户
    双方都表示感兴趣的匹配
    """
    try:
        agent = get_agent()
        user_id = current_user.get("user_id")
        
        # 验证用户存在
        user = await agent.user_manager.get_user(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # 获取相互匹配
        result = await agent.get_mutual_matches(user_id)
        
        if not result.success:
            raise HTTPException(status_code=400, detail=result.error)
        
        return {
            "success": True,
            "data": result.data,
            "message": "Mutual matches retrieved successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Mutual matches retrieval failed: {e}")
        raise HTTPException(status_code=500, detail="Mutual matches retrieval failed")

@router.get("/stats")
async def get_match_stats(current_user: dict = Depends(get_current_user)):
    """
    获取匹配统计信息
    包含匹配数量、成功率等统计数据
    """
    try:
        agent = get_agent()
        user_id = current_user.get("user_id")
        
        # 验证用户存在
        user = await agent.user_manager.get_user(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # 获取匹配统计
        result = await agent.get_match_stats(user_id)
        
        if not result.success:
            raise HTTPException(status_code=400, detail=result.error)
        
        return {
            "success": True,
            "data": result.data,
            "message": "Match statistics retrieved successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Match statistics retrieval failed: {e}")
        raise HTTPException(status_code=500, detail="Match statistics retrieval failed")
