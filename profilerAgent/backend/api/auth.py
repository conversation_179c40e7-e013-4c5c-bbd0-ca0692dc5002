"""
认证API
用户注册、SMS验证、登录功能
"""

import logging
from fastapi import APIRouter, HTTPException, Depends
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import sys
import os

# 添加项目根路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
sys.path.insert(0, project_root)

from models.api_models import (
    RegisterRequest, RegisterResponse,
    VerifySMSRequest, VerifySMSResponse,
    SendSMSRequest, SendSMSResponse,
    LoginRequest, LoginResponse,
    LogoutResponse, UserInfoResponse,
    ErrorResponse
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/auth", tags=["Authentication"])
security = HTTPBearer()

def get_agent():
    """获取Agent实例"""
    try:
        from main import agent_instance
        if agent_instance is None:
            raise HTTPException(status_code=503, detail="Agent service not available")
        return agent_instance
    except ImportError:
        raise HTTPException(status_code=503, detail="Agent service not initialized")

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """验证JWT token并获取当前用户"""
    try:
        agent = get_agent()
        
        # 验证token
        token = credentials.credentials
        user_data = agent.user_manager.verify_access_token(token)
        
        if not user_data:
            raise HTTPException(
                status_code=401,
                detail="Invalid or expired token"
            )
        
        return user_data
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Token verification failed: {e}")
        raise HTTPException(
            status_code=401,
            detail="Invalid authentication credentials"
        )

@router.post("/register", response_model=RegisterResponse)
async def register_user(request: RegisterRequest):
    """
    用户注册
    创建新用户并自动发送SMS验证码
    """
    try:
        agent = get_agent()
        
        # 检查用户是否已存在
        existing_user = await agent.user_manager.get_user_by_phone(request.phone_number)
        if existing_user:
            # 用户已存在，提示登录
            raise HTTPException(
                status_code=409,
                detail={
                    "error": "User already exists",
                    "message": "This phone number is already registered. Please login instead.",
                    "action": "login"
                }
            )
        
        # 创建Agent的注册请求对象
        from agent.utils.data_models import UserRegistrationRequest

        registration_data = UserRegistrationRequest(
            phone_number=request.phone_number,
            first_name=request.first_name,
            last_name=request.last_name
        )

        # 创建新用户
        result = await agent.register_user(registration_data)
        
        if not result.success:
            raise HTTPException(status_code=400, detail=result.error)
        
        user_data = result.data

        # Agent的register_user已经自动发送SMS，检查结果
        if not result.success:
            raise HTTPException(status_code=400, detail=result.error)

        # 注册和SMS发送都成功
        return RegisterResponse(
            success=True,
            data={
                "user_id": user_data["user_id"],
                "phone_number": request.phone_number,
                "verification_required": user_data.get("verification_required", True),
                "sms_sent": True,
                "expires_in": 300,  # 5分钟过期
                "can_resend": True
            },
            message=user_data.get("message", "Registration successful! Please check your SMS for the verification code.")
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Registration failed: {e}")
        raise HTTPException(status_code=500, detail="Registration failed")

@router.post("/verify-sms", response_model=VerifySMSResponse)
async def verify_sms_code(request: VerifySMSRequest):
    """
    验证SMS验证码
    验证成功后返回JWT token
    """
    try:
        agent = get_agent()
        
        # 创建Agent的验证请求对象
        from agent.utils.data_models import SMSVerificationRequest

        verification_data = SMSVerificationRequest(
            phone_number=request.phone_number,
            verification_code=request.verification_code
        )

        # 验证SMS验证码
        result = await agent.verify_sms(verification_data)
        
        if not result.success:
            raise HTTPException(status_code=400, detail=result.error)
        
        user_data = result.data

        # Agent的verify_sms已经生成了token
        return VerifySMSResponse(
            success=True,
            data={
                "verified": True,
                "user_id": user_data["user_id"],
                "access_token": user_data["access_token"],
                "token_type": "bearer",
                "expires_in": 86400,  # 24小时
                "user_status": user_data.get("user_status"),
                "verification_status": user_data.get("verification_status"),
                "next_step": user_data.get("next_step", "voice_interview")
            },
            message=user_data.get("message", "SMS verification successful!")
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"SMS verification failed: {e}")
        raise HTTPException(status_code=500, detail="SMS verification failed")

@router.post("/resend-sms", response_model=SendSMSResponse)
async def resend_sms_code(request: SendSMSRequest):
    """
    重新发送SMS验证码
    """
    try:
        agent = get_agent()
        
        # 检查用户是否存在
        user = await agent.user_manager.get_user_by_phone(request.phone_number)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # 简化版本：直接发送（Agent内部会处理频率限制）
        
        # 发送验证码
        result = await agent.send_sms_verification(request.phone_number)
        
        if not result.success:
            raise HTTPException(status_code=500, detail=result.error)
        
        return SendSMSResponse(
            success=True,
            data={
                "phone_number": request.phone_number,
                "expires_in": 300,
                "attempts_remaining": result.data.get("attempts_remaining", 3)
            },
            message="Verification code sent successfully!"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"SMS resend failed: {e}")
        raise HTTPException(status_code=500, detail="Failed to resend SMS")

@router.post("/login", response_model=LoginResponse)
async def login_user(request: LoginRequest):
    """
    用户登录
    检查用户状态并返回相应的JWT token
    """
    try:
        agent = get_agent()
        
        # 检查用户是否存在
        user = await agent.user_manager.get_user_by_phone(request.phone_number)
        if not user:
            raise HTTPException(status_code=404, detail="User not found. Please register first.")
        
        # 检查用户验证状态
        if user.verification_status.value == "pending":
            # 用户未验证，需要先验证SMS
            raise HTTPException(
                status_code=400,
                detail={
                    "error": "SMS verification required",
                    "message": "Please verify your phone number first.",
                    "action": "verify_sms"
                }
            )
        
        # 生成JWT token
        access_token = agent.user_manager.create_access_token(user.user_id)
        
        # 确定下一步操作
        next_step = "dashboard"
        if user.verification_status.value == "sms_verified":
            next_step = "voice_interview"
        elif user.verification_status.value == "voice_completed":
            next_step = "linkedin_verification"
        elif user.verification_status.value == "fully_verified":
            next_step = "matches"
        
        return LoginResponse(
            success=True,
            data={
                "access_token": access_token,
                "token_type": "bearer",
                "expires_in": 86400,  # 24小时
                "user_status": user.status.value,
                "verification_status": user.verification_status.value,
                "next_step": next_step
            },
            message="Login successful!"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Login failed: {e}")
        raise HTTPException(status_code=500, detail="Login failed")

@router.post("/logout", response_model=LogoutResponse)
async def logout_user(current_user: dict = Depends(get_current_user)):
    """
    用户登出
    使当前JWT token失效
    """
    try:
        agent = get_agent()
        
        # 获取用户ID
        user_id = current_user.get("user_id")
        if not user_id:
            raise HTTPException(status_code=400, detail="Invalid user data")
        
        # 使token失效（调用Agent的logout方法）
        try:
            # 调用Agent的logout_user方法
            result = await agent.logout_user(user_id)
            if not result.success:
                logger.warning(f"Agent logout failed: {result.error}")
        except AttributeError:
            # Agent可能还没有实现logout_user方法，这是正常的
            logger.info("Agent logout_user method not implemented, token will expire naturally")
        
        return LogoutResponse(
            success=True,
            data={
                "logged_out": True,
                "message": "Successfully logged out"
            },
            message="Logout successful"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Logout failed: {e}")
        raise HTTPException(status_code=500, detail="Logout failed")

@router.get("/me", response_model=UserInfoResponse)
async def get_current_user_info(current_user: dict = Depends(get_current_user)):
    """
    获取当前用户信息
    需要有效的JWT token
    """
    try:
        agent = get_agent()
        
        # 获取用户ID
        user_id = current_user.get("user_id")
        if not user_id:
            raise HTTPException(status_code=400, detail="Invalid user data")
        
        # 获取完整用户信息
        user = await agent.user_manager.get_user(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        return UserInfoResponse(
            success=True,
            data={
                "user_id": user.user_id,
                "phone_number": user.phone_number,
                "first_name": user.first_name,
                "last_name": user.last_name,
                "status": user.status.value if hasattr(user.status, 'value') else str(user.status),
                "verification_status": user.verification_status.value if hasattr(user.verification_status, 'value') else str(user.verification_status),
                "created_at": user.created_at.isoformat() if hasattr(user, 'created_at') and user.created_at else None,
                "last_login": user.last_login.isoformat() if hasattr(user, 'last_login') and user.last_login else None
            },
            message="User information retrieved successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get user info failed: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve user information")
