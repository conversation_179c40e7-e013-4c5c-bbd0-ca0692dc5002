"""
用户画像API
画像生成、分析结果、画像卡片功能
"""

import logging
from fastapi import APIRouter, HTTPException, Depends
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import sys
import os

# 添加项目根路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
sys.path.insert(0, project_root)

from models.api_models import (
    ProfileCardsResponse, ProfileGenerateResponse, 
    ErrorResponse, SuccessResponse
)
from middleware.auth import get_current_user

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/profile", tags=["Profile"])
security = HTTPBearer()

def get_agent():
    """获取Agent实例"""
    try:
        from main import agent_instance
        if agent_instance is None:
            raise HTTPException(status_code=503, detail="Agent service not available")
        return agent_instance
    except ImportError:
        raise HTTPException(status_code=503, detail="Agent service not initialized")

@router.get("/cards/{user_id}", response_model=ProfileCardsResponse)
async def get_profile_cards(user_id: str, current_user: dict = Depends(get_current_user)):
    """
    获取用户画像卡片
    需要完成语音面试
    """
    try:
        agent = get_agent()
        
        # 验证用户权限 - 只能访问自己的画像
        if current_user.get("user_id") != user_id:
            raise HTTPException(status_code=403, detail="Access denied")
        
        # 验证用户存在
        user = await agent.user_manager.get_user(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # 检查是否完成语音面试
        if not user.voice_call_completed:
            raise HTTPException(
                status_code=400, 
                detail="Voice interview must be completed before accessing profile cards"
            )
        
        # 获取画像卡片
        result = await agent.get_profile_cards(user_id)
        
        if not result.success:
            raise HTTPException(status_code=400, detail=result.error)
        
        return ProfileCardsResponse(
            success=True,
            data=result.data,
            message="Profile cards retrieved successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Profile cards retrieval failed: {e}")
        raise HTTPException(status_code=500, detail="Profile cards retrieval failed")

@router.post("/generate/{user_id}", response_model=ProfileGenerateResponse)
async def generate_final_profile(user_id: str, current_user: dict = Depends(get_current_user)):
    """
    生成最终用户画像
    需要完成语音面试和LinkedIn验证
    """
    try:
        agent = get_agent()
        
        # 验证用户权限
        if current_user.get("user_id") != user_id:
            raise HTTPException(status_code=403, detail="Access denied")
        
        # 验证用户存在
        user = await agent.user_manager.get_user(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # 检查是否完成语音面试
        if not user.voice_call_completed:
            raise HTTPException(
                status_code=400, 
                detail="Voice interview must be completed before generating final profile"
            )
        
        # 检查是否完成LinkedIn验证
        if not user.linkedin_verified:
            raise HTTPException(
                status_code=400, 
                detail="LinkedIn verification must be completed before generating final profile"
            )
        
        # 生成最终画像
        result = await agent.generate_final_profile(user_id)
        
        if not result.success:
            raise HTTPException(status_code=400, detail=result.error)
        
        return ProfileGenerateResponse(
            success=True,
            data=result.data,
            message="Final profile generated successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Profile generation failed: {e}")
        raise HTTPException(status_code=500, detail="Profile generation failed")

@router.get("/analysis/{user_id}")
async def get_profile_analysis(user_id: str, current_user: dict = Depends(get_current_user)):
    """
    获取用户画像分析详情
    包含MBTI、性格特征、兴趣等详细分析
    """
    try:
        agent = get_agent()
        
        # 验证用户权限
        if current_user.get("user_id") != user_id:
            raise HTTPException(status_code=403, detail="Access denied")
        
        # 验证用户存在
        user = await agent.user_manager.get_user(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # 检查是否完成语音面试
        if not user.voice_call_completed:
            raise HTTPException(
                status_code=400, 
                detail="Voice interview must be completed before accessing analysis"
            )
        
        # 获取分析结果
        result = await agent.get_profile_analysis(user_id)
        
        if not result.success:
            raise HTTPException(status_code=400, detail=result.error)
        
        return {
            "success": True,
            "data": result.data,
            "message": "Profile analysis retrieved successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Profile analysis retrieval failed: {e}")
        raise HTTPException(status_code=500, detail="Profile analysis retrieval failed")

@router.get("/status/{user_id}")
async def get_profile_status(user_id: str, current_user: dict = Depends(get_current_user)):
    """
    获取用户画像生成状态
    """
    try:
        agent = get_agent()
        
        # 验证用户权限
        if current_user.get("user_id") != user_id:
            raise HTTPException(status_code=403, detail="Access denied")
        
        # 验证用户存在
        user = await agent.user_manager.get_user(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # 获取画像状态
        result = await agent.get_profile_status(user_id)
        
        if not result.success:
            raise HTTPException(status_code=400, detail=result.error)
        
        return {
            "success": True,
            "data": result.data,
            "message": "Profile status retrieved successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Profile status check failed: {e}")
        raise HTTPException(status_code=500, detail="Profile status check failed")
