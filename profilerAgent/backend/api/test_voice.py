"""
Test Voice API - 测试语音面试的API端点
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Optional, Dict, Any
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/test-voice", tags=["test-voice"])

# 全局变量存储TestVoiceService实例
test_voice_service = None

def get_test_voice_service():
    """获取TestVoiceService实例"""
    global test_voice_service
    if test_voice_service is None:
        # 延迟导入避免循环依赖
        from agent import get_agent_interface
        agent = get_agent_interface()
        
        # 导入TestVoiceService
        from agent.services.test_voice_service import TestVoiceService
        test_voice_service = TestVoiceService(
            agent.user_manager, 
            agent.analysis_engine
        )
    return test_voice_service

# Request/Response Models
class StartInterviewRequest(BaseModel):
    phone_number: str

class RespondRequest(BaseModel):
    session_id: str
    response: str

class InterviewResponse(BaseModel):
    success: bool
    completed: Optional[bool] = False
    session_id: Optional[str] = None
    current_question: Optional[str] = None
    next_question: Optional[str] = None
    stage: Optional[str] = None
    progress: Optional[Dict[str, Any]] = None
    analysis_result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    message: Optional[str] = None

@router.post("/start", response_model=InterviewResponse)
async def start_interview(request: StartInterviewRequest):
    """开始测试面试"""
    try:
        service = get_test_voice_service()
        result = await service.start_test_interview(request.phone_number)
        
        if result["success"]:
            return InterviewResponse(
                success=True,
                session_id=result["session_id"],
                current_question=result.get("first_question") or result.get("current_question", ""),
                stage="greeting",
                message="Interview started successfully"
            )
        else:
            return InterviewResponse(
                success=False,
                error=result["error"],
                message=result["message"]
            )
            
    except Exception as e:
        logger.error(f"Start interview failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/respond", response_model=InterviewResponse)
async def process_response(request: RespondRequest):
    """处理用户回答"""
    try:
        service = get_test_voice_service()
        result = await service.process_text_input(request.session_id, request.response)
        
        if result["success"]:
            response_data = InterviewResponse(
                success=True,
                completed=result.get("completed", False),
                next_question=result.get("next_question"),
                stage=result.get("current_stage", result.get("stage", "greeting")),
                progress={
                    "stage": result.get("current_stage", result.get("stage", "greeting")),
                    "questions_asked": result.get("questions_asked", 0),
                    "responses_received": result.get("responses_received", 0),
                    "duration": result.get("duration", 0)
                }
            )
            
            # 如果面试完成，添加分析结果
            if result.get("completed") and "analysis_result" in result:
                analysis = result["analysis_result"]
                # 处理personality_traits，确保是数组格式
                traits = analysis.get("personality_traits", [])
                if isinstance(traits, dict):
                    traits = list(traits.keys())
                elif not isinstance(traits, list):
                    traits = []
                
                response_data.analysis_result = {
                    "mbti_type": analysis.get("mbti_type", "UNKNOWN"),
                    "confidence": analysis.get("confidence", 0.0),
                    "personality_traits": traits
                }
            
            return response_data
        else:
            return InterviewResponse(
                success=False,
                error=result["error"],
                message=result.get("message", "Failed to process response")
            )
            
    except Exception as e:
        logger.error(f"Process response failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/reset/{phone_number}")
async def reset_user_status(phone_number: str):
    """重置用户语音面试状态"""
    try:
        # 导入重置脚本的逻辑
        from agent import get_agent_interface
        agent = get_agent_interface()
        
        # 查找用户
        user = await agent.user_manager.get_user_by_phone(phone_number)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # 重置语音状态
        user.reset_voice_call_status()
        await agent.user_manager._save_user_to_db(user)
        
        return {
            "success": True,
            "message": f"Successfully reset voice status for {phone_number}"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Reset user status failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/session/{session_id}")
async def get_session_status(session_id: str):
    """获取会话状态"""
    try:
        service = get_test_voice_service()
        status = service.get_test_session_status(session_id)
        
        if "error" in status:
            raise HTTPException(status_code=404, detail=status["error"])
            
        return {
            "success": True,
            "session": status
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get session status failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/transcript/{session_id}")
async def get_session_transcript(session_id: str):
    """获取会话转录"""
    try:
        service = get_test_voice_service()
        transcript = service.get_session_transcript(session_id)
        
        if transcript == "Session not found":
            raise HTTPException(status_code=404, detail="Session not found")
            
        return {
            "success": True,
            "transcript": transcript
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get session transcript failed: {e}")
        raise HTTPException(status_code=500, detail=str(e)) 