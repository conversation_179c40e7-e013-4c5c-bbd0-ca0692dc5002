"""
LinkedIn OAuth API
LinkedIn 登录和资料验证功能
"""

import logging
from fastapi import APIRouter, HTTPException, Depends, Request
from fastapi.responses import RedirectResponse
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import sys
import os

# 添加项目根路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
sys.path.insert(0, project_root)

from models.api_models import (
    LinkedInAuthResponse, LinkedInCallbackResponse,
    LinkedInProfileResponse, ErrorResponse
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/linkedin", tags=["LinkedIn"])
security = HTTPBearer()

def get_agent():
    """获取Agent实例"""
    try:
        from main import agent_instance
        if agent_instance is None:
            raise HTTPException(status_code=503, detail="Agent service not available")
        return agent_instance
    except ImportError:
        raise HTTPException(status_code=503, detail="Agent service not initialized")

@router.get("/auth", response_model=LinkedInAuthResponse)
async def linkedin_auth(user_id: str):
    """
    开始LinkedIn OAuth流程
    生成LinkedIn授权URL
    """
    try:
        agent = get_agent()
        
        # 验证用户存在
        user = agent.user_manager.get_user(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # 检查用户是否已完成语音验证
        if not user.voice_call_completed:
            raise HTTPException(
                status_code=400, 
                detail="Voice interview must be completed before LinkedIn verification"
            )
        
        # 生成LinkedIn授权URL
        linkedin_service = agent.linkedin_service
        auth_url = linkedin_service.get_authorization_url(user_id)
        
        return LinkedInAuthResponse(
            success=True,
            data={
                "auth_url": auth_url,
                "user_id": user_id,
                "expires_in": 600  # 10分钟内完成授权
            },
            message="Please complete LinkedIn authorization"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"LinkedIn auth failed: {e}")
        raise HTTPException(status_code=500, detail="LinkedIn authorization failed")

@router.get("/callback")
async def linkedin_callback(request: Request):
    """
    LinkedIn OAuth回调处理
    处理LinkedIn返回的授权码
    """
    try:
        agent = get_agent()
        
        # 获取查询参数
        code = request.query_params.get("code")
        state = request.query_params.get("state")  # 包含user_id
        error = request.query_params.get("error")
        
        if error:
            logger.error(f"LinkedIn OAuth error: {error}")
            # 重定向到前端错误页面
            return RedirectResponse(
                url=f"http://localhost:3000/auth/linkedin/error?error={error}",
                status_code=302
            )
        
        if not code or not state:
            raise HTTPException(status_code=400, detail="Missing authorization code or state")
        
        # 从state中提取user_id
        user_id = state  # 简化版本，实际应该解析state参数
        
        # 处理LinkedIn回调
        linkedin_service = agent.linkedin_service
        result = linkedin_service.handle_oauth_callback(code, state)
        
        if not result.success:
            # 重定向到前端错误页面
            return RedirectResponse(
                url=f"http://localhost:3000/auth/linkedin/error?error={result.error}",
                status_code=302
            )
        
        # 成功，重定向到前端成功页面
        return RedirectResponse(
            url=f"http://localhost:3000/auth/linkedin/success?user_id={user_id}",
            status_code=302
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"LinkedIn callback failed: {e}")
        return RedirectResponse(
            url=f"http://localhost:3000/auth/linkedin/error?error=callback_failed",
            status_code=302
        )

@router.get("/status/{user_id}", response_model=LinkedInProfileResponse)
async def get_linkedin_status(user_id: str):
    """
    获取用户LinkedIn验证状态
    """
    try:
        agent = get_agent()
        
        # 获取用户
        user = agent.user_manager.get_user(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # 获取LinkedIn状态
        linkedin_service = agent.linkedin_service
        status_result = linkedin_service.get_verification_status(user_id)
        
        if not status_result.success:
            raise HTTPException(status_code=400, detail=status_result.error)
        
        return LinkedInProfileResponse(
            success=True,
            data=status_result.data,
            message="LinkedIn status retrieved successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"LinkedIn status check failed: {e}")
        raise HTTPException(status_code=500, detail="LinkedIn status check failed")

@router.post("/verify/{user_id}")
async def verify_linkedin_profile(user_id: str, linkedin_url: str):
    """
    手动提交LinkedIn资料URL进行验证
    备用方案，如果OAuth失败
    """
    try:
        agent = get_agent()
        
        # 验证用户存在
        user = agent.user_manager.get_user(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # 提交LinkedIn资料
        result = agent.submit_linkedin_profile(user_id, linkedin_url)
        
        if not result.success:
            raise HTTPException(status_code=400, detail=result.error)
        
        return {
            "success": True,
            "data": result.data,
            "message": result.data.get("message", "LinkedIn profile submitted successfully")
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"LinkedIn profile verification failed: {e}")
        raise HTTPException(status_code=500, detail="LinkedIn profile verification failed")

@router.delete("/disconnect/{user_id}")
async def disconnect_linkedin(user_id: str):
    """
    断开LinkedIn连接
    """
    try:
        agent = get_agent()
        
        # 验证用户存在
        user = agent.user_manager.get_user(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # 断开LinkedIn连接
        linkedin_service = agent.linkedin_service
        result = linkedin_service.disconnect_user(user_id)
        
        if not result.success:
            raise HTTPException(status_code=400, detail=result.error)
        
        return {
            "success": True,
            "message": "LinkedIn account disconnected successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"LinkedIn disconnect failed: {e}")
        raise HTTPException(status_code=500, detail="LinkedIn disconnect failed")

@router.get("/profile/{user_id}")
async def get_linkedin_profile(user_id: str):
    """
    获取用户的LinkedIn资料信息
    """
    try:
        agent = get_agent()
        
        # 验证用户存在
        user = await agent.user_manager.get_user(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # 检查LinkedIn是否已验证
        if not user.linkedin_verified:
            raise HTTPException(status_code=400, detail="LinkedIn not verified")
        
        # 获取LinkedIn资料
        linkedin_service = agent.linkedin_service
        profile_result = linkedin_service.get_user_profile(user_id)
        
        if not profile_result.success:
            raise HTTPException(status_code=400, detail=profile_result.error)
        
        return {
            "success": True,
            "data": profile_result.data,
            "message": "LinkedIn profile retrieved successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"LinkedIn profile retrieval failed: {e}")
        raise HTTPException(status_code=500, detail="LinkedIn profile retrieval failed")
