"""
健康检查API
检查系统各组件状态
"""

import logging
from fastapi import APIRouter, HTTPException
from datetime import datetime
import sys
import os

# 添加项目根路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
sys.path.insert(0, project_root)

from models.api_models import HealthCheckResponse
from database.connection import get_connection_status

logger = logging.getLogger(__name__)

router = APIRouter(tags=["Health"])

@router.get("/health")
async def health_check():
    """
    系统健康检查
    检查数据库、Redis、Agent等组件状态
    """
    try:
        from datetime import datetime as dt

        # 获取连接状态
        connection_status = await get_connection_status()

        # 检查Agent状态
        agent_status = await check_agent_status()

        # 判断整体健康状态
        overall_status = "healthy"

        # 检查关键服务
        if connection_status["database"]["status"] != "connected":
            overall_status = "unhealthy"

        if connection_status["redis"]["status"] != "connected":
            overall_status = "unhealthy"

        if not agent_status["available"]:
            overall_status = "unhealthy"

        return {
            "status": overall_status,
            "version": "1.0.0-mvp",
            "timestamp": dt.now().isoformat(),
            "services": {
                "database": connection_status["database"],
                "redis": connection_status["redis"],
                "api": {"status": "running"}
            },
            "agent_status": agent_status
        }

    except Exception as e:
        logger.error(f"Health check failed: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Health check failed: {str(e)}")

@router.get("/health/database")
async def database_health():
    """数据库健康检查"""
    try:
        connection_status = await get_connection_status()
        return connection_status["database"]
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        raise HTTPException(status_code=500, detail="Database health check failed")

@router.get("/health/redis")
async def redis_health():
    """Redis健康检查"""
    try:
        connection_status = await get_connection_status()
        return connection_status["redis"]
    except Exception as e:
        logger.error(f"Redis health check failed: {e}")
        raise HTTPException(status_code=500, detail="Redis health check failed")

@router.get("/health/agent")
async def agent_health():
    """Agent健康检查"""
    try:
        agent_status = await check_agent_status()
        return agent_status
    except Exception as e:
        logger.error(f"Agent health check failed: {e}")
        raise HTTPException(status_code=500, detail="Agent health check failed")

async def check_agent_status() -> dict:
    """检查Agent状态"""
    try:
        from datetime import datetime as dt
        # 这里会在main.py中的agent实例创建后更新
        from main import agent_instance
        
        if agent_instance is None:
            return {
                "available": False,
                "error": "Agent not initialized"
            }
        
        # 调用Agent的健康检查
        health_status = agent_instance.get_health_status()

        # 获取系统状态（使用新的工厂模式）
        try:
            from agent import get_system_status
            system_status = get_system_status()
        except:
            system_status = {"status": "unknown"}

        return {
            "available": health_status.success,
            "services": health_status.data if health_status.success else None,
            "system_status": system_status,
            "error": health_status.error if not health_status.success else None,
            "last_check": dt.now().isoformat()
        }
        
    except ImportError:
        # Agent还未初始化
        return {
            "available": False,
            "error": "Agent not yet initialized"
        }
    except Exception as e:
        logger.error(f"Agent status check failed: {e}")
        return {
            "available": False,
            "error": str(e)
        }
