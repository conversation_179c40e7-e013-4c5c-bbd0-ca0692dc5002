"""
用户管理API
用户信息、偏好设置、状态管理功能
"""

import logging
from fastapi import APIRouter, HTTPException, Depends
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel
from typing import Optional, Dict, Any
import sys
import os

# 添加项目根路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
sys.path.insert(0, project_root)

from models.api_models import (
    UserProfileResponse, UserPreferencesResponse,
    ErrorResponse, SuccessResponse
)
from middleware.auth import get_current_user

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/users", tags=["Users"])
security = HTTPBearer()

class UserProfileUpdateRequest(BaseModel):
    """用户信息更新请求模型"""
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    age: Optional[int] = None
    city: Optional[str] = None
    profession: Optional[str] = None
    bio: Optional[str] = None

class UserPreferencesUpdateRequest(BaseModel):
    """用户偏好更新请求模型"""
    age_range_min: Optional[int] = None
    age_range_max: Optional[int] = None
    max_distance: Optional[int] = None
    preferred_mbti_types: Optional[list] = None
    deal_breakers: Optional[list] = None
    interests: Optional[list] = None

def get_agent():
    """获取Agent实例"""
    try:
        from main import agent_instance
        if agent_instance is None:
            raise HTTPException(status_code=503, detail="Agent service not available")
        return agent_instance
    except ImportError:
        raise HTTPException(status_code=503, detail="Agent service not initialized")

@router.get("/profile", response_model=UserProfileResponse)
async def get_user_profile(current_user: dict = Depends(get_current_user)):
    """
    获取当前用户基础信息
    """
    try:
        agent = get_agent()
        user_id = current_user.get("user_id")
        
        # 获取用户信息
        user = await agent.user_manager.get_user(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # 构建响应数据
        profile_data = {
            "user_id": user.user_id,
            "phone_number": user.phone_number,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "age": user.age,
            "city": user.city,
            "profession": user.profession,
            "bio": user.bio,
            "status": user.status.value,
            "verification_status": user.verification_status.value,
            "sms_verified": user.sms_verified,
            "voice_call_completed": user.voice_call_completed,
            "linkedin_verified": user.linkedin_verified,
            "created_at": user.created_at.isoformat(),
            "last_login": user.last_login.isoformat() if user.last_login else None
        }
        
        return UserProfileResponse(
            success=True,
            data=profile_data,
            message="User profile retrieved successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"User profile retrieval failed: {e}")
        raise HTTPException(status_code=500, detail="User profile retrieval failed")

@router.put("/profile")
async def update_user_profile(
    profile_update: UserProfileUpdateRequest,
    current_user: dict = Depends(get_current_user)
):
    """
    更新用户基础信息
    """
    try:
        agent = get_agent()
        user_id = current_user.get("user_id")
        
        # 获取用户信息
        user = await agent.user_manager.get_user(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # 更新用户信息
        update_data = profile_update.dict(exclude_unset=True)
        result = await agent.update_user_profile(user_id, update_data)
        
        if not result.success:
            raise HTTPException(status_code=400, detail=result.error)
        
        return {
            "success": True,
            "data": result.data,
            "message": "User profile updated successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"User profile update failed: {e}")
        raise HTTPException(status_code=500, detail="User profile update failed")

@router.get("/preferences", response_model=UserPreferencesResponse)
async def get_user_preferences(current_user: dict = Depends(get_current_user)):
    """
    获取用户偏好设置
    """
    try:
        agent = get_agent()
        user_id = current_user.get("user_id")
        
        # 获取用户偏好
        result = await agent.get_user_preferences(user_id)
        
        if not result.success:
            raise HTTPException(status_code=400, detail=result.error)
        
        return UserPreferencesResponse(
            success=True,
            data=result.data,
            message="User preferences retrieved successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"User preferences retrieval failed: {e}")
        raise HTTPException(status_code=500, detail="User preferences retrieval failed")

@router.put("/preferences")
async def update_user_preferences(
    preferences_update: UserPreferencesUpdateRequest,
    current_user: dict = Depends(get_current_user)
):
    """
    更新用户偏好设置
    """
    try:
        agent = get_agent()
        user_id = current_user.get("user_id")
        
        # 验证用户存在
        user = await agent.user_manager.get_user(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # 更新偏好设置
        preferences_data = preferences_update.dict(exclude_unset=True)
        result = await agent.update_user_preferences(user_id, preferences_data)
        
        if not result.success:
            raise HTTPException(status_code=400, detail=result.error)
        
        return {
            "success": True,
            "data": result.data,
            "message": "User preferences updated successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"User preferences update failed: {e}")
        raise HTTPException(status_code=500, detail="User preferences update failed")

@router.get("/status")
async def get_user_status(current_user: dict = Depends(get_current_user)):
    """
    获取用户状态详情
    包含验证状态、权限等信息
    """
    try:
        agent = get_agent()
        user_id = current_user.get("user_id")
        
        # 获取用户状态
        result = await agent.get_user_status_details(user_id)
        
        if not result.success:
            raise HTTPException(status_code=400, detail=result.error)
        
        return {
            "success": True,
            "data": result.data,
            "message": "User status retrieved successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"User status retrieval failed: {e}")
        raise HTTPException(status_code=500, detail="User status retrieval failed")

@router.delete("/account")
async def delete_user_account(current_user: dict = Depends(get_current_user)):
    """
    删除用户账户
    软删除，保留数据但停用账户
    """
    try:
        agent = get_agent()
        user_id = current_user.get("user_id")
        
        # 删除用户账户
        result = await agent.delete_user_account(user_id)
        
        if not result.success:
            raise HTTPException(status_code=400, detail=result.error)
        
        return {
            "success": True,
            "data": result.data,
            "message": "User account deleted successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"User account deletion failed: {e}")
        raise HTTPException(status_code=500, detail="User account deletion failed")
