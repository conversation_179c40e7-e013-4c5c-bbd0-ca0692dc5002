"""
AI Agent专用Redis缓存管理器
用于管理用户短期记忆、对话状态、AI响应缓存等
"""

import asyncio
import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import redis.asyncio as redis

logger = logging.getLogger(__name__)

class AIAgentCache:
    """AI Agent专用缓存管理器"""
    
    # Redis数据库分区
    DB_USER_CONTEXT = 0      # 用户短期记忆缓存
    DB_CONVERSATION = 1      # 对话会话状态
    DB_AI_CACHE = 2         # AI模型响应缓存
    DB_PROFILE_CACHE = 3    # 用户画像缓存
    DB_MATCH_CACHE = 4      # 匹配算法缓存
    
    # 缓存过期时间
    EXPIRE_SHORT_TERM = 3600        # 1小时 - 短期记忆
    EXPIRE_CONVERSATION = 86400     # 24小时 - 对话状态
    EXPIRE_AI_CACHE = 604800       # 7天 - AI响应缓存
    EXPIRE_PROFILE = 3600          # 1小时 - 用户画像缓存
    EXPIRE_MATCH = 1800            # 30分钟 - 匹配缓存
    
    def __init__(self, redis_url: str = "redis://localhost:6380"):
        self.redis_url = redis_url
        self._connections = {}
    
    async def _get_connection(self, db: int) -> redis.Redis:
        """获取指定数据库的Redis连接"""
        if db not in self._connections:
            self._connections[db] = redis.from_url(
                self.redis_url,
                db=db,
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5
            )
        return self._connections[db]
    
    # ============ 用户短期记忆管理 ============
    
    async def get_user_context(self, user_id: str) -> Dict[str, Any]:
        """获取用户短期记忆上下文"""
        try:
            conn = await self._get_connection(self.DB_USER_CONTEXT)
            key = f"user_context:{user_id}"
            
            data = await conn.hgetall(key)
            if not data:
                return {
                    "short_term_memory": [],
                    "conversation_summary": "",
                    "active_conversation_id": None,
                    "last_updated": None
                }
            
            # 解析JSON字段
            context = {
                "short_term_memory": json.loads(data.get("short_term_memory", "[]")),
                "conversation_summary": data.get("conversation_summary", ""),
                "active_conversation_id": data.get("active_conversation_id"),
                "last_updated": data.get("last_updated")
            }
            
            return context
            
        except Exception as e:
            logger.error(f"Failed to get user context for {user_id}: {e}")
            return {"short_term_memory": [], "conversation_summary": "", "active_conversation_id": None}
    
    async def update_user_context(self, user_id: str, context: Dict[str, Any]) -> bool:
        """更新用户短期记忆上下文"""
        try:
            conn = await self._get_connection(self.DB_USER_CONTEXT)
            key = f"user_context:{user_id}"
            
            # 准备数据
            data = {
                "short_term_memory": json.dumps(context.get("short_term_memory", [])),
                "conversation_summary": context.get("conversation_summary", ""),
                "active_conversation_id": context.get("active_conversation_id", ""),
                "last_updated": datetime.now().isoformat()
            }
            
            # 批量设置
            await conn.hset(key, mapping=data)
            await conn.expire(key, self.EXPIRE_SHORT_TERM)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to update user context for {user_id}: {e}")
            return False
    
    async def add_to_short_term_memory(self, user_id: str, role: str, content: str, max_messages: int = 10) -> bool:
        """添加消息到短期记忆"""
        try:
            context = await self.get_user_context(user_id)
            memory = context["short_term_memory"]
            
            # 添加新消息
            memory.append({
                "role": role,
                "content": content,
                "timestamp": datetime.now().isoformat()
            })
            
            # 保持最大消息数量
            if len(memory) > max_messages:
                memory = memory[-max_messages:]
            
            context["short_term_memory"] = memory
            return await self.update_user_context(user_id, context)
            
        except Exception as e:
            logger.error(f"Failed to add to short term memory for {user_id}: {e}")
            return False
    
    # ============ 对话状态管理 ============
    
    async def set_conversation_state(self, conversation_id: str, state: Dict[str, Any]) -> bool:
        """设置对话状态"""
        try:
            conn = await self._get_connection(self.DB_CONVERSATION)
            key = f"conversation:{conversation_id}"
            
            await conn.set(key, json.dumps(state), ex=self.EXPIRE_CONVERSATION)
            return True
            
        except Exception as e:
            logger.error(f"Failed to set conversation state for {conversation_id}: {e}")
            return False
    
    async def get_conversation_state(self, conversation_id: str) -> Optional[Dict[str, Any]]:
        """获取对话状态"""
        try:
            conn = await self._get_connection(self.DB_CONVERSATION)
            key = f"conversation:{conversation_id}"
            
            data = await conn.get(key)
            return json.loads(data) if data else None
            
        except Exception as e:
            logger.error(f"Failed to get conversation state for {conversation_id}: {e}")
            return None
    
    # ============ AI响应缓存 ============
    
    async def cache_ai_response(self, prompt_hash: str, response: Dict[str, Any]) -> bool:
        """缓存AI响应"""
        try:
            conn = await self._get_connection(self.DB_AI_CACHE)
            key = f"ai_cache:{prompt_hash}"
            
            await conn.set(key, json.dumps(response), ex=self.EXPIRE_AI_CACHE)
            return True
            
        except Exception as e:
            logger.error(f"Failed to cache AI response for {prompt_hash}: {e}")
            return False
    
    async def get_cached_ai_response(self, prompt_hash: str) -> Optional[Dict[str, Any]]:
        """获取缓存的AI响应"""
        try:
            conn = await self._get_connection(self.DB_AI_CACHE)
            key = f"ai_cache:{prompt_hash}"
            
            data = await conn.get(key)
            return json.loads(data) if data else None
            
        except Exception as e:
            logger.error(f"Failed to get cached AI response for {prompt_hash}: {e}")
            return None
    
    # ============ 用户画像缓存 ============
    
    async def cache_user_profile(self, user_id: str, profile: Dict[str, Any]) -> bool:
        """缓存用户画像"""
        try:
            conn = await self._get_connection(self.DB_PROFILE_CACHE)
            key = f"profile:{user_id}"
            
            await conn.set(key, json.dumps(profile), ex=self.EXPIRE_PROFILE)
            return True
            
        except Exception as e:
            logger.error(f"Failed to cache user profile for {user_id}: {e}")
            return False
    
    async def get_cached_user_profile(self, user_id: str) -> Optional[Dict[str, Any]]:
        """获取缓存的用户画像"""
        try:
            conn = await self._get_connection(self.DB_PROFILE_CACHE)
            key = f"profile:{user_id}"
            
            data = await conn.get(key)
            return json.loads(data) if data else None
            
        except Exception as e:
            logger.error(f"Failed to get cached user profile for {user_id}: {e}")
            return None
    
    # ============ 清理方法 ============
    
    async def clear_user_data(self, user_id: str) -> bool:
        """清理用户相关的所有缓存数据"""
        try:
            # 清理各个数据库中的用户数据
            tasks = []
            
            # 用户上下文
            conn_context = await self._get_connection(self.DB_USER_CONTEXT)
            tasks.append(conn_context.delete(f"user_context:{user_id}"))
            
            # 用户画像缓存
            conn_profile = await self._get_connection(self.DB_PROFILE_CACHE)
            tasks.append(conn_profile.delete(f"profile:{user_id}"))
            
            # 匹配缓存
            conn_match = await self._get_connection(self.DB_MATCH_CACHE)
            tasks.append(conn_match.delete(f"matches:{user_id}"))
            
            # 执行所有清理任务
            await asyncio.gather(*tasks, return_exceptions=True)
            
            logger.info(f"Cleared cache data for user {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to clear user data for {user_id}: {e}")
            return False
    
    async def close(self):
        """关闭所有Redis连接"""
        for conn in self._connections.values():
            await conn.close()
        self._connections.clear()
