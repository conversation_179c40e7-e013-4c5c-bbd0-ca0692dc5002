"""
Configuration module for AI Dating Agent MVP
Contains all configuration settings
"""

from .core_config import CoreConfig
from .service_config import ServiceConfig
from .templates import Templates

__all__ = [
    'CoreConfig',
    'ServiceConfig',
    'Templates'
]

# 便捷函数
def validate_all_configs() -> bool:
    """验证所有配置是否完整"""
    try:
        # 验证核心配置
        CoreConfig.validate_config()

        # 验证服务配置
        if not ServiceConfig.validate_twilio_sms_config():
            raise ValueError("Twilio SMS configuration is incomplete")

        if not ServiceConfig.validate_twilio_voice_config():
            raise ValueError("Twilio Voice configuration is incomplete")

        # LinkedIn配置是可选的
        ServiceConfig.validate_linkedin_config()

        return True
    except Exception as e:
        print(f"Configuration validation failed: {e}")
        return False

def get_environment_info() -> dict:
    """获取环境信息"""
    return {
        "environment": CoreConfig.ENVIRONMENT,
        "debug": CoreConfig.DEBUG,
        "is_production": CoreConfig.is_production(),
        "is_development": CoreConfig.is_development()
    }
