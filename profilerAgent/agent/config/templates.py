"""
Templates - 对话模板和消息模板
语音对话模板、Web界面消息等
"""

from typing import Dict, List, Any
import random

class Templates:
    """模板配置 - MVP版本"""

    # ============ Web界面消息模板 ============
    WEB_MESSAGES = {
        "welcome_messages": [
            "Welcome to AI Dating! Let's find your perfect match through personality analysis.",
            "Ready to discover meaningful connections? Let's start with understanding who you are.",
            "Welcome! I'm here to help you find genuine relationships through AI-powered matching."
        ],

        "voice_invitation": [
            "Ready for your personality analysis? Call {phone_number} for a quick 5-minute conversation that helps me understand who you are!",
            "Time for your voice interview! Dial {phone_number} when you're ready - it's just 5 minutes and makes all the difference.",
            "Let's chat! Call {phone_number} for your personality analysis. It's quick, fun, and helps me find your perfect matches."
        ],

        "linkedin_request": [
            "Great job on your voice interview! Now, please add your LinkedIn profile to verify your information and enhance your matches.",
            "Voice analysis complete! Adding your LinkedIn profile helps verify your background and improves match quality.",
            "Almost done! Please submit your LinkedIn URL to complete your verification and unlock premium matching."
        ],

        "profile_complete": [
            "Your profile is complete! I'm generating your personality cards and finding your best matches.",
            "All set! Your verified profile gives you access to high-quality, compatible matches.",
            "Profile complete! Get ready to discover some amazing connections."
        ],

        "error_messages": [
            "Something went wrong. Please try again.",
            "We encountered an error. Please refresh and try again.",
            "Oops! There was an issue. Please try again."
        ]
    }

    # ============ 语音对话问题模板 ============
    VOICE_QUESTIONS = {
        "opening": [
            "Hi! Great to chat with you! Just relax, this is like talking to a friend. Could you start by telling me your name and which city you're in?"
        ],

        "professional": [
            "That sounds great! What kind of work do you do?",
            "Do you enjoy your current job? What gives you a sense of achievement at work?",
            "How did you get into this field? Any interesting experiences along the way?",
            "What's your role like day-to-day? Are you more hands-on or strategic?",
            "Where do you see your career heading in the next few years?"
        ],

        "personality": [
            "Do you prefer hanging out with friends or having some alone time to recharge?",
            "When making important decisions, how do you usually approach it? Do you go with your gut or analyze everything?",
            "How do you typically handle stress or pressure?",
            "Are you more of a planner who likes structure, or do you prefer to go with the flow?"
        ],

        "interests": [
            "Outside of work, what gets you most excited? What are you passionate about?",
            "How do you like to spend your weekends? Any favorite activities?",
            "Is there something you've always wanted to learn or try but haven't started yet?"
        ],

        "relationships": [
            "What does an ideal relationship look like to you?",
            "What qualities matter most to you in a partner?",
            "How do you like to show care and affection in relationships?"
        ],

        "closing": [
            "Thank you so much for sharing! I'll analyze our conversation and have your results ready in your profile shortly.",
            "This was wonderful! I have everything I need to create your personality profile. You'll see the results in your dashboard.",
            "Perfect! I'll analyze our conversation and update your profile with the personality insights."
        ]
    }

    # ============ AI分析提示模板 ============
    AI_PROMPTS = {
        "voice_analysis": {
            "system_prompt": """You are an expert personality analyst specializing in dating compatibility.
Analyze the voice conversation transcript to extract personality traits, professional background,
interests, and relationship preferences. Focus on MBTI dimensions and dating-relevant characteristics.
Provide confidence scores (0-1) for each dimension and specific evidence from the conversation.""",

            "analysis_prompt": """Analyze this voice conversation transcript and extract:

1. MBTI Personality Dimensions (provide scores 0-1 for each):
   - E/I (Extroversion/Introversion): Based on social energy and interaction style
   - S/N (Sensing/Intuition): Based on information processing and focus
   - T/F (Thinking/Feeling): Based on decision-making approach
   - J/P (Judging/Perceiving): Based on structure and planning preferences

2. Professional Profile:
   - Current position and company
   - Industry and experience level
   - Career goals and motivations
   - Work style preferences

3. Personal Interests (list specific interests mentioned):
   - Hobbies and activities
   - Lifestyle preferences
   - Values and priorities

4. Relationship Preferences:
   - Ideal partner qualities mentioned
   - Relationship goals and expectations
   - Communication style preferences

Conversation: {conversation_text}

Return a JSON response with confidence scores (0-1) for each dimension and specific evidence quotes."""
        },

        "linkedin_comparison": {
            "system_prompt": """You are a verification specialist. Compare voice conversation analysis
with LinkedIn profile data to assess consistency and authenticity. Focus on factual information
that can be cross-verified.""",

            "comparison_prompt": """Compare the voice analysis with LinkedIn data and assess consistency:

Voice Analysis: {voice_analysis}
LinkedIn Data: {linkedin_data}

Evaluate consistency in:
1. Basic Info (name, location, age indicators) - Weight: 25%
2. Professional Background (position, company, industry, experience) - Weight: 50%
3. Skills and Interests - Weight: 15%
4. Communication Style and Values - Weight: 10%

Provide:
- Overall consistency score (0-1)
- Detailed breakdown by category
- Specific inconsistencies found (if any)
- Verification recommendation (verified/partial/unverified)"""
        },

        "web_response": {
            "system_prompt": """You are a friendly AI dating assistant. Generate natural, helpful
responses for web interface interactions. Be warm, encouraging, and professional.""",

            "response_prompt": """Generate an appropriate response for this web interaction:

Context: {interaction_context}
User Action: {user_action}
Current Stage: {current_stage}

Response should be:
- Natural and friendly
- Appropriate for web interface
- Encouraging and supportive
- Clear and actionable
- Professional but warm"""
        }
    }

    # ============ 画像卡片模板 ============
    PROFILE_CARD_TEMPLATES = {
        "professional": {
            "tech_innovator": {
                "title": "Tech Innovator 💻",
                "description": "You're at the forefront of technology, driving digital transformation with creative solutions.",
                "tags": ["technology", "innovation", "problem-solving", "future-focused"]
            },
            "business_strategist": {
                "title": "Business Strategist 📊",
                "description": "You have sharp business insight with expertise in strategic planning and execution.",
                "tags": ["strategy", "leadership", "analytical", "results-driven"]
            },
            "creative_professional": {
                "title": "Creative Professional 🎨",
                "description": "You bring creativity and artistic vision to everything you do.",
                "tags": ["creative", "artistic", "innovative", "expressive"]
            },
            "people_leader": {
                "title": "People Leader 👥",
                "description": "You excel at inspiring and guiding teams toward shared goals.",
                "tags": ["leadership", "empathy", "communication", "team-building"]
            }
        },

        "personality": {
            "ENFJ": {
                "title": "The Inspiring Leader 🌟",
                "description": "You have natural charisma and empathy, inspiring others to reach their potential.",
                "tags": ["empathetic", "inspiring", "organized", "people-focused"]
            },
            "INFP": {
                "title": "The Authentic Idealist 🦋",
                "description": "You're driven by deep values and seek authentic, meaningful connections.",
                "tags": ["authentic", "creative", "values-driven", "empathetic"]
            },
            "ENTJ": {
                "title": "The Visionary Commander 🎯",
                "description": "You're a natural leader with strong vision and execution skills.",
                "tags": ["ambitious", "strategic", "confident", "goal-oriented"]
            },
            "ISFJ": {
                "title": "The Caring Protector 🛡️",
                "description": "You're warm, reliable, and always there for the people you care about.",
                "tags": ["caring", "reliable", "supportive", "loyal"]
            }
        },

        "interests": {
            "music_lover": {
                "title": "Music Enthusiast 🎵",
                "description": "Music is the soundtrack to your life - you appreciate rhythm, melody, and emotion.",
                "tags": ["music", "artistic", "emotional", "cultural"]
            },
            "adventure_seeker": {
                "title": "Adventure Seeker 🏔️",
                "description": "You love exploring new places and trying exciting experiences.",
                "tags": ["adventurous", "curious", "active", "open-minded"]
            },
            "foodie_explorer": {
                "title": "Culinary Explorer 🍽️",
                "description": "You appreciate good food and love discovering new flavors and cuisines.",
                "tags": ["culinary", "social", "curious", "experiential"]
            },
            "fitness_enthusiast": {
                "title": "Fitness Enthusiast 💪",
                "description": "You prioritize health and wellness, finding joy in physical activity.",
                "tags": ["health-conscious", "disciplined", "energetic", "goal-oriented"]
            }
        },

        "social": {
            "social_connector": {
                "title": "Social Connector 🤝",
                "description": "You're skilled at bringing people together and creating meaningful connections.",
                "tags": ["social", "networking", "empathetic", "inclusive"]
            },
            "deep_thinker": {
                "title": "Deep Thinker 🤔",
                "description": "You prefer meaningful conversations and value quality over quantity in relationships.",
                "tags": ["thoughtful", "introspective", "genuine", "quality-focused"]
            },
            "team_player": {
                "title": "Team Player 🏆",
                "description": "You thrive in collaborative environments and bring out the best in others.",
                "tags": ["collaborative", "supportive", "reliable", "harmonious"]
            }
        }
    }

    # ============ 匹配推荐模板 ============
    MATCH_TEMPLATES = {
        "recommendation_intro": [
            "Here's a great match for you!",
            "You might really connect with this person:",
            "This person could be a perfect match:"
        ],

        "compatibility_reasons": {
            "mbti_compatible": "Your personality types complement each other beautifully",
            "shared_interests": "You both share a passion for {interests}",
            "professional_synergy": "Your careers align in interesting ways",
            "communication_style": "You have compatible communication styles",
            "values_alignment": "You share similar values and life goals"
        },

        "match_actions": [
            "Click 'Interested' if you'd like to connect, or 'Pass' to see the next match!",
            "Tap 'Like' if you're interested, or 'Next' for more recommendations!",
            "Show interest or continue browsing - it's up to you!"
        ],

        "no_matches": [
            "No new matches today. Check back tomorrow for fresh recommendations!",
            "We're working on finding your perfect matches. Come back soon!",
            "More great matches coming soon. Check back later!"
        ]
    }

    # ============ MBTI兼容性配置 ============
    MBTI_COMPATIBILITY = {
        # 理想匹配 (0.9-1.0)
        "ENFJ": ["INFP", "ISFP", "ENFJ", "INFJ"],
        "ENFP": ["INTJ", "INFJ", "ENFP", "ENTP"],
        "ENTJ": ["INTP", "INFP", "ENTJ", "INTJ"],
        "ENTP": ["INFJ", "INTJ", "ENTP", "ENFP"],
        "ESFJ": ["ISFP", "ISTP", "ESFJ", "ISFJ"],
        "ESFP": ["ISFJ", "ISTJ", "ESFP", "ESTP"],
        "ESTJ": ["ISTP", "ISFP", "ESTJ", "ISTJ"],
        "ESTP": ["ISFJ", "ISTJ", "ESTP", "ESFP"],
        "INFJ": ["ENFP", "ENTP", "INFJ", "ENFJ"],
        "INFP": ["ENFJ", "ENTJ", "INFP", "ISFP"],
        "INTJ": ["ENFP", "ENTP", "INTJ", "ENTJ"],
        "INTP": ["ENTJ", "ESTJ", "INTP", "INTJ"],
        "ISFJ": ["ESFP", "ESTP", "ISFJ", "ESFJ"],
        "ISFP": ["ENFJ", "ESFJ", "ISFP", "INFP"],
        "ISTJ": ["ESFP", "ESTP", "ISTJ", "ESTJ"],
        "ISTP": ["ESFJ", "ESTJ", "ISTP", "ISFP"]
    }

    # ============ 工具方法 ============
    @classmethod
    def get_random_template(cls, template_category: str, template_type: str) -> str:
        """获取随机模板"""
        templates = getattr(cls, template_category, {}).get(template_type, [])
        if not templates:
            return "Template not found"
        return random.choice(templates)

    @classmethod
    def get_web_message(cls, template_type: str, **kwargs) -> str:
        """获取Web消息模板并格式化"""
        template = cls.get_random_template("WEB_MESSAGES", template_type)
        try:
            return template.format(**kwargs)
        except KeyError:
            return template

    @classmethod
    def get_voice_question(cls, stage: str, exclude_used: List[str] = None) -> str:
        """获取语音问题，避免重复"""
        questions = cls.VOICE_QUESTIONS.get(stage, [])
        if exclude_used:
            questions = [q for q in questions if q not in exclude_used]

        if not questions:
            return "Could you tell me more about that?"

        return random.choice(questions)

    @classmethod
    def get_profile_card_template(cls, card_type: str, specific_type: str) -> Dict[str, Any]:
        """获取画像卡片模板"""
        return cls.PROFILE_CARD_TEMPLATES.get(card_type, {}).get(specific_type, {
            "title": "Unique Individual",
            "description": "You have a unique personality that makes you special.",
            "tags": ["unique", "individual", "special"]
        })

    @classmethod
    def get_mbti_compatibility_score(cls, mbti1: str, mbti2: str) -> float:
        """获取MBTI兼容性分数"""
        if not mbti1 or not mbti2:
            return 0.5

        compatible_types = cls.MBTI_COMPATIBILITY.get(mbti1, [])
        if mbti2 in compatible_types:
            # 理想匹配
            return 0.9
        elif mbti1 == mbti2:
            # 相同类型
            return 0.7
        else:
            # 计算维度兼容性
            score = 0.0
            for i in range(4):
                if mbti1[i] == mbti2[i]:
                    score += 0.15  # 相同维度加分
                else:
                    score += 0.1   # 不同维度也有一定兼容性
            return min(score, 0.8)

    @classmethod
    def format_match_recommendation(cls, match_data: Dict[str, Any]) -> Dict[str, Any]:
        """格式化匹配推荐数据（用于Web界面）"""
        intro = cls.get_random_template("MATCH_TEMPLATES", "recommendation_intro")
        action = cls.get_random_template("MATCH_TEMPLATES", "match_actions")

        # 构建推荐数据结构
        recommendation = {
            "intro_message": intro,
            "user_info": {
                "name": match_data.get('name', 'Someone special'),
                "profession": match_data.get('profession', 'Professional'),
                "location": match_data.get('location', 'Nearby'),
                "age": match_data.get('age'),
                "photo_url": match_data.get('photo_url')
            },
            "compatibility_score": match_data.get('match_score', 0),
            "compatibility_reasons": match_data.get('compatibility_reasons', [])[:2],
            "action_message": action,
            "match_id": match_data.get('match_id')
        }

        return recommendation
