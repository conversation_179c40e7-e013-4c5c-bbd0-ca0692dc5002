"""
ServiceConfig - 服务配置
Twilio SMS/Voice、LinkedIn等外部服务配置
"""

import os
from typing import Dict, Any, List

class ServiceConfig:
    """服务配置 - MVP版本"""

    # ============ Twilio混合模式配置 ============
    TWILIO_MODE = os.getenv("TWILIO_MODE", "live")  # test | live | auto

    # Live凭据
    TWILIO_LIVE_ACCOUNT_SID = os.getenv("TWILIO_LIVE_ACCOUNT_SID", "")
    TWILIO_LIVE_AUTH_TOKEN = os.getenv("TWILIO_LIVE_AUTH_TOKEN", "")

    # Test凭据
    TWILIO_TEST_ACCOUNT_SID = os.getenv("TWILIO_TEST_ACCOUNT_SID", "")
    TWILIO_TEST_AUTH_TOKEN = os.getenv("TWILIO_TEST_AUTH_TOKEN", "")

    @classmethod
    def get_twilio_credentials(cls):
        """根据模式获取Twilio凭据"""
        mode = cls.TWILIO_MODE.lower()

        if mode == "live":
            return {
                "account_sid": cls.TWILIO_LIVE_ACCOUNT_SID,
                "auth_token": cls.TWILIO_LIVE_AUTH_TOKEN,
                "mode": "live"
            }
        elif mode == "test":
            return {
                "account_sid": cls.TWILIO_TEST_ACCOUNT_SID,
                "auth_token": cls.TWILIO_TEST_AUTH_TOKEN,
                "mode": "test"
            }
        elif mode == "auto":
            # 自动模式：开发环境用test，生产环境用live
            env = os.getenv("APP_ENV", "development")
            if env == "production":
                return {
                    "account_sid": cls.TWILIO_LIVE_ACCOUNT_SID,
                    "auth_token": cls.TWILIO_LIVE_AUTH_TOKEN,
                    "mode": "live"
                }
            else:
                return {
                    "account_sid": cls.TWILIO_TEST_ACCOUNT_SID,
                    "auth_token": cls.TWILIO_TEST_AUTH_TOKEN,
                    "mode": "test"
                }
        else:
            # 默认使用test
            return {
                "account_sid": cls.TWILIO_TEST_ACCOUNT_SID,
                "auth_token": cls.TWILIO_TEST_AUTH_TOKEN,
                "mode": "test"
            }

    # 兼容性：保持原有的属性
    @classmethod
    def get_current_twilio_account_sid(cls):
        return cls.get_twilio_credentials()["account_sid"]

    @classmethod
    def get_current_twilio_auth_token(cls):
        return cls.get_twilio_credentials()["auth_token"]

    # Twilio SMS配置
    TWILIO_SMS_SERVICE_SID = os.getenv("TWILIO_SMS_SERVICE_SID", "")
    TWILIO_SMS_PHONE_NUMBER = os.getenv("TWILIO_SMS_PHONE_NUMBER", "")

    # Twilio Voice配置
    TWILIO_VOICE_PHONE_NUMBER = os.getenv("TWILIO_VOICE_PHONE_NUMBER", "")
    TWILIO_WEBHOOK_URL = os.getenv("TWILIO_WEBHOOK_URL", "")

    # Twilio语音设置
    TWILIO_VOICE_LANGUAGE = "en-US"  # MVP版本仅支持英语
    TWILIO_VOICE_MODEL = "alice"
    TWILIO_SPEECH_TIMEOUT = "auto"
    TWILIO_CONFIDENCE_THRESHOLD = float(os.getenv("TWILIO_CONFIDENCE_THRESHOLD", "0.7"))
    TWILIO_MAX_CALL_DURATION = int(os.getenv("TWILIO_MAX_CALL_DURATION", "600"))  # 10分钟

    # SMS验证设置
    SMS_CODE_EXPIRE_MINUTES = int(os.getenv("SMS_CODE_EXPIRE_MINUTES", "5"))
    SMS_MAX_ATTEMPTS = int(os.getenv("SMS_MAX_ATTEMPTS", "3"))
    SMS_RATE_LIMIT_PER_HOUR = int(os.getenv("SMS_RATE_LIMIT_PER_HOUR", "5"))

    # ============ AI服务配置 ============
    DEEPSEEK_API_KEY = os.getenv("DEEPSEEK_API_KEY", "")
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "")
    AI_PROVIDER = os.getenv("AI_PROVIDER", "openai").lower()  # openai 或 deepseek
    AI_ENABLED = os.getenv("AI_ENABLED", "true").lower() == "true"
    AI_TIMEOUT = int(os.getenv("AI_TIMEOUT", "10"))  # 10秒超时

    # ============ LinkedIn数据抓取配置 ============
    LINKEDIN_SCRAPER_API = os.getenv("LINKEDIN_SCRAPER_API", "")
    BRIGHT_DATA_API_KEY = os.getenv("BRIGHT_DATA_API_KEY", "")
    LINKEDIN_SCRAPER_TIMEOUT = int(os.getenv("LINKEDIN_SCRAPER_TIMEOUT", "30"))
    LINKEDIN_RETRY_ATTEMPTS = int(os.getenv("LINKEDIN_RETRY_ATTEMPTS", "3"))
    LINKEDIN_RATE_LIMIT = int(os.getenv("LINKEDIN_RATE_LIMIT", "1"))  # 每秒请求数

    # LinkedIn验证配置
    LINKEDIN_VERIFICATION_THRESHOLD = float(os.getenv("LINKEDIN_VERIFICATION_THRESHOLD", "0.6"))
    LINKEDIN_CONSISTENCY_THRESHOLD = float(os.getenv("LINKEDIN_CONSISTENCY_THRESHOLD", "0.65"))
    LINKEDIN_REQUIRED_FIELDS = ["name", "current_position", "current_company", "location"]

    # Bright Data配置
    BRIGHT_DATA_ZONE = os.getenv("BRIGHT_DATA_ZONE", "datacenter")
    BRIGHT_DATA_ENDPOINT = os.getenv("BRIGHT_DATA_ENDPOINT", "https://api.brightdata.com")
    BRIGHT_DATA_TIMEOUT = int(os.getenv("BRIGHT_DATA_TIMEOUT", "60"))
    BRIGHT_DATA_MAX_RETRIES = int(os.getenv("BRIGHT_DATA_MAX_RETRIES", "3"))
    BRIGHT_DATA_RATE_LIMIT_DELAY = float(os.getenv("BRIGHT_DATA_RATE_LIMIT_DELAY", "2.0"))
    BRIGHT_DATA_USER_AGENT = os.getenv("BRIGHT_DATA_USER_AGENT", "Mozilla/5.0 (compatible; ProfilerAgent/1.0)")

    # ============ 外部API通用配置 ============
    DEFAULT_REQUEST_TIMEOUT = int(os.getenv("DEFAULT_REQUEST_TIMEOUT", "30"))
    DEFAULT_RETRY_ATTEMPTS = int(os.getenv("DEFAULT_RETRY_ATTEMPTS", "3"))
    DEFAULT_RETRY_DELAY = float(os.getenv("DEFAULT_RETRY_DELAY", "1.0"))

    # ============ 配置验证 ============
    @classmethod
    def validate_twilio_sms_config(cls) -> bool:
        """验证Twilio SMS配置"""
        credentials = cls.get_twilio_credentials()
        required = [
            credentials["account_sid"],
            credentials["auth_token"]
        ]
        # SMS服务可以使用Service SID或Phone Number
        has_sms_service = cls.TWILIO_SMS_SERVICE_SID or cls.TWILIO_SMS_PHONE_NUMBER
        return all(required) and has_sms_service

    @classmethod
    def validate_twilio_voice_config(cls) -> bool:
        """验证Twilio Voice配置"""
        credentials = cls.get_twilio_credentials()
        required = [
            credentials["account_sid"],
            credentials["auth_token"],
            cls.TWILIO_VOICE_PHONE_NUMBER
        ]
        return all(required)

    @classmethod
    def validate_linkedin_config(cls) -> bool:
        """验证LinkedIn配置"""
        # LinkedIn配置是可选的，但如果提供了API key就需要完整配置
        if cls.LINKEDIN_SCRAPER_API or cls.BRIGHT_DATA_API_KEY:
            return bool(cls.LINKEDIN_SCRAPER_API or cls.BRIGHT_DATA_API_KEY)
        return True  # 没有配置也是有效的

    # ============ 配置获取方法 ============
    @classmethod
    def get_twilio_sms_config(cls) -> Dict[str, Any]:
        """获取Twilio SMS配置"""
        credentials = cls.get_twilio_credentials()
        return {
            "account_sid": credentials["account_sid"],
            "auth_token": credentials["auth_token"],
            "service_sid": cls.TWILIO_SMS_SERVICE_SID,
            "phone_number": cls.TWILIO_SMS_PHONE_NUMBER,
            "code_expire_minutes": cls.SMS_CODE_EXPIRE_MINUTES,
            "max_attempts": cls.SMS_MAX_ATTEMPTS,
            "rate_limit_per_hour": cls.SMS_RATE_LIMIT_PER_HOUR,
            "mode": credentials["mode"]
        }

    @classmethod
    def get_twilio_voice_config(cls) -> Dict[str, Any]:
        """获取Twilio Voice配置"""
        credentials = cls.get_twilio_credentials()
        return {
            "account_sid": credentials["account_sid"],
            "auth_token": credentials["auth_token"],
            "phone_number": cls.TWILIO_VOICE_PHONE_NUMBER,
            "webhook_url": cls.TWILIO_WEBHOOK_URL,
            "voice_language": cls.TWILIO_VOICE_LANGUAGE,
            "voice_model": cls.TWILIO_VOICE_MODEL,
            "speech_timeout": cls.TWILIO_SPEECH_TIMEOUT,
            "confidence_threshold": cls.TWILIO_CONFIDENCE_THRESHOLD,
            "max_call_duration": cls.TWILIO_MAX_CALL_DURATION,
            "mode": credentials["mode"]
        }

    @classmethod
    def get_linkedin_config(cls) -> Dict[str, Any]:
        """获取LinkedIn配置"""
        return {
            "scraper_api": cls.LINKEDIN_SCRAPER_API,
            "bright_data_api_key": cls.BRIGHT_DATA_API_KEY,
            "timeout": cls.LINKEDIN_SCRAPER_TIMEOUT,
            "retry_attempts": cls.LINKEDIN_RETRY_ATTEMPTS,
            "rate_limit": cls.LINKEDIN_RATE_LIMIT,
            "verification_threshold": cls.LINKEDIN_VERIFICATION_THRESHOLD,
            "required_fields": cls.LINKEDIN_REQUIRED_FIELDS
        }

    @classmethod
    def get_bright_data_config(cls) -> Dict[str, Any]:
        """获取Bright Data配置"""
        return {
            "api_key": cls.BRIGHT_DATA_API_KEY,
            "zone": cls.BRIGHT_DATA_ZONE,
            "endpoint": cls.BRIGHT_DATA_ENDPOINT,
            "timeout": cls.BRIGHT_DATA_TIMEOUT,
            "max_retries": cls.BRIGHT_DATA_MAX_RETRIES,
            "rate_limit_delay": cls.BRIGHT_DATA_RATE_LIMIT_DELAY,
            "user_agent": cls.BRIGHT_DATA_USER_AGENT
        }
