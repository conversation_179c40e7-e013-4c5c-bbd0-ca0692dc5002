"""
AgentFactory - Agent系统工厂类
统一初始化和配置所有Agent组件
"""

import logging
import asyncio
from typing import Optional, Dict, Any

from .config import CoreConfig, ServiceConfig, validate_all_configs
from .core.database_manager import DatabaseManager
from .core.registration_state import RegistrationStateManager
from .core.profile_manager import ProfileManager
from .core.memory_manager import MemoryManager
from .agent_interface import AgentInterface

# Optional service imports
try:
    from .services.sms_service import SMSService
except ImportError:
    SMSService = None

try:
    from .services.voice_service import VoiceService
except ImportError:
    VoiceService = None

try:
    from .services.linkedin_service import LinkedInService
except ImportError:
    LinkedInService = None

logger = logging.getLogger(__name__)

class AgentFactory:
    """Agent系统工厂类 - 统一初始化所有组件"""
    
    def __init__(self):
        """初始化工厂"""
        self.components = {}
        self.initialized = False
        
    async def initialize_system(self, redis_client=None) -> Dict[str, Any]:
        """初始化整个Agent系统"""
        try:
            logger.info("Starting Agent system initialization...")
            
            # 1. 验证配置
            if not validate_all_configs():
                raise ValueError("Configuration validation failed")
            
            # 2. 初始化数据库管理器
            db_manager = DatabaseManager()
            await db_manager.connect()
            self.components['db_manager'] = db_manager

            # 3. 初始化核心管理器
            registration_manager = RegistrationStateManager(
                redis_client=redis_client,
                db_manager=db_manager
            )
            self.components['registration_manager'] = registration_manager

            profile_manager = ProfileManager(
                db_manager=db_manager,
                redis_client=redis_client
            )
            self.components['profile_manager'] = profile_manager

            memory_manager = MemoryManager(
                redis_client=redis_client,
                db_manager=db_manager
            )
            self.components['memory_manager'] = memory_manager

            # 4. 初始化可选服务层
            sms_service = None
            if SMSService:
                try:
                    sms_service = SMSService(
                        redis_client=redis_client,
                        db_manager=db_manager
                    )
                    self.components['sms_service'] = sms_service
                except Exception as e:
                    logger.warning(f"Failed to initialize SMS service: {e}")

            # 5. 初始化主接口（简化版）
            agent_interface = AgentInterface(
                db_manager=db_manager,
                registration_manager=registration_manager,
                profile_manager=profile_manager,
                memory_manager=memory_manager,
                redis_client=redis_client
            )
            self.components['agent_interface'] = agent_interface
            
            self.initialized = True
            
            logger.info("Agent system initialization completed successfully")
            
            return {
                "status": "success",
                "components": list(self.components.keys()),
                "config_info": {
                    "environment": CoreConfig.ENVIRONMENT,
                    "debug": CoreConfig.DEBUG,
                    "database_url": CoreConfig.DATABASE_URL[:50] + "...",
                    "redis_url": CoreConfig.REDIS_URL[:50] + "..."
                }
            }
            
        except Exception as e:
            logger.error(f"Agent system initialization failed: {e}")
            await self.cleanup()
            raise
    
    def get_component(self, component_name: str):
        """获取组件实例"""
        if not self.initialized:
            raise RuntimeError("Agent system not initialized")
        
        component = self.components.get(component_name)
        if not component:
            raise ValueError(f"Component '{component_name}' not found")
        
        return component
    
    def get_agent_interface(self) -> AgentInterface:
        """Get main interface"""
        return self.get_component('agent_interface')

    def get_database_manager(self) -> DatabaseManager:
        """Get database manager"""
        return self.get_component('db_manager')

    def get_registration_manager(self) -> RegistrationStateManager:
        """Get registration manager"""
        return self.get_component('registration_manager')

    def get_profile_manager(self) -> ProfileManager:
        """Get profile manager"""
        return self.get_component('profile_manager')

    def get_memory_manager(self) -> MemoryManager:
        """Get memory manager"""
        return self.get_component('memory_manager')

    def get_sms_service(self):
        """Get SMS service (if available)"""
        return self.get_component('sms_service') if SMSService else None
    
    async def cleanup(self):
        """清理资源"""
        try:
            logger.info("Cleaning up Agent system...")
            
            # 断开数据库连接
            if 'db_manager' in self.components:
                await self.components['db_manager'].disconnect()
            
            # 清理其他资源
            self.components.clear()
            self.initialized = False
            
            logger.info("Agent system cleanup completed")
            
        except Exception as e:
            logger.error(f"Agent system cleanup error: {e}")
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        if not self.initialized:
            return {
                "initialized": False,
                "components": [],
                "status": "not_initialized"
            }
        
        status = {
            "initialized": True,
            "components": {},
            "status": "healthy"
        }
        
        # 检查各组件状态
        for name, component in self.components.items():
            try:
                if hasattr(component, 'check_service_status'):
                    status["components"][name] = component.check_service_status()
                elif hasattr(component, 'check_connection'):
                    status["components"][name] = component.check_connection()
                else:
                    status["components"][name] = {"status": "active"}
            except Exception as e:
                status["components"][name] = {"status": "error", "error": str(e)}
        
        return status

# 全局工厂实例
agent_factory = AgentFactory()

async def initialize_agent_system(redis_client=None) -> AgentFactory:
    """初始化Agent系统的便捷函数"""
    await agent_factory.initialize_system(redis_client)
    return agent_factory

def get_agent_interface() -> AgentInterface:
    """获取Agent接口的便捷函数"""
    return agent_factory.get_agent_interface()

def get_system_status() -> Dict[str, Any]:
    """获取系统状态的便捷函数"""
    return agent_factory.get_system_status()

async def cleanup_agent_system():
    """清理Agent系统的便捷函数"""
    await agent_factory.cleanup()
