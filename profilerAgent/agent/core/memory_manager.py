"""
MemoryManager - 记忆系统管理
管理短期记忆、对话上下文和重要话题
"""

import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict

logger = logging.getLogger(__name__)

@dataclass
class ConversationMessage:
    """对话消息"""
    role: str  # 'user' or 'assistant'
    content: str
    timestamp: datetime
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
    
    def to_dict(self) -> Dict[str, Any]:
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ConversationMessage':
        data['timestamp'] = datetime.fromisoformat(data['timestamp'])
        return cls(**data)

@dataclass
class ImportantTopic:
    """重要话题"""
    topic: str
    context: str
    last_mentioned: datetime
    importance_score: float = 1.0
    related_keywords: List[str] = None
    
    def __post_init__(self):
        if self.related_keywords is None:
            self.related_keywords = []
    
    def to_dict(self) -> Dict[str, Any]:
        data = asdict(self)
        data['last_mentioned'] = self.last_mentioned.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ImportantTopic':
        data['last_mentioned'] = datetime.fromisoformat(data['last_mentioned'])
        return cls(**data)

class MemoryManager:
    """记忆系统管理器"""
    
    def __init__(self, redis_client=None, db_manager=None):
        self.redis = redis_client
        self.db = db_manager
        
        # 配置
        self.short_term_memory_limit = 10  # 最多保留10轮对话
        self.memory_cache_ttl = 3600  # 1小时
        self.important_topic_ttl = 7 * 24 * 3600  # 7天
        
        logger.info("MemoryManager initialized")
    
    async def add_message(self, user_id: str, role: str, content: str, 
                         metadata: Dict[str, Any] = None) -> bool:
        """添加消息到短期记忆"""
        try:
            message = ConversationMessage(
                role=role,
                content=content,
                timestamp=datetime.now(),
                metadata=metadata or {}
            )
            
            # 获取现有短期记忆
            short_memory = await self._get_short_term_memory(user_id)
            
            # 添加新消息
            short_memory.append(message)
            
            # 保持滑动窗口
            if len(short_memory) > self.short_term_memory_limit:
                short_memory = short_memory[-self.short_term_memory_limit:]
            
            # 保存更新后的记忆
            await self._save_short_term_memory(user_id, short_memory)
            
            # 检测重要话题
            if role == "user":
                await self._detect_and_save_important_topics(user_id, content)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to add message to memory for user {user_id}: {e}")
            return False
    
    async def get_conversation_context(self, user_id: str, include_topics: bool = True) -> Dict[str, Any]:
        """获取对话上下文"""
        try:
            context = {
                "short_term_memory": [],
                "important_topics": [],
                "conversation_summary": ""
            }
            
            # 获取短期记忆
            short_memory = await self._get_short_term_memory(user_id)
            context["short_term_memory"] = [msg.to_dict() for msg in short_memory]
            
            # 获取重要话题
            if include_topics:
                important_topics = await self._get_important_topics(user_id)
                context["important_topics"] = [topic.to_dict() for topic in important_topics]
            
            # 生成对话摘要
            if short_memory:
                context["conversation_summary"] = self._generate_conversation_summary(short_memory)
            
            return context
            
        except Exception as e:
            logger.error(f"Failed to get conversation context for user {user_id}: {e}")
            return {"short_term_memory": [], "important_topics": [], "conversation_summary": ""}
    
    async def build_llm_context(self, user_id: str, current_input: str, 
                               user_profile: Dict[str, Any] = None) -> List[Dict[str, str]]:
        """构建LLM上下文"""
        try:
            context_messages = []
            
            # 系统提示
            system_prompt = self._build_system_prompt(user_profile)
            context_messages.append({"role": "system", "content": system_prompt})
            
            # 获取短期记忆
            short_memory = await self._get_short_term_memory(user_id)
            
            # 添加最近的对话（最多8条消息）
            recent_messages = short_memory[-8:] if len(short_memory) > 8 else short_memory
            for msg in recent_messages:
                context_messages.append({
                    "role": msg.role,
                    "content": msg.content
                })
            
            # 添加当前用户输入
            context_messages.append({"role": "user", "content": current_input})
            
            return context_messages
            
        except Exception as e:
            logger.error(f"Failed to build LLM context for user {user_id}: {e}")
            return [{"role": "user", "content": current_input}]
    
    async def clear_memory(self, user_id: str) -> bool:
        """清除用户记忆"""
        try:
            # 清除Redis缓存
            if self.redis:
                await self.redis.delete(f"short_memory:{user_id}")
                await self.redis.delete(f"important_topics:{user_id}")
            
            logger.info(f"Cleared memory for user {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to clear memory for user {user_id}: {e}")
            return False
    
    async def _get_short_term_memory(self, user_id: str) -> List[ConversationMessage]:
        """获取短期记忆"""
        try:
            if self.redis:
                cached_data = await self.redis.get(f"short_memory:{user_id}")
                if cached_data:
                    messages_data = json.loads(cached_data)
                    return [ConversationMessage.from_dict(msg_data) for msg_data in messages_data]
            
            return []
            
        except Exception as e:
            logger.warning(f"Failed to get short term memory for user {user_id}: {e}")
            return []
    
    async def _save_short_term_memory(self, user_id: str, messages: List[ConversationMessage]):
        """保存短期记忆"""
        try:
            if self.redis:
                messages_data = [msg.to_dict() for msg in messages]
                await self.redis.setex(
                    f"short_memory:{user_id}",
                    self.memory_cache_ttl,
                    json.dumps(messages_data, default=str)
                )
            
        except Exception as e:
            logger.warning(f"Failed to save short term memory for user {user_id}: {e}")
    
    async def _detect_and_save_important_topics(self, user_id: str, user_input: str):
        """检测并保存重要话题"""
        try:
            # 简单的关键词检测
            important_keywords = {
                "工作": ["工作", "职业", "公司", "同事", "老板", "项目"],
                "爱好": ["喜欢", "爱好", "兴趣", "运动", "音乐", "电影"],
                "关系": ["男朋友", "女朋友", "恋爱", "结婚", "单身", "约会"],
                "家庭": ["家人", "父母", "兄弟", "姐妹", "家庭"],
                "未来": ["计划", "目标", "梦想", "希望", "将来"]
            }
            
            detected_topics = []
            for topic, keywords in important_keywords.items():
                for keyword in keywords:
                    if keyword in user_input:
                        detected_topics.append({
                            "topic": topic,
                            "context": user_input[:100],  # 保留前100个字符作为上下文
                            "keywords": [keyword]
                        })
                        break
            
            # 保存检测到的话题
            if detected_topics:
                await self._save_important_topics(user_id, detected_topics)
            
        except Exception as e:
            logger.warning(f"Failed to detect important topics for user {user_id}: {e}")
    
    async def _save_important_topics(self, user_id: str, topics_data: List[Dict[str, Any]]):
        """保存重要话题"""
        try:
            # 获取现有话题
            existing_topics = await self._get_important_topics(user_id)
            existing_topics_dict = {topic.topic: topic for topic in existing_topics}
            
            # 更新或添加新话题
            for topic_data in topics_data:
                topic_name = topic_data["topic"]
                if topic_name in existing_topics_dict:
                    # 更新现有话题
                    existing_topics_dict[topic_name].last_mentioned = datetime.now()
                    existing_topics_dict[topic_name].context = topic_data["context"]
                    existing_topics_dict[topic_name].importance_score += 0.1  # 增加重要性
                else:
                    # 添加新话题
                    new_topic = ImportantTopic(
                        topic=topic_name,
                        context=topic_data["context"],
                        last_mentioned=datetime.now(),
                        importance_score=1.0,
                        related_keywords=topic_data.get("keywords", [])
                    )
                    existing_topics_dict[topic_name] = new_topic
            
            # 保存到Redis
            if self.redis:
                topics_list = list(existing_topics_dict.values())
                topics_data = [topic.to_dict() for topic in topics_list]
                await self.redis.setex(
                    f"important_topics:{user_id}",
                    self.important_topic_ttl,
                    json.dumps(topics_data, default=str)
                )
            
        except Exception as e:
            logger.warning(f"Failed to save important topics for user {user_id}: {e}")
    
    async def _get_important_topics(self, user_id: str) -> List[ImportantTopic]:
        """获取重要话题"""
        try:
            if self.redis:
                cached_data = await self.redis.get(f"important_topics:{user_id}")
                if cached_data:
                    topics_data = json.loads(cached_data)
                    return [ImportantTopic.from_dict(topic_data) for topic_data in topics_data]
            
            return []
            
        except Exception as e:
            logger.warning(f"Failed to get important topics for user {user_id}: {e}")
            return []
    
    def _generate_conversation_summary(self, messages: List[ConversationMessage]) -> str:
        """生成对话摘要"""
        if not messages:
            return ""
        
        # 简单的摘要生成
        user_messages = [msg.content for msg in messages if msg.role == "user"]
        if not user_messages:
            return ""
        
        # 提取关键信息
        summary_parts = []
        if len(user_messages) > 0:
            summary_parts.append(f"用户提到了{len(user_messages)}个话题")
        
        # 检测主要话题
        all_content = " ".join(user_messages)
        if "工作" in all_content or "职业" in all_content:
            summary_parts.append("讨论了工作相关话题")
        if "喜欢" in all_content or "爱好" in all_content:
            summary_parts.append("分享了兴趣爱好")
        if "家" in all_content or "家人" in all_content:
            summary_parts.append("谈到了家庭情况")
        
        return "，".join(summary_parts) if summary_parts else "进行了日常对话"
    
    def _build_system_prompt(self, user_profile: Dict[str, Any] = None) -> str:
        """构建系统提示"""
        base_prompt = """你是一个专业的约会档案助手。你的任务是通过自然对话了解用户，帮助他们完善约会档案。

请保持对话自然、友好，避免像问卷调查一样机械地提问。根据用户的回答灵活调整话题。"""
        
        if user_profile:
            profile_summary = self._summarize_profile(user_profile)
            if profile_summary:
                base_prompt += f"\n\n用户档案信息：\n{profile_summary}"
        
        return base_prompt
    
    def _summarize_profile(self, profile: Dict[str, Any]) -> str:
        """总结用户档案"""
        summary_parts = []
        
        # 基本信息
        basic_info = profile.get("basic_info", {})
        if basic_info.get("name"):
            summary_parts.append(f"姓名：{basic_info['name']}")
        if basic_info.get("age"):
            summary_parts.append(f"年龄：{basic_info['age']}")
        if basic_info.get("city"):
            summary_parts.append(f"城市：{basic_info['city']}")
        if basic_info.get("occupation"):
            summary_parts.append(f"职业：{basic_info['occupation']}")
        
        # 兴趣爱好
        interests = profile.get("interests", {})
        if interests.get("hobbies"):
            hobbies = interests["hobbies"]
            if isinstance(hobbies, list):
                summary_parts.append(f"兴趣：{', '.join(hobbies)}")
        
        return "\n".join(summary_parts)
