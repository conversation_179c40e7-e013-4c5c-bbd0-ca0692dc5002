"""
ProfileManager - 用户档案管理
负责用户档案的创建、更新和智能合并
"""

import json
import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from copy import deepcopy

logger = logging.getLogger(__name__)

class ProfileManager:
    """用户档案管理器"""
    
    def __init__(self, db_manager=None, redis_client=None):
        self.db = db_manager
        self.redis = redis_client
        
        # 配置
        self.profile_cache_ttl = 3600  # 1小时
        self.confidence_threshold = 0.7  # 置信度阈值
        
        # 档案字段定义
        self.profile_schema = {
            "basic_info": {
                "required_fields": ["name", "age", "city", "occupation"],
                "optional_fields": ["education", "company", "industry"]
            },
            "personality": {
                "required_fields": [],
                "optional_fields": ["mbti", "traits", "social_energy", "communication_style"]
            },
            "interests": {
                "required_fields": [],
                "optional_fields": ["hobbies", "music", "sports", "entertainment", "travel"]
            },
            "relationship": {
                "required_fields": [],
                "optional_fields": ["looking_for", "preferences", "deal_breakers", "relationship_history"]
            },
            "lifestyle": {
                "required_fields": [],
                "optional_fields": ["work_schedule", "social_life", "health_habits", "living_situation"]
            },
            "values": {
                "required_fields": [],
                "optional_fields": ["life_goals", "family_values", "career_priorities", "personal_beliefs"]
            }
        }
        
        logger.info("ProfileManager initialized")
    
    async def create_profile(self, user_id: str, initial_data: Dict[str, Any] = None) -> bool:
        """创建新的用户档案"""
        try:
            if initial_data is None:
                initial_data = {}
            
            # 初始化空档案结构
            profile_data = self._initialize_empty_profile()
            
            # 合并初始数据
            if initial_data:
                profile_data = self._deep_merge_profiles(profile_data, initial_data)
            
            # 保存到数据库
            success = await self.db.create_user_profile(user_id, profile_data)
            
            if success:
                # 缓存到Redis
                await self._cache_profile(user_id, profile_data)
                logger.info(f"Created profile for user {user_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to create profile for user {user_id}: {e}")
            return False
    
    async def get_profile(self, user_id: str) -> Optional[Dict[str, Any]]:
        """获取用户档案"""
        try:
            # 先从Redis缓存获取
            cached_profile = await self._get_cached_profile(user_id)
            if cached_profile:
                return cached_profile
            
            # 从数据库获取
            profile_record = await self.db.get_user_profile(user_id)
            if not profile_record:
                return None
            
            profile_data = profile_record.get('profile_data', {})
            
            # 缓存到Redis
            await self._cache_profile(user_id, profile_data)
            
            return profile_data
            
        except Exception as e:
            logger.error(f"Failed to get profile for user {user_id}: {e}")
            return None
    
    async def update_profile(self, user_id: str, new_data: Dict[str, Any], 
                           confidence_scores: Dict[str, float] = None) -> bool:
        """更新用户档案"""
        try:
            # 获取现有档案
            current_profile = await self.get_profile(user_id)
            if not current_profile:
                # 如果档案不存在，创建新档案
                return await self.create_profile(user_id, new_data)
            
            # 智能合并档案数据
            merged_profile = self._deep_merge_profiles(current_profile, new_data)
            
            # 计算置信度分数
            if confidence_scores is None:
                confidence_scores = self._calculate_confidence_scores(new_data)
            
            # 保存到数据库
            success = await self.db.update_user_profile(user_id, merged_profile, confidence_scores)
            
            if success:
                # 更新缓存
                await self._cache_profile(user_id, merged_profile)
                # 清除旧缓存
                await self._invalidate_cache(user_id)
                logger.info(f"Updated profile for user {user_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to update profile for user {user_id}: {e}")
            return False
    
    async def extract_and_update_from_conversation(self, user_id: str, user_input: str, 
                                                 ai_response: str) -> bool:
        """从对话中提取信息并更新档案"""
        try:
            # 使用简单的关键词提取（后续可以集成AI提取）
            extracted_info = self._extract_info_from_text(user_input)
            
            if not extracted_info:
                return True  # 没有新信息，但不算失败
            
            # 更新档案
            return await self.update_profile(user_id, extracted_info)
            
        except Exception as e:
            logger.error(f"Failed to extract and update profile for user {user_id}: {e}")
            return False
    
    def calculate_profile_completeness(self, profile_data: Dict[str, Any]) -> Tuple[float, Dict[str, float]]:
        """计算档案完整度"""
        try:
            section_completeness = {}
            total_fields = 0
            completed_fields = 0
            
            for section_name, schema in self.profile_schema.items():
                section_data = profile_data.get(section_name, {})
                
                # 计算该部分的完整度
                required_fields = schema["required_fields"]
                optional_fields = schema["optional_fields"]
                all_fields = required_fields + optional_fields
                
                if not all_fields:
                    section_completeness[section_name] = 1.0
                    continue
                
                completed_in_section = sum(1 for field in all_fields 
                                         if section_data.get(field))
                section_completeness[section_name] = completed_in_section / len(all_fields)
                
                # 累计总体统计
                total_fields += len(all_fields)
                completed_fields += completed_in_section
            
            overall_completeness = completed_fields / total_fields if total_fields > 0 else 0.0
            
            return overall_completeness, section_completeness
            
        except Exception as e:
            logger.error(f"Failed to calculate profile completeness: {e}")
            return 0.0, {}
    
    def get_missing_required_fields(self, profile_data: Dict[str, Any]) -> List[str]:
        """获取缺失的必须字段"""
        missing_fields = []
        
        for section_name, schema in self.profile_schema.items():
            section_data = profile_data.get(section_name, {})
            required_fields = schema["required_fields"]
            
            for field in required_fields:
                if not section_data.get(field):
                    missing_fields.append(f"{section_name}.{field}")
        
        return missing_fields
    
    def _initialize_empty_profile(self) -> Dict[str, Any]:
        """初始化空档案结构"""
        profile = {}
        for section_name in self.profile_schema.keys():
            profile[section_name] = {}
        return profile
    
    def _deep_merge_profiles(self, base_profile: Dict[str, Any], 
                           new_data: Dict[str, Any]) -> Dict[str, Any]:
        """深度合并档案数据"""
        merged = deepcopy(base_profile)
        
        for section_name, section_data in new_data.items():
            if section_name not in merged:
                merged[section_name] = {}
            
            if isinstance(section_data, dict):
                for field_name, field_value in section_data.items():
                    if field_value is not None and field_value != "":
                        # 处理列表类型的合并
                        if isinstance(field_value, list):
                            existing_list = merged[section_name].get(field_name, [])
                            if isinstance(existing_list, list):
                                # 合并列表，去重
                                combined_list = existing_list + field_value
                                merged[section_name][field_name] = list(set(combined_list))
                            else:
                                merged[section_name][field_name] = field_value
                        else:
                            # 直接覆盖非列表类型
                            merged[section_name][field_name] = field_value
            else:
                merged[section_name] = section_data
        
        return merged
    
    def _extract_info_from_text(self, text: str) -> Dict[str, Any]:
        """从文本中提取信息（简单版本）"""
        extracted = {}
        text_lower = text.lower()
        
        # 基本信息提取
        basic_info = {}
        
        # 年龄提取
        import re
        age_patterns = [
            r"我(\d{1,2})岁",
            r"今年(\d{1,2})",
            r"(\d{1,2})岁",
            r"i'm (\d{1,2})",
            r"i am (\d{1,2})"
        ]
        for pattern in age_patterns:
            match = re.search(pattern, text_lower)
            if match:
                age = int(match.group(1))
                if 18 <= age <= 80:  # 合理年龄范围
                    basic_info["age"] = age
                    break
        
        # 职业提取
        occupation_keywords = {
            "程序员": "程序员", "软件工程师": "软件工程师", "开发": "软件开发",
            "医生": "医生", "护士": "护士", "教师": "教师", "老师": "教师",
            "销售": "销售", "市场": "市场营销", "设计师": "设计师",
            "会计": "会计", "律师": "律师", "工程师": "工程师"
        }
        for keyword, occupation in occupation_keywords.items():
            if keyword in text_lower:
                basic_info["occupation"] = occupation
                break
        
        # 城市提取
        city_keywords = [
            "北京", "上海", "广州", "深圳", "杭州", "南京", "武汉", "成都", "西安", "重庆"
        ]
        for city in city_keywords:
            if city in text:
                basic_info["city"] = city
                break
        
        if basic_info:
            extracted["basic_info"] = basic_info
        
        # 兴趣爱好提取
        interests = {}
        hobby_keywords = {
            "编程": "编程", "读书": "阅读", "看书": "阅读", "运动": "运动",
            "游泳": "游泳", "跑步": "跑步", "爬山": "爬山", "旅游": "旅游",
            "音乐": "音乐", "电影": "电影", "摄影": "摄影", "画画": "绘画"
        }
        hobbies = []
        for keyword, hobby in hobby_keywords.items():
            if keyword in text:
                hobbies.append(hobby)
        
        if hobbies:
            interests["hobbies"] = hobbies
            extracted["interests"] = interests
        
        return extracted
    
    def _calculate_confidence_scores(self, new_data: Dict[str, Any]) -> Dict[str, float]:
        """计算置信度分数"""
        confidence_scores = {}
        
        for section_name, section_data in new_data.items():
            if isinstance(section_data, dict):
                for field_name, field_value in section_data.items():
                    if field_value is not None and field_value != "":
                        # 简单的置信度计算
                        confidence = 0.8  # 默认置信度
                        
                        # 根据数据类型调整置信度
                        if isinstance(field_value, list) and len(field_value) > 1:
                            confidence = 0.9  # 多个值的置信度更高
                        elif isinstance(field_value, str) and len(field_value) > 10:
                            confidence = 0.85  # 详细描述的置信度更高
                        
                        confidence_scores[f"{section_name}.{field_name}"] = confidence
        
        return confidence_scores
    
    async def _cache_profile(self, user_id: str, profile_data: Dict[str, Any]):
        """缓存档案到Redis"""
        if self.redis:
            try:
                await self.redis.setex(
                    f"profile:{user_id}",
                    self.profile_cache_ttl,
                    json.dumps(profile_data, default=str)
                )
            except Exception as e:
                logger.warning(f"Failed to cache profile for user {user_id}: {e}")
    
    async def _get_cached_profile(self, user_id: str) -> Optional[Dict[str, Any]]:
        """从Redis获取缓存的档案"""
        if self.redis:
            try:
                cached_data = await self.redis.get(f"profile:{user_id}")
                if cached_data:
                    return json.loads(cached_data)
            except Exception as e:
                logger.warning(f"Failed to get cached profile for user {user_id}: {e}")
        return None
    
    async def _invalidate_cache(self, user_id: str):
        """清除档案缓存"""
        if self.redis:
            try:
                await self.redis.delete(f"profile:{user_id}")
            except Exception as e:
                logger.warning(f"Failed to invalidate cache for user {user_id}: {e}")
