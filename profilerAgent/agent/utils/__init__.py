"""
Utils module for AI Dating Agent MVP
Contains shared utilities and data models
"""

from .data_models import (
    # 枚举类型
    UserStatus, VerificationStatus, UserPermission, ConversationStage,
    VerificationLevel, EventType,

    # 核心数据模型
    User, ConversationContext, AnalysisResult, ProfileCard,
    VoiceAnalysis, LinkedInProfile, MatchResult, VerificationResult,

    # 事件和反馈模型
    EventData, UserFeedback,

    # API和请求模型
    APIResponse, UserRegistrationRequest, SMSVerificationRequest,
    LoginRequest, UserPreferences, SMSVerification,

    # 权限配置
    USER_PERMISSIONS, VERIFICATION_PERMISSIONS
)

__all__ = [
    # 枚举类型
    'UserStatus', 'VerificationStatus', 'UserPermission', 'ConversationStage',
    'VerificationLevel', 'EventType',

    # 核心数据模型
    'User', 'ConversationContext', 'AnalysisResult', 'ProfileCard',
    'VoiceAnalysis', 'LinkedInProfile', 'MatchResult', 'VerificationResult',

    # 事件和反馈模型
    'EventData', 'UserFeedback',

    # API和请求模型
    'APIResponse', 'UserRegistrationRequest', 'SMSVerificationRequest',
    'LoginRequest', 'UserPreferences', 'SMSVerification',

    # 权限配置
    'USER_PERMISSIONS', 'VERIFICATION_PERMISSIONS'
]
