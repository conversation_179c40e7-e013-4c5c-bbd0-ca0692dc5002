"""
DataModels - 统一数据模型
定义所有核心数据结构，为MVP系统提供数据基础
"""

from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, List, Optional, Any
import uuid

# ============ 枚举定义 ============

class UserStatus(Enum):
    """用户状态枚举 - 匹配数据库status字段"""
    ACTIVE = "active"          # 活跃用户
    INACTIVE = "inactive"      # 非活跃状态 - 长期未使用
    SUSPENDED = "suspended"    # 暂停状态 - 违规或其他原因暂停

class VerificationStatus(Enum):
    """验证状态枚举 - 匹配数据库verification_status字段"""
    PENDING = "pending"                    # 待验证状态 - 刚注册
    SMS_VERIFIED = "sms_verified"          # SMS验证完成
    VOICE_COMPLETED = "voice_completed"    # 语音通话完成
    LINKEDIN_VERIFIED = "linkedin_verified" # LinkedIn验证完成
    FULLY_VERIFIED = "fully_verified"      # 完全验证

class UserPermission(Enum):
    """用户权限枚举"""
    CHAT_ACCESS = "chat_access"           # 基础聊天权限
    VOICE_ACCESS = "voice_access"         # 语音功能权限
    MATCHING_ACCESS = "matching_access"   # 匹配服务权限
    PROFILE_ACCESS = "profile_access"     # 画像查看权限
    LINKEDIN_VERIFY = "linkedin_verify"   # LinkedIn验证权限

class ConversationStage(Enum):
    """对话阶段枚举"""
    REGISTRATION = "registration"         # 注册阶段
    VOICE_PENDING = "voice_pending"       # 等待语音通话阶段
    VOICE_COMPLETED = "voice_completed"   # 语音通话完成阶段
    LINKEDIN_REQUEST = "linkedin_request" # 请求LinkedIn验证阶段
    PROFILE_COMPLETE = "profile_complete" # 档案完成阶段
    MATCHING_ACTIVE = "matching_active"   # 匹配服务激活阶段

class VerificationLevel(Enum):
    """验证级别枚举"""
    UNVERIFIED = "unverified"     # 未验证
    PARTIAL = "partial"           # 部分验证
    VERIFIED = "verified"         # 已验证
    HIGH_TRUST = "high_trust"     # 高信任度

class EventType(Enum):
    """事件类型枚举"""
    USER_CREATED = "user.created"
    VOICE_COMPLETED = "user.voice_completed"
    LINKEDIN_VERIFIED = "user.linkedin_verified"
    MATCH_GENERATED = "match.generated"
    FEEDBACK_RECEIVED = "feedback.received"

class ProfileCardType(Enum):
    """画像卡片类型枚举 - 匹配MVP设计的5种核心卡片"""
    PERSONALITY_MBTI = "personality_mbti"      # 性格特质卡 (MBTI核心)
    INTERESTS = "interests"                    # 兴趣爱好卡
    LIFESTYLE = "lifestyle"                    # 生活方式卡
    SOCIAL_STYLE = "social_style"              # 社交风格卡
    RELATIONSHIP_GOALS = "relationship_goals"  # 关系期待卡

# ============ 核心数据模型 ============

@dataclass
class User:
    """用户数据模型 - 核心用户信息"""
    user_id: str
    phone_number: str
    email: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    age: Optional[int] = None
    city: Optional[str] = None
    profession: Optional[str] = None

    # 状态字段 - 匹配数据库
    status: UserStatus = UserStatus.ACTIVE
    sms_verified: bool = False
    voice_call_completed: bool = False
    linkedin_verified: bool = False
    verification_status: VerificationStatus = VerificationStatus.PENDING
    verification_level: VerificationLevel = VerificationLevel.UNVERIFIED

    # 时间戳 - 匹配数据库
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    last_login: datetime = field(default_factory=datetime.now)

    # 对话状态 (Agent内部使用，不存储到数据库)
    current_conversation_stage: ConversationStage = ConversationStage.REGISTRATION
    message_count: int = 0
    last_interaction: datetime = field(default_factory=datetime.now)

    # 元数据
    metadata: Dict[str, Any] = field(default_factory=dict)

    @classmethod
    def create_pending_user(cls, phone_number: str, first_name: str, last_name: str,
                           age: Optional[int] = None, city: Optional[str] = None,
                           profession: Optional[str] = None, email: Optional[str] = None) -> 'User':
        """创建待验证用户"""
        return cls(
            user_id=str(uuid.uuid4()),
            phone_number=phone_number,
            first_name=first_name,
            last_name=last_name,
            age=age,
            city=city,
            profession=profession,
            email=email,
            status=UserStatus.ACTIVE,
            verification_status=VerificationStatus.PENDING
        )

    def verify_sms(self) -> None:
        """完成SMS验证"""
        self.sms_verified = True
        self.verification_status = VerificationStatus.SMS_VERIFIED
        self.current_conversation_stage = ConversationStage.VOICE_PENDING
        self.updated_at = datetime.now()

    def complete_voice_call(self) -> None:
        """完成语音通话"""
        self.voice_call_completed = True
        self.verification_status = VerificationStatus.VOICE_COMPLETED
        self.current_conversation_stage = ConversationStage.VOICE_COMPLETED
        self.verification_level = VerificationLevel.PARTIAL
        self.updated_at = datetime.now()

    def verify_linkedin(self) -> None:
        """完成LinkedIn验证"""
        self.linkedin_verified = True
        self.verification_status = VerificationStatus.LINKEDIN_VERIFIED
        self.verification_level = VerificationLevel.VERIFIED
        self.current_conversation_stage = ConversationStage.PROFILE_COMPLETE
        self.updated_at = datetime.now()

    def complete_full_verification(self) -> None:
        """完成完全验证"""
        self.verification_status = VerificationStatus.FULLY_VERIFIED
        self.verification_level = VerificationLevel.VERIFIED
        self.current_conversation_stage = ConversationStage.MATCHING_ACTIVE
        self.updated_at = datetime.now()

    def upgrade_to_registered(self) -> None:
        """升级为注册用户 - 兼容性方法"""
        # 这个方法在user_manager中被调用，但User模型中没有定义
        # 实际上应该基于验证状态来判断，这里提供兼容性实现
        if self.sms_verified:
            self.status = UserStatus.ACTIVE
            self.updated_at = datetime.now()

    def reset_voice_call_status(self) -> None:
        """重置语音通话状态"""
        self.voice_call_completed = False
        if self.verification_status == VerificationStatus.VOICE_COMPLETED:
            self.verification_status = VerificationStatus.SMS_VERIFIED
            self.current_conversation_stage = ConversationStage.VOICE_PENDING
        self.updated_at = datetime.now()

@dataclass
class SMSVerification:
    """SMS验证数据模型"""
    verification_id: str
    phone_number: str
    verification_code: str
    expires_at: datetime
    verified_at: Optional[datetime] = None
    attempts: int = 0
    created_at: datetime = field(default_factory=datetime.now)

    @classmethod
    def create_verification(cls, phone_number: str, code: str, expire_minutes: int = 5) -> 'SMSVerification':
        """创建SMS验证"""
        from datetime import timezone
        # 使用UTC时间创建过期时间，确保时区一致性
        now_utc = datetime.now(timezone.utc)
        expires_at_utc = now_utc + timedelta(minutes=expire_minutes)

        return cls(
            verification_id=str(uuid.uuid4()),
            phone_number=phone_number,
            verification_code=code,
            expires_at=expires_at_utc,
            created_at=now_utc
        )

    def is_expired(self) -> bool:
        """检查是否过期"""
        import logging
        from datetime import timezone
        logger = logging.getLogger(__name__)

        # 获取当前UTC时间
        now_utc = datetime.now(timezone.utc)
        expires_at = self.expires_at

        logger.info(f"is_expired() DEBUG:")
        logger.info(f"  Current time (UTC): {now_utc}")
        logger.info(f"  Expires at: {expires_at} (tzinfo: {expires_at.tzinfo})")

        # 确保expires_at有时区信息，如果没有则假设为UTC
        if expires_at.tzinfo is None:
            expires_at = expires_at.replace(tzinfo=timezone.utc)
            logger.info(f"  Expires at (with UTC): {expires_at}")

        # 直接比较带时区的datetime对象
        result = now_utc > expires_at
        logger.info(f"  Comparison: {now_utc} > {expires_at} = {result}")

        return result

    def is_verified(self) -> bool:
        """检查是否已验证"""
        return self.verified_at is not None

    def verify(self, code: str) -> bool:
        """验证代码"""
        import logging
        from datetime import timezone
        logger = logging.getLogger(__name__)

        self.attempts += 1
        is_expired = self.is_expired()
        code_matches = self.verification_code == code

        logger.info(f"SMS Verification Debug - Phone: {self.phone_number}")
        logger.info(f"  Input code: '{code}' (type: {type(code)})")
        logger.info(f"  Stored code: '{self.verification_code}' (type: {type(self.verification_code)})")
        logger.info(f"  Code matches: {code_matches}")
        logger.info(f"  Is expired: {is_expired}")
        logger.info(f"  Expires at: {self.expires_at}")
        logger.info(f"  Attempts: {self.attempts}")

        if not is_expired and code_matches:
            # 使用UTC时间记录验证时间
            self.verified_at = datetime.now(timezone.utc)
            logger.info(f"  Verification SUCCESS at {self.verified_at}")
            return True
        else:
            logger.info(f"  Verification FAILED")
            return False

@dataclass
class ConversationContext:
    """对话上下文数据模型"""
    user_id: str
    current_stage: ConversationStage
    collected_info: Dict[str, Any] = field(default_factory=dict)
    stage_attempts: Dict[str, int] = field(default_factory=dict)
    last_interaction: datetime = field(default_factory=datetime.now)

    def add_message(self, role: str, content: str) -> None:
        """添加消息到历史"""
        # 修复：ConversationContext没有conversation_history和message_count字段
        # 这个方法应该在UserManager中处理，这里只更新最后交互时间
        self.last_interaction = datetime.now()

@dataclass
class AnalysisResult:
    """分析结果数据模型"""
    user_id: str
    analysis_type: str  # 'conversation', 'voice', 'combined'
    mbti_type: Optional[str] = None
    mbti_dimensions: Dict[str, float] = field(default_factory=dict)  # E/I, S/N, T/F, J/P
    personality_traits: Dict[str, float] = field(default_factory=dict)
    interests: List[str] = field(default_factory=list)
    professional_info: Dict[str, Any] = field(default_factory=dict)
    relationship_preferences: Dict[str, Any] = field(default_factory=dict)
    confidence_scores: Dict[str, float] = field(default_factory=dict)
    raw_analysis: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)

    def get_overall_confidence(self) -> float:
        """获取整体置信度"""
        if not self.confidence_scores:
            return 0.0
        return sum(self.confidence_scores.values()) / len(self.confidence_scores)

@dataclass
class ProfileCard:
    """用户画像卡片数据模型"""
    card_id: str
    user_id: str
    card_type: str  # 'personality_mbti', 'interests', 'lifestyle', 'social_style', 'relationship_goals'
    title: str
    description: str
    confidence: float
    tags: List[str] = field(default_factory=list)
    evidence: List[str] = field(default_factory=list)
    data_sources: List[str] = field(default_factory=list)  # 'conversation', 'voice', 'linkedin'
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)

    @classmethod
    def create_card(cls, user_id: str, card_type: str, title: str,
                   description: str, confidence: float) -> 'ProfileCard':
        """创建新的画像卡片"""
        return cls(
            card_id=str(uuid.uuid4()),
            user_id=user_id,
            card_type=card_type,
            title=title,
            description=description,
            confidence=confidence
        )

@dataclass
class VoiceAnalysis:
    """语音分析数据模型"""
    user_id: str
    call_sid: str
    call_duration: int  # 秒
    transcript: str
    analysis_data: Dict[str, Any] = field(default_factory=dict)
    quality_score: float = 0.0
    questions_asked: int = 0
    responses_given: int = 0
    conversation_stages: List[str] = field(default_factory=list)
    timestamp: datetime = field(default_factory=datetime.now)

    def calculate_engagement_score(self) -> float:
        """计算参与度分数"""
        if self.questions_asked == 0:
            return 0.0
        response_rate = self.responses_given / self.questions_asked
        duration_score = min(1.0, self.call_duration / 300)  # 5分钟为满分
        return (response_rate + duration_score) / 2

@dataclass
class LinkedInProfile:
    """LinkedIn资料数据模型"""
    user_id: str
    linkedin_url: str
    profile_data: Dict[str, Any] = field(default_factory=dict)
    verification_status: str = "pending"  # pending, verified, failed
    consistency_score: float = 0.0
    consistency_details: Dict[str, Any] = field(default_factory=dict)
    scraped_at: datetime = field(default_factory=datetime.now)
    verified_at: Optional[datetime] = None

    # 提取的关键信息
    name: Optional[str] = None
    current_position: Optional[str] = None
    current_company: Optional[str] = None
    location: Optional[str] = None
    industry: Optional[str] = None
    experience_level: Optional[str] = None
    skills: List[str] = field(default_factory=list)
    education: List[Dict[str, Any]] = field(default_factory=list)

    def is_verified(self) -> bool:
        """检查是否已验证"""
        return self.verification_status == "verified" and self.consistency_score >= 0.6

@dataclass
class MatchResult:
    """匹配结果数据模型"""
    match_id: str
    user1_id: str
    user2_id: str
    match_score: float
    compatibility_breakdown: Dict[str, float] = field(default_factory=dict)
    recommendation_reason: str = ""
    match_explanation: str = ""
    status: str = "recommended"  # recommended, interested, matched, passed
    created_at: datetime = field(default_factory=datetime.now)

    # 详细兼容性分析
    mbti_compatibility: float = 0.0
    interest_overlap: float = 0.0
    location_compatibility: float = 0.0
    professional_compatibility: float = 0.0

    @classmethod
    def create_match(cls, user1_id: str, user2_id: str, match_score: float) -> 'MatchResult':
        """创建新的匹配结果"""
        return cls(
            match_id=str(uuid.uuid4()),
            user1_id=user1_id,
            user2_id=user2_id,
            match_score=match_score
        )

# ============ 事件和反馈模型 ============

@dataclass
class EventData:
    """事件数据模型 - 用于简单事件系统"""
    event_type: EventType
    user_id: str
    data: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)
    event_id: str = field(default_factory=lambda: str(uuid.uuid4()))

    @classmethod
    def create_voice_completed(cls, user_id: str, voice_data: Dict[str, Any]) -> 'EventData':
        """创建语音完成事件"""
        return cls(
            event_type=EventType.VOICE_COMPLETED,
            user_id=user_id,
            data=voice_data
        )

    @classmethod
    def create_linkedin_verified(cls, user_id: str, verification_data: Dict[str, Any]) -> 'EventData':
        """创建LinkedIn验证事件"""
        return cls(
            event_type=EventType.LINKEDIN_VERIFIED,
            user_id=user_id,
            data=verification_data
        )

@dataclass
class UserFeedback:
    """用户反馈数据模型"""
    feedback_id: str
    user_id: str
    card_id: str
    feedback_type: str  # 'accurate', 'inaccurate', 'partially_correct'
    rating: Optional[int] = None  # 1-5 星级评分
    comment: Optional[str] = None
    confidence_adjustment: float = 0.0  # -1.0 到 1.0
    timestamp: datetime = field(default_factory=datetime.now)

    @classmethod
    def create_feedback(cls, user_id: str, card_id: str, feedback_type: str) -> 'UserFeedback':
        """创建用户反馈"""
        return cls(
            feedback_id=str(uuid.uuid4()),
            user_id=user_id,
            card_id=card_id,
            feedback_type=feedback_type
        )

# ============ API响应模型 ============

@dataclass
class APIResponse:
    """API响应数据模型 - 标准化API返回格式"""
    success: bool
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    error_code: Optional[str] = None
    timestamp: datetime = field(default_factory=datetime.now)
    request_id: str = field(default_factory=lambda: str(uuid.uuid4()))

    @classmethod
    def success_response(cls, data: Dict[str, Any]) -> 'APIResponse':
        """创建成功响应"""
        return cls(success=True, data=data)

    @classmethod
    def error_response(cls, error: str, error_code: Optional[str] = None) -> 'APIResponse':
        """创建错误响应"""
        return cls(success=False, error=error, error_code=error_code)

@dataclass
class UserRegistrationRequest:
    """用户注册请求数据模型"""
    phone_number: str
    first_name: str
    last_name: str
    age: Optional[int] = None
    city: Optional[str] = None
    profession: Optional[str] = None
    email: Optional[str] = None

@dataclass
class SMSVerificationRequest:
    """SMS验证请求数据模型"""
    phone_number: str
    verification_code: str

@dataclass
class LoginRequest:
    """登录请求数据模型"""
    phone_number: str
    verification_code: Optional[str] = None  # 用于SMS登录

@dataclass
class UserPreferences:
    """用户偏好设置数据模型"""
    user_id: str
    age_range_min: int = 18
    age_range_max: int = 45
    max_distance: int = 50  # 公里
    preferred_mbti_types: List[str] = field(default_factory=list)
    deal_breakers: List[str] = field(default_factory=list)
    interests: List[str] = field(default_factory=list)
    updated_at: datetime = field(default_factory=datetime.now)



# ============ 权限映射配置 ============

# 用户状态到权限的映射 - 修复映射键类型
USER_PERMISSIONS = {
    UserStatus.ACTIVE: [
        UserPermission.CHAT_ACCESS,
        UserPermission.VOICE_ACCESS,
        UserPermission.MATCHING_ACCESS,
        UserPermission.PROFILE_ACCESS,
        UserPermission.LINKEDIN_VERIFY,
    ],
    UserStatus.INACTIVE: [
        UserPermission.CHAT_ACCESS,
    ],
    UserStatus.SUSPENDED: [
        # 暂停用户没有权限
    ]
}

# 验证状态到权限的映射 - 保留原有逻辑但重命名
VERIFICATION_PERMISSIONS = {
    VerificationStatus.PENDING: [
        UserPermission.CHAT_ACCESS,
    ],
    VerificationStatus.SMS_VERIFIED: [
        UserPermission.CHAT_ACCESS,
        UserPermission.VOICE_ACCESS,
    ],
    VerificationStatus.VOICE_COMPLETED: [
        UserPermission.CHAT_ACCESS,
        UserPermission.VOICE_ACCESS,
        UserPermission.MATCHING_ACCESS,
        UserPermission.PROFILE_ACCESS,
        UserPermission.LINKEDIN_VERIFY,
    ],
    VerificationStatus.LINKEDIN_VERIFIED: [
        UserPermission.CHAT_ACCESS,
        UserPermission.VOICE_ACCESS,
        UserPermission.MATCHING_ACCESS,
        UserPermission.PROFILE_ACCESS,
        UserPermission.LINKEDIN_VERIFY,
    ],
    VerificationStatus.FULLY_VERIFIED: [
        UserPermission.CHAT_ACCESS,
        UserPermission.VOICE_ACCESS,
        UserPermission.MATCHING_ACCESS,
        UserPermission.PROFILE_ACCESS,
        UserPermission.LINKEDIN_VERIFY,
    ]
}

# 移除重复的LinkedInProfile定义 - 使用上面第277行的版本

@dataclass
class VerificationResult:
    """LinkedIn验证结果数据模型"""
    user_id: str
    linkedin_url: str
    overall_score: float
    is_verified: bool
    consistency_breakdown: Dict[str, float]
    verification_details: Dict[str, Any]
    verified_at: datetime

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "user_id": self.user_id,
            "linkedin_url": self.linkedin_url,
            "overall_score": self.overall_score,
            "is_verified": self.is_verified,
            "consistency_breakdown": self.consistency_breakdown,
            "verification_details": self.verification_details,
            "verified_at": self.verified_at.isoformat()
        }
