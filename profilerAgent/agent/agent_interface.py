"""
AgentInterface - Core AI Conversation Interface
Focused on AI conversation logic, not handling Web API
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime

from .core.database_manager import DatabaseManager
from .core.registration_state import RegistrationStateManager
from .core.profile_manager import ProfileManager
from .core.memory_manager import MemoryManager
from .services.unified_agent import UnifiedAgent, UnifiedAgentResponse

logger = logging.getLogger(__name__)

class AgentInterface:
    """AI Agent Core Conversation Interface - Focused on AI Logic"""

    def __init__(self, db_manager=None, registration_manager=None,
                 profile_manager=None, memory_manager=None, redis_client=None):
        """Initialize Agent Interface"""
        self.db_manager = db_manager or DatabaseManager()
        self.registration_manager = registration_manager
        self.profile_manager = profile_manager
        self.memory_manager = memory_manager
        self.redis_client = redis_client

        # Initialize unified AI Agent
        self.unified_agent = None

        logger.info("AgentInterface initialized for AI conversation")

    async def initialize(self):
        """Async initialization"""
        try:
            # Ensure database connection
            if not self.db_manager.is_connected:
                await self.db_manager.connect()

            # Initialize unified AI Agent
            self.unified_agent = UnifiedAgent(
                db_manager=self.db_manager,
                registration_manager=self.registration_manager,
                profile_manager=self.profile_manager,
                memory_manager=self.memory_manager
            )

            logger.info("AgentInterface initialization completed")
            return True

        except Exception as e:
            logger.error(f"AgentInterface initialization failed: {e}")
            return False

    # ============ Core Conversation Interface ============

    async def process_user_message(self, user_id: str, message: str,
                                 conversation_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Process user message - Core conversation interface

        Args:
            user_id: User ID
            message: User message
            conversation_context: Conversation context

        Returns:
            Dict containing AI response and status information
        """
        try:
            if not self.unified_agent:
                await self.initialize()

            # Get or create conversation context
            if conversation_context is None:
                conversation_context = await self._get_conversation_context(user_id)

            # Process message
            response = await self.unified_agent.process_input(
                user_id=user_id,
                user_input=message
            )

            # Save conversation record
            await self._save_conversation_turn(user_id, message, response)

            return {
                "success": True,
                "response": response.content,
                "should_continue": response.should_continue,
                "agent_type": response.agent_type,
                "registration_complete": response.registration_complete,
                "progress": response.progress,
                "updated_info": response.updated_info
            }

        except Exception as e:
            logger.error(f"Message processing failed for user {user_id}: {e}")
            return {
                "success": False,
                "error": str(e),
                "response": "Sorry, I encountered some technical issues. Please try again later."
            }

    async def start_conversation(self, user_id: str, phone_number: str = None) -> Dict[str, Any]:
        """
        Start new conversation

        Args:
            user_id: User ID
            phone_number: User phone number (optional)

        Returns:
            Dict containing welcome message and initial status
        """
        try:
            if not self.unified_agent:
                await self.initialize()

            # Get or create user
            await self._get_or_create_user(user_id, phone_number)

            # Start conversation
            response = await self.unified_agent.start_conversation(user_id, phone_number)

            return {
                "success": True,
                "welcome_message": response.content,
                "agent_type": response.agent_type,
                "should_continue": response.should_continue,
                "registration_complete": response.registration_complete,
                "is_new_user": response.agent_type == "registration"
            }

        except Exception as e:
            logger.error(f"Conversation start failed for user {user_id}: {e}")
            return {
                "success": False,
                "error": str(e),
                "welcome_message": "Hello! I'm your AI assistant, nice to meet you!"
            }

    async def end_conversation(self, user_id: str, reason: str = "user_ended") -> Dict[str, Any]:
        """
        End conversation

        Args:
            user_id: User ID
            reason: End reason

        Returns:
            Dict containing end status and summary
        """
        try:
            # Get user status and profile completion
            profile = None
            if self.profile_manager:
                profile = await self.profile_manager.get_profile(user_id)

            profile_completion = self._calculate_profile_completion(profile) if profile else 0.0

            # Record conversation end
            await self.db_manager.save_conversation_message({
                "user_id": user_id,
                "user_message": f"[CONVERSATION_ENDED: {reason}]",
                "ai_response": "Thank you for your time! Looking forward to chatting again.",
                "timestamp": datetime.now()
            })

            return {
                "success": True,
                "farewell_message": "Thank you for your time! Looking forward to chatting again.",
                "reason": reason,
                "profile_completion": profile_completion
            }

        except Exception as e:
            logger.error(f"Conversation end failed for user {user_id}: {e}")
            return {
                "success": False,
                "error": str(e),
                "farewell_message": "Thank you for your time! Looking forward to chatting again."
            }

    # ============ Status Query Interface ============

    async def get_user_status(self, user_id: str) -> Dict[str, Any]:
        """Get user status"""
        try:
            # Get registration status
            registration_status = None
            if self.registration_manager:
                registration_status = await self.registration_manager.get_registration_status(user_id)

            # Get profile information
            profile = None
            if self.profile_manager:
                profile = await self.profile_manager.get_profile(user_id)

            # Get conversation history
            conversation_history = await self._get_recent_conversation_history(user_id)

            return {
                "success": True,
                "user_id": user_id,
                "registration_status": registration_status,
                "profile_completion": self._calculate_profile_completion(profile) if profile else 0,
                "conversation_count": len(conversation_history),
                "last_conversation": conversation_history[0] if conversation_history else None
            }

        except Exception as e:
            logger.error(f"Status retrieval failed for user {user_id}: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def get_conversation_history(self, user_id: str, limit: int = 10) -> Dict[str, Any]:
        """Get conversation history"""
        try:
            history = await self._get_recent_conversation_history(user_id, limit)

            return {
                "success": True,
                "user_id": user_id,
                "conversation_history": history,
                "total_messages": len(history)
            }

        except Exception as e:
            logger.error(f"Conversation history retrieval failed for user {user_id}: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    # ============ Internal Helper Methods ============

    async def _get_conversation_context(self, user_id: str) -> Dict[str, Any]:
        """Get conversation context"""
        try:
            context = {}

            # Get user basic information
            user = await self.db_manager.get_user(user_id)
            if user:
                context["user_info"] = user

            # Get registration progress
            if self.registration_manager:
                registration_progress = await self.registration_manager.get_registration_progress(user_id)
                context["registration_progress"] = registration_progress

            # Get short-term memory
            if self.memory_manager:
                short_term_memory = await self.memory_manager.get_short_term_memory(user_id)
                context["recent_messages"] = short_term_memory

            return context

        except Exception as e:
            logger.error(f"Failed to get conversation context for user {user_id}: {e}")
            return {}

    async def _get_or_create_user(self, user_id: str, phone_number: str = None) -> Optional[Dict[str, Any]]:
        """Get or create user"""
        try:
            # Try to get existing user
            user = await self.db_manager.get_user(user_id)

            if not user and phone_number:
                # Create new user
                success = await self.db_manager.create_user(user_id, phone_number)
                if success:
                    user = {
                        "user_id": user_id,
                        "phone_number": phone_number,
                        "created_at": datetime.now(),
                        "status": "active"
                    }

                    # Initialize user profile
                    if self.profile_manager:
                        await self.profile_manager.create_profile(user_id)

            return user

        except Exception as e:
            logger.error(f"Failed to get or create user {user_id}: {e}")
            return None

    async def _save_conversation_turn(self, user_id: str, user_message: str,
                                    ai_response: UnifiedAgentResponse):
        """Save conversation turn"""
        try:
            # Save to database
            conversation_data = {
                "user_id": user_id,
                "user_message": user_message,
                "ai_response": ai_response.content,
                "timestamp": datetime.now(),
                "agent_type": ai_response.agent_type,
                "updated_info": ai_response.updated_info
            }

            await self.db_manager.save_conversation_message(conversation_data)

            # Update memory
            if self.memory_manager:
                await self.memory_manager.add_conversation_turn(
                    user_id, user_message, ai_response.content
                )

        except Exception as e:
            logger.error(f"Failed to save conversation turn for user {user_id}: {e}")

    async def _get_recent_conversation_history(self, user_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent conversation history"""
        try:
            messages = await self.db_manager.get_conversation_messages(user_id, limit)
            return messages or []

        except Exception as e:
            logger.error(f"Failed to get conversation history for user {user_id}: {e}")
            return []

    def _calculate_profile_completion(self, profile: Dict[str, Any]) -> float:
        """Calculate profile completion"""
        if not profile:
            return 0.0

        try:
            # Basic information fields
            basic_fields = ["name", "age", "city", "occupation"]
            basic_completed = sum(1 for field in basic_fields if profile.get("basic_info", {}).get(field))
            basic_score = basic_completed / len(basic_fields)

            # Extended information fields
            extended_fields = ["interests", "personality", "lifestyle", "relationship_goals"]
            extended_completed = sum(1 for field in extended_fields if profile.get(field))
            extended_score = extended_completed / len(extended_fields)

            # Total completion (basic info weight 70%, extended info weight 30%)
            total_score = (basic_score * 0.7) + (extended_score * 0.3)

            return round(total_score, 2)

        except Exception as e:
            logger.error(f"Failed to calculate profile completion: {e}")
            return 0.0

    # ============ System Management Interface ============

    async def health_check(self) -> Dict[str, Any]:
        """System health check"""
        try:
            health_status = {
                "timestamp": datetime.now().isoformat(),
                "components": {
                    "database": self.db_manager.is_connected if self.db_manager else False,
                    "unified_agent": self.unified_agent is not None,
                    "registration_manager": self.registration_manager is not None,
                    "profile_manager": self.profile_manager is not None,
                    "memory_manager": self.memory_manager is not None
                },
                "status": "healthy"
            }

            # 检查是否有组件不可用
            if not all(health_status["components"].values()):
                health_status["status"] = "degraded"

            return {
                "success": True,
                "health": health_status
            }

        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "health": {"status": "unhealthy"}
            }

    async def cleanup(self):
        """清理资源"""
        try:
            if self.db_manager:
                await self.db_manager.disconnect()

            logger.info("AgentInterface cleanup completed")

        except Exception as e:
            logger.error(f"AgentInterface cleanup failed: {e}")