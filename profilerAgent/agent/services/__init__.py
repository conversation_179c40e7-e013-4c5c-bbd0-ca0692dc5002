"""
Services module for AI Dating Agent MVP
Contains external service integrations and AI agents
"""

from .registration_agent import RegistrationAgent, RegistrationResponse
from .regular_conversation_agent import RegularConversationAgent, ConversationResponse
from .unified_agent import UnifiedAgent, UnifiedAgentResponse

# Optional imports - only import if files exist
try:
    from .sms_service import SMSService
    _SMS_AVAILABLE = True
except ImportError:
    SMSService = None
    _SMS_AVAILABLE = False

try:
    from .voice_service import VoiceService
    _VOICE_AVAILABLE = True
except ImportError:
    VoiceService = None
    _VOICE_AVAILABLE = False

try:
    from .linkedin_service import LinkedInService
    _LINKEDIN_AVAILABLE = True
except ImportError:
    LinkedInService = None
    _LINKEDIN_AVAILABLE = False

__all__ = [
    'RegistrationAgent',
    'RegistrationResponse',
    'RegularConversationAgent',
    'ConversationResponse',
    'UnifiedAgent',
    'UnifiedAgentResponse'
]

# Add optional services to __all__ if available
if _SMS_AVAILABLE:
    __all__.append('SMSService')
if _VOICE_AVAILABLE:
    __all__.append('VoiceService')
if _LINKEDIN_AVAILABLE:
    __all__.append('LinkedInService')
