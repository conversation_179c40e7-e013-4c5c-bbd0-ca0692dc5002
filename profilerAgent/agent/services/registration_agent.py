"""
RegistrationAgent - 新用户注册专用AI Agent
负责引导新用户完成首次注册对话，收集必须信息和可选信息
"""

import logging
import asyncio
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime

from ..core.registration_state import RegistrationStateManager, RegistrationStatus, RegistrationProgress
from ..core.profile_manager import ProfileManager
from ..core.memory_manager import MemoryManager
from ..core.database_manager import DatabaseManager
from .ai_service import AIService

logger = logging.getLogger(__name__)

class RegistrationResponse:
    """注册响应数据结构"""
    
    def __init__(self, content: str, should_continue: bool = True, 
                 registration_complete: bool = False, next_question: str = None,
                 collected_info: Dict[str, Any] = None, progress: float = 0.0):
        self.content = content
        self.should_continue = should_continue
        self.registration_complete = registration_complete
        self.next_question = next_question
        self.collected_info = collected_info or {}
        self.progress = progress
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "content": self.content,
            "should_continue": self.should_continue,
            "registration_complete": self.registration_complete,
            "next_question": self.next_question,
            "collected_info": self.collected_info,
            "progress": self.progress
        }

class RegistrationAgent:
    """新用户注册专用AI Agent"""
    
    def __init__(self, registration_manager: RegistrationStateManager,
                 profile_manager: ProfileManager, memory_manager: MemoryManager,
                 db_manager: DatabaseManager, ai_service: AIService = None):
        """初始化注册Agent"""
        self.registration_manager = registration_manager
        self.profile_manager = profile_manager
        self.memory_manager = memory_manager
        self.db_manager = db_manager
        self.ai_service = ai_service or AIService()
        
        # 注册配置
        self.max_attempts_per_field = 5  # 每个必须字段最多尝试5次
        self.required_completion_rate = 1.0  # 必须信息100%完成
        self.optional_completion_rate = 0.75  # 可选信息75%完成
        
        # Question templates
        self.question_templates = {
            "name": [
                "Hello! I'm your dating profile assistant. To help you find the right person, I need to learn some basic information about you. What's your name?",
                "Please tell me your name so I can serve you better.",
                "I need to know what to call you. What's your name?",
                "To complete registration, I need to know your name.",
                "I'm sorry, but to help you find suitable matches, I must know your name. What should I call you?"
            ],
            "age": [
                "How old are you?",
                "Please tell me your age.",
                "What's your age?",
                "For better matching, I need to know your age.",
                "Age information is important for matching. How old are you?"
            ],
            "city": [
                "Which city do you currently live in?",
                "Where do you live?",
                "Please tell me your city.",
                "What city are you currently in?",
                "To find people in your area, which city are you in?"
            ],
            "occupation": [
                "What do you do for work?",
                "Please tell me your occupation.",
                "What industry do you work in?",
                "What's your job?",
                "Career information helps find suitable matches. What do you do for work?"
            ]
        }
        
        logger.info("RegistrationAgent initialized")
    
    async def start_registration(self, user_id: str, phone_number: str) -> RegistrationResponse:
        """开始注册流程"""
        try:
            # 创建用户记录
            await self.db_manager.create_user(user_id, phone_number)
            
            # 创建注册进度
            progress = await self.registration_manager.create_registration(user_id, phone_number)
            
            # 更新状态为语音注册中
            await self.registration_manager.update_status(user_id, RegistrationStatus.VOICE_REGISTRATION_INCOMPLETE)
            
            # 开始第一个问题
            first_question = self._get_question_for_field("name", 0)
            
            # 保存到记忆
            await self.memory_manager.add_message(user_id, "assistant", first_question)
            
            logger.info(f"Started registration for user {user_id}")
            
            return RegistrationResponse(
                content=first_question,
                should_continue=True,
                progress=0.0
            )
            
        except Exception as e:
            logger.error(f"Failed to start registration for user {user_id}: {e}")
            return RegistrationResponse(
                content="Sorry, there's an issue with the registration system. Please try again later.",
                should_continue=False
            )
    
    async def process_registration_input(self, user_id: str, user_input: str) -> RegistrationResponse:
        """处理注册过程中的用户输入"""
        try:
            # 获取注册状态
            progress = await self.registration_manager.get_registration_state(user_id)
            if not progress:
                return RegistrationResponse(
                    content="Can't find your registration information. Please start registration again.",
                    should_continue=False
                )
            
            # 保存用户输入到记忆
            await self.memory_manager.add_message(user_id, "user", user_input)
            
            # 提取信息
            extracted_info = await self._extract_information_from_input(user_input, progress)
            
            # 更新注册进度
            await self._update_registration_progress(progress, extracted_info)
            
            # 检查是否完成注册
            if progress.is_registration_complete():
                return await self._complete_registration(user_id, progress)
            
            # 生成下一个问题
            next_response = await self._generate_next_question(progress, user_input)
            
            # 保存AI回复到记忆
            await self.memory_manager.add_message(user_id, "assistant", next_response.content)
            
            return next_response
            
        except Exception as e:
            logger.error(f"Failed to process registration input for user {user_id}: {e}")
            return RegistrationResponse(
                content="There was an issue processing your answer. Please try again.",
                should_continue=True
            )
    
    async def _extract_information_from_input(self, user_input: str, progress: RegistrationProgress) -> Dict[str, Any]:
        """从用户输入中提取信息"""
        try:
            # 使用AI服务提取结构化信息
            extraction_prompt = f"""
Extract information from the following user response:

User said: {user_input}

Please extract the following information (if available):
- Name (name)
- Age (age)
- City (city)
- Occupation (occupation)
- Interests (interests)
- Personality traits (personality)

Return JSON format:
{{
  "name": "extracted name",
  "age": extracted_age_number,
  "city": "extracted city",
  "occupation": "extracted occupation",
  "interests": ["interest1", "interest2"],
  "personality": ["trait1", "trait2"]
}}

If certain information is not available, do not include that field.
"""
            
            extracted = await self.ai_service.extract_structured_data(extraction_prompt)
            return extracted or {}
            
        except Exception as e:
            logger.warning(f"Failed to extract information from input: {e}")
            # Use simple keyword extraction as fallback
            return self._simple_extract_info(user_input)

    def _simple_extract_info(self, user_input: str) -> Dict[str, Any]:
        """Simple information extraction (fallback)"""
        extracted = {}
        text_lower = user_input.lower()

        # Age extraction
        import re
        age_patterns = [r"i am (\d{1,2})", r"(\d{1,2}) years old", r"age (\d{1,2})", r"(\d{1,2})"]
        for pattern in age_patterns:
            match = re.search(pattern, text_lower)
            if match:
                age = int(match.group(1))
                if 18 <= age <= 80:
                    extracted["age"] = age
                    break

        # City extraction
        cities = ["new york", "los angeles", "chicago", "houston", "phoenix", "philadelphia",
                 "san antonio", "san diego", "dallas", "san jose", "austin", "jacksonville",
                 "london", "paris", "tokyo", "beijing", "shanghai", "mumbai", "delhi"]
        for city in cities:
            if city in text_lower:
                extracted["city"] = city.title()
                break

        # Occupation extraction
        occupations = {
            "programmer": "Programmer", "software engineer": "Software Engineer", "engineer": "Engineer",
            "doctor": "Doctor", "nurse": "Nurse", "teacher": "Teacher", "professor": "Professor",
            "sales": "Sales", "designer": "Designer", "accountant": "Accountant", "lawyer": "Lawyer"
        }
        for keyword, occupation in occupations.items():
            if keyword in text_lower:
                extracted["occupation"] = occupation
                break
        
        return extracted
    
    async def _update_registration_progress(self, progress: RegistrationProgress, extracted_info: Dict[str, Any]):
        """更新注册进度"""
        try:
            # 更新必须信息
            for field_name in ["name", "age", "city", "occupation"]:
                if field_name in extracted_info:
                    progress.update_required_info(field_name, str(extracted_info[field_name]))
            
            # 更新可选信息
            if "interests" in extracted_info:
                progress.update_optional_info("interests", {"hobbies": extracted_info["interests"]}, 0.8)
            
            if "personality" in extracted_info:
                progress.update_optional_info("personality", {"traits": extracted_info["personality"]}, 0.7)
            
            # 保存进度
            await self.registration_manager._save_state(progress)
            
        except Exception as e:
            logger.error(f"Failed to update registration progress: {e}")
    
    async def _generate_next_question(self, progress: RegistrationProgress, user_input: str) -> RegistrationResponse:
        """生成下一个问题"""
        try:
            # 检查是否有未收集的必须信息
            next_required_field = progress.get_next_required_info()
            
            if next_required_field:
                # 获取尝试次数
                attempts = progress.required_info[next_required_field].attempts
                
                # 检查是否超过最大尝试次数
                if attempts >= self.max_attempts_per_field:
                    return RegistrationResponse(
                        content="I'm sorry, but to help you find suitable matches, I need to understand your basic information. If you're not willing to share this information, I cannot complete your registration process.",
                        should_continue=False
                    )
                
                # 增加尝试次数
                question = self._get_question_for_field(next_required_field, attempts)
                progress.increment_attempts(next_required_field, question)
                await self.registration_manager._save_state(progress)
                
                # 计算进度
                required_complete, optional_rate = progress.calculate_completion_rates()
                current_progress = (len([f for f in progress.required_info.values() if f.collected]) / 4.0) * 0.7 + optional_rate * 0.3
                
                return RegistrationResponse(
                    content=question,
                    should_continue=True,
                    progress=current_progress
                )
            
            # 必须信息已完成，收集可选信息
            missing_optional = progress.get_missing_optional_info()
            if missing_optional and len(missing_optional) > 1:  # 还需要收集更多可选信息
                return await self._ask_optional_question(progress, missing_optional[0])
            
            # 检查是否达到完成标准
            if progress.is_registration_complete():
                return await self._complete_registration(progress.user_id, progress)
            else:
                # 尝试收集更多可选信息
                return await self._ask_optional_question(progress, missing_optional[0] if missing_optional else "interests")
            
        except Exception as e:
            logger.error(f"Failed to generate next question: {e}")
            return RegistrationResponse(
                content="Let's continue learning about you. Is there anything else you'd like to share?",
                should_continue=True
            )
    
    def _get_question_for_field(self, field_name: str, attempt: int) -> str:
        """获取指定字段的问题"""
        templates = self.question_templates.get(field_name, ["Please provide the relevant information."])
        attempt_index = min(attempt, len(templates) - 1)
        return templates[attempt_index]
    
    async def _ask_optional_question(self, progress: RegistrationProgress, topic: str) -> RegistrationResponse:
        """询问可选信息"""
        questions = {
            "interests": "What are your hobbies and interests? For example, sports, music, reading, etc.",
            "personality": "How would you describe your personality? Are you more introverted or extroverted? Do you like planning or being spontaneous?",
            "relationship_goals": "What kind of relationship are you looking for? Something serious or starting as friends?",
            "lifestyle": "What's your lifestyle like? Are you busy with work?",
            "values": "What's important to you? For example, family, career, hobbies, etc."
        }

        question = questions.get(topic, "Is there anything else you'd like to share with me?")
        
        # 计算当前进度
        required_complete, optional_rate = progress.calculate_completion_rates()
        current_progress = 0.7 + optional_rate * 0.3  # 必须信息完成后从70%开始
        
        return RegistrationResponse(
            content=question,
            should_continue=True,
            progress=current_progress
        )
    
    async def _complete_registration(self, user_id: str, progress: RegistrationProgress) -> RegistrationResponse:
        """完成注册"""
        try:
            # 更新注册状态
            await self.registration_manager.update_status(user_id, RegistrationStatus.REGISTERED)
            
            # 更新用户状态
            await self.db_manager.update_user_status(user_id, "active")
            
            # 创建用户档案
            profile_data = self._build_profile_from_progress(progress)
            await self.profile_manager.create_profile(user_id, profile_data)
            
            # 记录完成
            progress.add_call_record(
                duration=int((datetime.now() - progress.created_at).total_seconds()),
                reason="registration_complete",
                progress_description="Registration completed successfully"
            )
            await self.registration_manager._save_state(progress)
            
            logger.info(f"Registration completed for user {user_id}")
            
            return RegistrationResponse(
                content="Great! I now have a comprehensive understanding of you. Registration complete! I'll use this information to help you find suitable matches.",
                should_continue=False,
                registration_complete=True,
                progress=1.0
            )
            
        except Exception as e:
            logger.error(f"Failed to complete registration for user {user_id}: {e}")
            return RegistrationResponse(
                content="There was an issue during registration, but your information has been saved. Please contact customer service to complete registration.",
                should_continue=False
            )
    
    def _build_profile_from_progress(self, progress: RegistrationProgress) -> Dict[str, Any]:
        """从注册进度构建用户档案"""
        profile_data = {
            "basic_info": {},
            "personality": {},
            "interests": {},
            "relationship": {},
            "lifestyle": {},
            "values": {}
        }
        
        # 添加必须信息
        for field_name, info in progress.required_info.items():
            if info.collected and info.value:
                if field_name == "age":
                    profile_data["basic_info"][field_name] = int(info.value)
                else:
                    profile_data["basic_info"][field_name] = info.value
        
        # 添加可选信息
        for field_name, info in progress.optional_info.items():
            if info.collected and info.data:
                profile_data[field_name].update(info.data)
        
        return profile_data
