"""
AI Service - DeepSeek集成
最简单的AI问题生成服务
"""

import asyncio
import aiohttp
import logging
from typing import Optional, Dict, Any
from ..config.service_config import ServiceConfig

logger = logging.getLogger(__name__)

class AIService:
    """AI服务 - 支持OpenAI和DeepSeek"""

    def __init__(self):
        """初始化AI服务"""
        self.provider = ServiceConfig.AI_PROVIDER
        self.timeout = ServiceConfig.AI_TIMEOUT
        self.enabled = ServiceConfig.AI_ENABLED

        if self.provider == "openai":
            self.api_key = ServiceConfig.OPENAI_API_KEY
            self.api_url = "https://api.openai.com/v1/chat/completions"
            self.model = "gpt-4o-mini"  # 使用GPT-4o-mini替代GPT-3.5-turbo
            if not self.api_key:
                logger.warning("OpenAI API key not configured, AI features disabled")
                self.enabled = False
        else:  # deepseek
            self.api_key = ServiceConfig.DEEPSEEK_API_KEY
            self.api_url = "https://api.deepseek.com/v1/chat/completions"
            self.model = "deepseek-chat"
            if not self.api_key:
                logger.warning("DeepSeek API key not configured, AI features disabled")
                self.enabled = False

        logger.info(f"AIService initialized - provider: {self.provider}, enabled: {self.enabled}")
    
    async def generate_simple_question(self, domain: str, user_response: str) -> Optional[str]:
        """
        生成简单的后续问题
        
        Args:
            domain: 当前领域 (professional/personality/interests/relationships)
            user_response: 用户的回答
            
        Returns:
            生成的问题字符串，失败时返回None
        """
        if not self.enabled:
            logger.debug("AI service disabled, returning None")
            return None
        
        try:
            logger.info(f"Generating AI question for domain: {domain}")
            logger.debug(f"User response: {user_response}")
            
            # 构建简单的prompt
            prompt = self._build_simple_prompt(domain, user_response)
            
            # 调用AI API
            question = await self._call_ai_api(prompt)
            
            if question:
                # Clean up AI response format
                question = self._clean_ai_response(question)
                
                # 自动添加问号如果缺失
                if not question.strip().endswith('?'):
                    question = question.strip() + '?'

                if self.validate_question(question):
                    logger.info(f"AI question generated: {question}")
                    return question
                else:
                    logger.warning(f"AI question validation failed: {question}")
                    return None
            else:
                logger.warning("AI returned empty question")
                return None
                
        except Exception as e:
            logger.error(f"AI question generation failed: {e}")
            return None
    
    async def extract_information(self, user_input: str, conversation_context: dict = None) -> dict:
        """Extract structured information from user input using AI"""
        if not self.enabled:
            logger.debug("AI service disabled, returning empty extraction")
            return {}
        
        try:
            prompt = self._build_extraction_prompt(user_input, conversation_context)
            result = await self._call_ai_api(prompt)
            
            if result:
                # Clean and parse JSON response
                import json
                try:
                    # Clean markdown formatting if present
                    cleaned_result = result.strip()
                    if cleaned_result.startswith('```json'):
                        cleaned_result = cleaned_result[7:]  # Remove ```json
                    if cleaned_result.endswith('```'):
                        cleaned_result = cleaned_result[:-3]  # Remove ```
                    cleaned_result = cleaned_result.strip()
                    
                    extracted_info = json.loads(cleaned_result)
                    logger.info(f"AI extracted info: {extracted_info}")
                    return extracted_info
                except json.JSONDecodeError as e:
                    logger.warning(f"AI returned non-JSON response: {result}")
                    logger.warning(f"JSON decode error: {e}")
                    return {}
            else:
                return {}
                
        except Exception as e:
            logger.error(f"AI information extraction failed: {e}")
            return {}
    
    async def analyze_emotion(self, user_input: str, conversation_history: list = None) -> dict:
        """Analyze emotional state from user input using AI"""
        if not self.enabled:
            logger.debug("AI service disabled, returning neutral emotion")
            return {"engagement": 0.5, "comfort": 0.5, "enthusiasm": 0.5}
        
        try:
            prompt = self._build_emotion_prompt(user_input, conversation_history)
            result = await self._call_ai_api(prompt)
            
            if result:
                import json
                try:
                    # Clean markdown formatting if present
                    cleaned_result = result.strip()
                    if cleaned_result.startswith('```json'):
                        cleaned_result = cleaned_result[7:]  # Remove ```json
                    if cleaned_result.endswith('```'):
                        cleaned_result = cleaned_result[:-3]  # Remove ```
                    cleaned_result = cleaned_result.strip()
                    
                    emotion_data = json.loads(cleaned_result)
                    logger.info(f"AI emotion analysis: {emotion_data}")
                    return emotion_data
                except json.JSONDecodeError as e:
                    logger.warning(f"AI returned non-JSON emotion response: {result}")
                    logger.warning(f"JSON decode error: {e}")
                    return {"engagement": 0.5, "comfort": 0.5, "enthusiasm": 0.5}
            else:
                return {"engagement": 0.5, "comfort": 0.5, "enthusiasm": 0.5}
                
        except Exception as e:
            logger.error(f"AI emotion analysis failed: {e}")
            return {"engagement": 0.5, "comfort": 0.5, "enthusiasm": 0.5}
    
    def _build_simple_prompt(self, domain: str, user_response: str) -> str:
        """构建简单的prompt"""
        domain_descriptions = {
            "professional": "their work and career",
            "personality": "their personality and how they handle situations", 
            "interests": "their hobbies and interests",
            "relationships": "their relationship values and preferences"
        }
        
        domain_desc = domain_descriptions.get(domain, "them")
        
        prompt = f"""You are a friendly interviewer for a dating app profile.

Domain: {domain}
Goal: Learn about {domain_desc}
User just said: "{user_response}"

Generate a natural, friendly follow-up question based on their response.

Requirements:
- Maximum 25 words
- Conversational and warm tone
- Must end with a question mark
- Build on what they just shared
- Avoid sensitive topics
- Return ONLY the question, no explanations or additional text

Question:"""

        return prompt
    
    def _build_extraction_prompt(self, user_input: str, conversation_context: dict = None) -> str:
        """Build prompt for information extraction"""
        context_info = ""
        if conversation_context:
            stage = conversation_context.get("conversation_stage", "unknown")
            context_info = f"Current conversation stage: {stage}\n"
        
        prompt = f"""You are an expert information extractor for a dating app conversation.

{context_info}User said: "{user_input}"

Extract relevant information and return as JSON. Only include information that is explicitly mentioned.

Expected fields:
- "name": person's name (string)
- "city": current city/location (string) 
- "profession": job title or field (string)
- "personality_traits": personality indicators (array of strings)
- "interests": hobbies, passions, activities (array of strings)
- "relationship_preferences": what they want in relationships (array of strings)

Rules:
- Only include fields that have clear information
- Don't make assumptions or inferences
- Keep arrays concise
- Return valid JSON only

JSON:"""
        return prompt
    
    def _build_emotion_prompt(self, user_input: str, conversation_history: list = None) -> str:
        """Build prompt for emotion analysis"""
        history_context = ""
        if conversation_history and len(conversation_history) > 0:
            recent = conversation_history[-2:]  # Last 2 exchanges
            history_context = "Recent conversation:\n"
            for exchange in recent:
                if exchange.get("question"):
                    history_context += f"Assistant: {exchange['question']}\n"
                if exchange.get("response"):
                    history_context += f"User: {exchange['response']}\n"
            history_context += "\n"
        
        prompt = f"""{history_context}Current user response: "{user_input}"

Analyze the user's emotional state based on their language, response length, and tone.

Return JSON with scores from 0.0 to 1.0:
- "engagement": How engaged/interested they seem (0.0 = disengaged, 1.0 = very engaged)
- "comfort": How comfortable they seem (0.0 = uncomfortable, 1.0 = very comfortable)  
- "enthusiasm": How enthusiastic they sound (0.0 = flat, 1.0 = very enthusiastic)

Consider:
- Response length (longer = more engaged)
- Positive words (increase enthusiasm)
- Hesitation words like "um", "uh" (decrease comfort)
- Specific details (increase engagement)
- Excitement words (increase enthusiasm)

JSON:"""
        return prompt
    
    def _clean_ai_response(self, response: str) -> str:
        """Clean up AI response to remove formatting issues"""
        if not response:
            return response
        
        # Remove extra quotes and formatting
        cleaned = response.strip()
        
        # Remove surrounding quotes if present
        if (cleaned.startswith('"') and cleaned.endswith('"')) or \
           (cleaned.startswith("'") and cleaned.endswith("'")):
            cleaned = cleaned[1:-1]
        
        # Remove double quotes around the entire response
        if cleaned.startswith('""') and cleaned.endswith('""'):
            cleaned = cleaned[2:-2]
        
        # Remove any trailing explanatory text in parentheses
        import re
        # Remove text like "(24 words, lighthearted, open-ended...)"
        cleaned = re.sub(r'\s*\([^)]*words[^)]*\)\s*', '', cleaned)
        
        # Remove extra question marks (keep only one at the end)
        cleaned = cleaned.rstrip('?')
        if not cleaned.endswith('?'):
            cleaned += '?'
        
        return cleaned.strip()
    
    async def _call_ai_api(self, prompt: str) -> Optional[str]:
        """调用AI API (支持OpenAI和DeepSeek)"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": self.model,
            "messages": [
                {"role": "user", "content": prompt}
            ],
            "max_tokens": 100,
            "temperature": 0.7
        }
        
        try:
            logger.debug(f"Making API call to: {self.api_url}")
            logger.debug(f"Payload: {payload}")

            # 设置更详细的超时配置，优化并发性能
            timeout = aiohttp.ClientTimeout(
                total=self.timeout,
                connect=3,  # 连接超时 - 减少到3秒
                sock_read=self.timeout - 1  # 读取超时 - 为总超时留1秒缓冲
            )

            # 使用连接池提高并发性能
            connector = aiohttp.TCPConnector(
                limit=10,  # 总连接池大小
                limit_per_host=5,  # 每个主机的连接数
                ttl_dns_cache=300,  # DNS缓存5分钟
                use_dns_cache=True
            )

            async with aiohttp.ClientSession(timeout=timeout, connector=connector) as session:
                async with session.post(self.api_url, json=payload, headers=headers) as response:
                    logger.debug(f"API response status: {response.status}")
                    if response.status == 200:
                        data = await response.json()
                        question = data["choices"][0]["message"]["content"].strip()
                        logger.debug(f"API returned question: {question}")
                        return question
                    else:
                        response_text = await response.text()
                        logger.error(f"{self.provider.upper()} API error {response.status}: {response_text}")
                        return None

        except asyncio.TimeoutError:
            logger.warning(f"{self.provider.upper()} API timeout after {self.timeout}s")
            return None
        except aiohttp.ClientError as e:
            logger.error(f"{self.provider.upper()} API client error: {e}")
            return None
        except Exception as e:
            logger.error(f"{self.provider.upper()} API call failed: {e}")
            return None
    
    def validate_question(self, question: str) -> bool:
        """验证生成的问题质量"""
        if not question:
            return False
        
        # 基本检查
        checks = [
            (len(question.strip()) >= 10, "too short"),
            (len(question.strip()) <= 200, "too long (characters)"),
            (question.strip().endswith('?'), "missing question mark"),
            (len(question.split()) <= 30, f"too many words ({len(question.split())})"),
            (not self._contains_sensitive_words(question), "contains sensitive words")
        ]

        failed_checks = [reason for passed, reason in checks if not passed]

        if failed_checks:
            logger.warning(f"Question validation failed: {question}")
            logger.warning(f"Failed checks: {failed_checks}")
            return False

        return True
    
    def _contains_sensitive_words(self, question: str) -> bool:
        """检查是否包含敏感词"""
        import re

        sensitive_words = [
            'sex', 'sexual', 'money', 'salary', 'income', 'politics',
            'religion', 'weight', 'divorce', 'ex-wife', 'ex-husband',
            'depression', 'anxiety', 'therapy', 'medication'
        ]

        question_lower = question.lower()
        # 使用单词边界避免误报（如"age"在"manager"中）
        for word in sensitive_words:
            if re.search(r'\b' + re.escape(word) + r'\b', question_lower):
                return True
        return False
