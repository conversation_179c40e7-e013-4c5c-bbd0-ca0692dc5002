"""
SMSService - 短信验证服务
使用Twilio SMS API进行手机号验证
"""

from typing import Dict, Optional, Any
from datetime import datetime, timedelta
import logging
import random
import string
import hashlib

from ..config.service_config import ServiceConfig
from ..utils.data_models import SMSVerification, APIResponse
from ..core.database_manager import DatabaseManager

# Twilio导入 - 使用try/except处理可能的导入错误
try:
    from twilio.rest import Client as TwilioClient
    from twilio.base.exceptions import TwilioException
    TWILIO_AVAILABLE = True
except ImportError:
    TWILIO_AVAILABLE = False
    TwilioClient = None
    TwilioException = Exception

logger = logging.getLogger(__name__)

class TwilioSMSClient:
    """Twilio SMS API客户端"""

    def __init__(self, config: Dict[str, Any]):
        self.account_sid = config["account_sid"]
        self.auth_token = config["auth_token"]
        self.service_sid = config.get("service_sid")
        self.phone_number = config.get("phone_number")

        # 验证配置
        if not self.service_sid and not self.phone_number:
            raise ValueError("Either service_sid or phone_number must be provided")

        # 初始化Twilio客户端
        if TWILIO_AVAILABLE:
            self.client = TwilioClient(self.account_sid, self.auth_token)
            logger.info(f"Twilio SMS client initialized with Account SID: {self.account_sid[:8]}...")
        else:
            self.client = None
            logger.warning("Twilio SDK not available. SMS will use test mode.")

        # 检测是否为测试凭据
        self.is_test_mode = self.account_sid.startswith("AC3dd33d8d166d7582c37f79ac806adef3")
    
    def send_sms(self, to_number: str, message: str) -> bool:
        """发送SMS消息"""
        try:
            # 如果Twilio SDK不可用，使用模拟模式
            if not TWILIO_AVAILABLE or not self.client:
                logger.info(f"[SIMULATION] Sending SMS to {to_number}: {message}")
                logger.info(f"[SIMULATION] SMS sent successfully to {to_number}")
                return True

            # 测试凭据模式
            if self.is_test_mode:
                logger.info(f"[TEST MODE] Sending SMS to {to_number}: {message}")

                # Twilio测试凭据会发送到测试号码，但不会发送真实SMS
                # 这里我们仍然调用API来验证配置，但知道不会发送真实消息
                try:
                    if self.service_sid:
                        message_obj = self.client.messages.create(
                            messaging_service_sid=self.service_sid,
                            body=message,
                            to=to_number
                        )
                    else:
                        message_obj = self.client.messages.create(
                            from_=self.phone_number,
                            body=message,
                            to=to_number
                        )

                    logger.info(f"[TEST MODE] SMS API call successful, SID: {message_obj.sid}")
                    return True

                except TwilioException as e:
                    logger.warning(f"[TEST MODE] Twilio API error (expected in test): {e}")
                    # 测试模式下，即使API调用失败也返回成功
                    return True

            # 生产模式：调用真实Twilio API
            try:
                if self.service_sid:
                    # 使用Messaging Service
                    message_obj = self.client.messages.create(
                        messaging_service_sid=self.service_sid,
                        body=message,
                        to=to_number
                    )
                else:
                    # 使用电话号码
                    message_obj = self.client.messages.create(
                        from_=self.phone_number,
                        body=message,
                        to=to_number
                    )

                logger.info(f"[PRODUCTION] SMS sent successfully to {to_number}, SID: {message_obj.sid}")
                return True

            except TwilioException as e:
                logger.error(f"Twilio API error: {e}")
                return False

        except Exception as e:
            logger.error(f"SMS sending error: {e}")
            return False

class SMSService:
    """SMS验证服务"""
    
    def __init__(self, redis_client=None, db_manager=None):
        """初始化SMS服务"""
        self.redis = redis_client
        self.db_manager = db_manager or DatabaseManager(redis_client=redis_client)

        # 获取Twilio配置
        sms_config = ServiceConfig.get_twilio_sms_config()
        self.sms_client = TwilioSMSClient(sms_config)

        # 验证配置
        self.code_expire_minutes = sms_config["code_expire_minutes"]
        self.max_attempts = sms_config["max_attempts"]
        self.rate_limit_per_hour = sms_config["rate_limit_per_hour"]

        # 内存存储（MVP版本备用）
        self._verifications: Dict[str, SMSVerification] = {}
        self._rate_limits: Dict[str, list] = {}

        logger.info("SMSService initialized")
    
    async def send_verification_code(self, phone_number: str) -> APIResponse:
        """发送验证码"""
        try:
            # 检查频率限制
            if not self._check_rate_limit(phone_number):
                return APIResponse.error_response(
                    "Too many SMS requests. Please try again later.", 
                    "RATE_LIMIT_EXCEEDED"
                )
            
            # 生成验证码
            verification_code = self._generate_verification_code()
            
            # 创建验证记录
            verification = SMSVerification.create_verification(
                phone_number, verification_code, self.code_expire_minutes
            )
            
            # 构建SMS消息
            message = f"Your verification code is: {verification_code}. Valid for {self.code_expire_minutes} minutes."
            
            # 发送SMS
            send_success = self.sms_client.send_sms(phone_number, message)
            
            if not send_success:
                return APIResponse.error_response(
                    "Failed to send SMS. Please try again.", 
                    "SMS_SEND_FAILED"
                )
            
            # 保存验证记录
            await self._store_verification(verification)
            
            # 记录发送时间（用于频率限制）
            self._record_sms_sent(phone_number)
            
            return APIResponse.success_response({
                "message": "Verification code sent successfully",
                "expires_in": self.code_expire_minutes * 60,  # 秒
                "phone_number": phone_number
            })
            
        except Exception as e:
            logger.error(f"SMS verification sending failed: {e}")
            return APIResponse.error_response(str(e), "SMS_VERIFICATION_FAILED")
    
    async def verify_code(self, phone_number: str, code: str) -> APIResponse:
        """验证验证码"""
        try:
            logger.info(f"=== SMS VERIFICATION START ===")
            logger.info(f"Phone: {phone_number}, Input code: '{code}'")
            
            # 获取验证记录
            verification = await self._get_verification(phone_number)
            
            if not verification:
                logger.info(f"No verification found for phone: {phone_number}")
                return APIResponse.error_response(
                    "No verification found for this phone number", 
                    "VERIFICATION_NOT_FOUND"
                )
            
            logger.info(f"Verification found - Code: '{verification.verification_code}', Attempts: {verification.attempts}")
            
            # 检查是否已验证
            try:
                is_already_verified = verification.is_verified()
                logger.info(f"is_verified() returned: {is_already_verified}")
                if is_already_verified:
                    logger.info(f"Verification already used")
                    return APIResponse.error_response(
                        "This verification code has already been used", 
                        "CODE_ALREADY_USED"
                    )
            except Exception as e:
                logger.error(f"Error in is_verified(): {e}")
                return APIResponse.error_response(str(e), "VERIFICATION_ERROR")
            
            # 检查是否过期
            try:
                is_expired = verification.is_expired()
                logger.info(f"is_expired() returned: {is_expired}")
                if is_expired:
                    logger.info(f"Verification expired")
                    return APIResponse.error_response(
                        "Verification code has expired", 
                        "CODE_EXPIRED"
                    )
            except Exception as e:
                logger.error(f"Error in is_expired(): {e}")
                return APIResponse.error_response(str(e), "VERIFICATION_ERROR")
            
            # 检查尝试次数
            if verification.attempts >= self.max_attempts:
                logger.info(f"Too many attempts: {verification.attempts} >= {self.max_attempts}")
                return APIResponse.error_response(
                    "Too many verification attempts", 
                    "MAX_ATTEMPTS_EXCEEDED"
                )
            
            logger.info(f"About to call verification.verify() with code: '{code}'")
            # 验证代码
            is_valid = verification.verify(code)
            logger.info(f"verification.verify() returned: {is_valid}")
            
            # 更新验证记录
            await self._store_verification(verification)
            
            if is_valid:
                return APIResponse.success_response({
                    "message": "Phone number verified successfully",
                    "phone_number": phone_number,
                    "verified_at": verification.verified_at.isoformat()
                })
            else:
                return APIResponse.error_response(
                    f"Invalid verification code. {self.max_attempts - verification.attempts} attempts remaining.", 
                    "INVALID_CODE"
                )
                
        except Exception as e:
            logger.error(f"SMS verification failed: {e}")
            return APIResponse.error_response(str(e), "VERIFICATION_FAILED")
    
    async def resend_verification_code(self, phone_number: str) -> APIResponse:
        """重新发送验证码"""
        try:
            # 检查是否存在未过期的验证
            existing_verification = await self._get_verification(phone_number)
            
            if existing_verification and not existing_verification.is_expired():
                time_remaining = (existing_verification.expires_at - datetime.now()).total_seconds()
                if time_remaining > 60:  # 如果还有超过1分钟，不允许重发
                    return APIResponse.error_response(
                        f"Please wait {int(time_remaining)} seconds before requesting a new code", 
                        "RESEND_TOO_SOON"
                    )
            
            # 发送新的验证码
            return await self.send_verification_code(phone_number)
            
        except Exception as e:
            logger.error(f"SMS resend failed: {e}")
            return APIResponse.error_response(str(e), "RESEND_FAILED")
    
    def check_service_status(self) -> Dict[str, Any]:
        """检查服务状态"""
        status_data = {
            "service": "sms",
            "status": "healthy",
            "twilio_sdk_available": TWILIO_AVAILABLE,
            "client_initialized": self.sms_client is not None,
            "test_mode": self.sms_client.is_test_mode if self.sms_client else False,
            "config": {
                "code_expire_minutes": self.code_expire_minutes,
                "max_attempts": self.max_attempts,
                "rate_limit_per_hour": self.rate_limit_per_hour,
                "has_service_sid": bool(self.sms_client.service_sid if self.sms_client else False),
                "has_phone_number": bool(self.sms_client.phone_number if self.sms_client else False)
            }
        }

        # 测试Twilio连接
        if TWILIO_AVAILABLE and self.sms_client and self.sms_client.client:
            try:
                # 获取账户信息来测试连接
                account = self.sms_client.client.api.accounts(self.sms_client.account_sid).fetch()
                status_data["twilio_connection"] = "connected"
                status_data["account_status"] = account.status
            except Exception as e:
                status_data["twilio_connection"] = "error"
                status_data["connection_error"] = str(e)

        return status_data
    
    # ============ 私有方法 ============
    
    def _generate_verification_code(self, length: int = 6) -> str:
        """生成验证码"""
        return ''.join(random.choices(string.digits, k=length))
    
    def _check_rate_limit(self, phone_number: str) -> bool:
        """检查频率限制"""
        now = datetime.now()
        hour_ago = now - timedelta(hours=1)
        
        # 获取过去一小时的发送记录
        if phone_number not in self._rate_limits:
            self._rate_limits[phone_number] = []
        
        # 清理过期记录
        self._rate_limits[phone_number] = [
            timestamp for timestamp in self._rate_limits[phone_number]
            if timestamp > hour_ago
        ]
        
        # 检查是否超过限制
        return len(self._rate_limits[phone_number]) < self.rate_limit_per_hour
    
    def _record_sms_sent(self, phone_number: str):
        """记录SMS发送时间"""
        if phone_number not in self._rate_limits:
            self._rate_limits[phone_number] = []
        
        self._rate_limits[phone_number].append(datetime.now())
    
    async def _store_verification(self, verification: SMSVerification):
        """异步存储验证记录"""
        try:
            # 使用数据库管理器异步存储
            await self.db_manager.save_sms_verification(verification)
        except Exception as e:
            logger.error(f"Failed to store verification in database: {e}")
            # 备用：内存存储
            self._verifications[verification.phone_number] = verification
    
    async def _get_verification(self, phone_number: str) -> Optional[SMSVerification]:
        """异步获取验证记录"""
        try:
            # 使用数据库管理器异步获取
            verification = await self.db_manager.load_sms_verification(phone_number)
            if verification:
                return verification
        except Exception as e:
            logger.error(f"Failed to get verification from database: {e}")

        # 备用：从内存获取
        if phone_number in self._verifications:
            return self._verifications[phone_number]

        return None
