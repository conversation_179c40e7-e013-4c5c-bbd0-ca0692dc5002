"""
RegularConversationAgent - 已注册用户对话代理
处理已注册用户的日常对话，增量更新档案信息
"""

import logging
import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime
from dataclasses import dataclass

from .ai_service import AIService
from ..core.database_manager import DatabaseManager
from ..core.profile_manager import ProfileManager
from ..core.memory_manager import MemoryManager

logger = logging.getLogger(__name__)

@dataclass
class ConversationResponse:
    """对话响应数据结构"""
    content: str
    should_end: bool = False
    updated_info: Dict[str, Any] = None
    conversation_id: str = None
    
    def __post_init__(self):
        if self.updated_info is None:
            self.updated_info = {}
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "content": self.content,
            "should_end": self.should_end,
            "updated_info": self.updated_info,
            "conversation_id": self.conversation_id
        }

class RegularConversationAgent:
    """已注册用户对话代理"""
    
    def __init__(self, profile_manager: <PERSON><PERSON><PERSON><PERSON>, memory_manager: MemoryManager,
                 db_manager: DatabaseManager, ai_service: AIService = None):
        """初始化对话Agent"""
        self.profile_manager = profile_manager
        self.memory_manager = memory_manager
        self.db_manager = db_manager
        self.ai_service = ai_service or AIService()
        
        logger.info("RegularConversationAgent initialized")
    
    async def start_conversation(self, user_id: str, conversation_type: str = "chat") -> ConversationResponse:
        """开始新对话"""
        try:
            # 创建对话记录
            conversation_id = await self.db_manager.create_conversation(user_id, conversation_type)
            
            # 获取用户档案
            profile = await self.profile_manager.get_profile(user_id)
            
            # 生成个性化开场白
            greeting = await self._generate_greeting(user_id, profile)
            
            # 保存到记忆
            await self.memory_manager.add_message(user_id, "assistant", greeting)
            
            logger.info(f"Started conversation {conversation_id} for user {user_id}")
            
            return ConversationResponse(
                content=greeting,
                conversation_id=conversation_id
            )
            
        except Exception as e:
            logger.error(f"Failed to start conversation for user {user_id}: {e}")
            return ConversationResponse(
                content="Hello! Nice to chat with you. How are you doing?"
            )
    
    async def process_conversation(self, user_id: str, user_input: str, 
                                 conversation_id: str = None) -> ConversationResponse:
        """处理用户对话输入"""
        try:
            # 保存用户输入到记忆
            await self.memory_manager.add_message(user_id, "user", user_input)
            
            # 获取用户档案
            profile = await self.profile_manager.get_profile(user_id)
            
            # 构建对话上下文
            context = await self.memory_manager.build_llm_context(user_id, user_input, profile)
            
            # 生成AI回复
            ai_response = await self.ai_service.chat(context)
            
            # 保存AI回复到记忆
            await self.memory_manager.add_message(user_id, "assistant", ai_response.content)
            
            # 保存到数据库
            if conversation_id:
                await self.db_manager.add_message(conversation_id, "user", user_input)
                await self.db_manager.add_message(conversation_id, "assistant", ai_response.content)
            
            # 异步更新档案信息
            updated_info = {}
            try:
                updated_info = await self._update_profile_from_conversation(user_id, user_input, ai_response.content)
            except Exception as e:
                logger.warning(f"Failed to update profile for user {user_id}: {e}")
            
            # 检查是否应该结束对话
            should_end = self._should_end_conversation(user_input, ai_response.content)
            
            return ConversationResponse(
                content=ai_response.content,
                should_end=should_end,
                updated_info=updated_info,
                conversation_id=conversation_id
            )
            
        except Exception as e:
            logger.error(f"Failed to process conversation for user {user_id}: {e}")
            return ConversationResponse(
                content="Sorry, I didn't catch that. Could you say that again?"
            )
    
    async def _generate_greeting(self, user_id: str, profile: Dict[str, Any]) -> str:
        """Generate personalized greeting"""
        try:
            # Get important topics
            context = await self.memory_manager.get_conversation_context(user_id)
            important_topics = context.get("important_topics", [])

            # Build personalized prompt
            greeting_prompt = f"""
You are a friendly dating profile assistant. The user is your old friend.

User Profile:
{self._format_profile_for_prompt(profile)}

Recent Topics:
{self._format_topics_for_prompt(important_topics)}

Please generate a natural, friendly greeting that can:
1. Simple greeting
2. Mention previously discussed topics (if any)
3. Ask about recent updates

Keep it casual and natural, not too formal.
"""

            greeting = await self.ai_service.generate_text(greeting_prompt)
            return greeting or "Hey! How have you been?"

        except Exception as e:
            logger.warning(f"Failed to generate greeting: {e}")
            return "Hello! Great to see you again. How have you been?"
    
    async def _update_profile_from_conversation(self, user_id: str, user_input: str, 
                                              ai_response: str) -> Dict[str, Any]:
        """从对话中更新用户档案"""
        try:
            # 使用ProfileManager的提取功能
            success = await self.profile_manager.extract_and_update_from_conversation(
                user_id, user_input, ai_response
            )
            
            if success:
                # 获取更新后的档案
                updated_profile = await self.profile_manager.get_profile(user_id)
                return {"profile_updated": True, "new_profile": updated_profile}
            else:
                return {"profile_updated": False}
                
        except Exception as e:
            logger.error(f"Failed to update profile from conversation: {e}")
            return {"profile_updated": False, "error": str(e)}
    
    def _should_end_conversation(self, user_input: str, ai_response: str) -> bool:
        """Determine if conversation should end"""
        # Check if user expressed ending intent
        end_signals = [
            "goodbye", "bye", "see you", "talk later", "gotta go",
            "have to go", "end", "stop", "quit", "exit"
        ]

        user_input_lower = user_input.lower()
        for signal in end_signals:
            if signal in user_input_lower:
                return True

        # Check if AI gave ending signal
        ai_end_signals = ["goodbye", "bye", "talk soon", "take care", "see you"]
        ai_response_lower = ai_response.lower()
        for signal in ai_end_signals:
            if signal in ai_response_lower:
                return True

        return False
    
    def _format_profile_for_prompt(self, profile: Dict[str, Any]) -> str:
        """Format profile information for prompt"""
        if not profile:
            return "No profile information available"

        formatted_parts = []

        # Basic information
        basic_info = profile.get("basic_info", {})
        if basic_info:
            parts = []
            if basic_info.get("name"):
                parts.append(f"Name: {basic_info['name']}")
            if basic_info.get("age"):
                parts.append(f"Age: {basic_info['age']}")
            if basic_info.get("city"):
                parts.append(f"City: {basic_info['city']}")
            if basic_info.get("occupation"):
                parts.append(f"Occupation: {basic_info['occupation']}")

            if parts:
                formatted_parts.append("Basic Info: " + ", ".join(parts))

        # Interests
        interests = profile.get("interests", {})
        if interests and interests.get("hobbies"):
            hobbies = interests["hobbies"]
            if isinstance(hobbies, list):
                formatted_parts.append(f"Interests: {', '.join(hobbies)}")

        # Personality traits
        personality = profile.get("personality", {})
        if personality and personality.get("traits"):
            traits = personality["traits"]
            if isinstance(traits, list):
                formatted_parts.append(f"Personality: {', '.join(traits)}")

        return "\n".join(formatted_parts) if formatted_parts else "No detailed profile information available"
    
    def _format_topics_for_prompt(self, topics: List[Dict[str, Any]]) -> str:
        """Format topic information for prompt"""
        if not topics:
            return "No recent topics"

        # Only take the most recent 3 topics
        recent_topics = topics[-3:] if len(topics) > 3 else topics

        formatted_topics = []
        for topic in recent_topics:
            topic_name = topic.get("topic", "Unknown topic")
            context = topic.get("context", "")
            if context:
                formatted_topics.append(f"- {topic_name}: {context[:50]}...")

        return "\n".join(formatted_topics) if formatted_topics else "No recent topics"
    
    async def end_conversation(self, user_id: str, conversation_id: str = None) -> ConversationResponse:
        """结束对话"""
        try:
            # 生成结束语
            farewell = await self._generate_farewell(user_id)
            
            # 保存到记忆
            await self.memory_manager.add_message(user_id, "assistant", farewell)
            
            # 保存到数据库
            if conversation_id:
                await self.db_manager.add_message(conversation_id, "assistant", farewell)
            
            logger.info(f"Ended conversation for user {user_id}")
            
            return ConversationResponse(
                content=farewell,
                should_end=True,
                conversation_id=conversation_id
            )
            
        except Exception as e:
            logger.error(f"Failed to end conversation for user {user_id}: {e}")
            return ConversationResponse(
                content="Goodbye! Looking forward to chatting with you again.",
                should_end=True
            )
    
    async def _generate_farewell(self, user_id: str) -> str:
        """Generate personalized farewell"""
        try:
            # Get user profile
            profile = await self.profile_manager.get_profile(user_id)

            farewell_prompt = f"""
Generate a friendly and natural farewell message.

User Information:
{self._format_profile_for_prompt(profile)}

Requirements:
1. Brief and friendly
2. Can mention looking forward to next chat
3. Keep warm tone

Example: Goodbye! Looking forward to chatting with you again.
"""

            farewell = await self.ai_service.generate_text(farewell_prompt)
            return farewell or "Goodbye! Looking forward to chatting with you again."

        except Exception as e:
            logger.warning(f"Failed to generate farewell: {e}")
            return "Goodbye! Take care!"
