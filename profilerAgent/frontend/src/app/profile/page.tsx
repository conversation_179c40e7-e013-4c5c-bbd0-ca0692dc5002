'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { Input } from '@/components/ui/Input'
import { User, ArrowLeft, Sparkles, BarChart3, CheckCircle, XCircle, Eye } from 'lucide-react'
import { useRouter } from 'next/navigation'

export default function ProfilePage() {
  const router = useRouter()
  const [userId, setUserId] = useState('')
  const [token, setToken] = useState('')
  const [response, setResponse] = useState<any>(null)
  const [loading, setLoading] = useState(false)

  const getAuthHeaders = () => ({
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  })

  const testGetProfileCards = async () => {
    setLoading(true)
    setResponse(null)

    try {
      const res = await fetch(`http://localhost:8000/api/v1/profile/cards/${userId}`, {
        headers: getAuthHeaders()
      })
      const data = await res.json()
      setResponse({ status: res.status, data })
    } catch (error: any) {
      setResponse({ error: error.message })
    } finally {
      setLoading(false)
    }
  }

  const testGenerateFinalProfile = async () => {
    setLoading(true)
    setResponse(null)

    try {
      const res = await fetch(`http://localhost:8000/api/v1/profile/generate/${userId}`, {
        method: 'POST',
        headers: getAuthHeaders()
      })
      const data = await res.json()
      setResponse({ status: res.status, data })
    } catch (error: any) {
      setResponse({ error: error.message })
    } finally {
      setLoading(false)
    }
  }

  const testGetProfileAnalysis = async () => {
    setLoading(true)
    setResponse(null)

    try {
      const res = await fetch(`http://localhost:8000/api/v1/profile/analysis/${userId}`, {
        headers: getAuthHeaders()
      })
      const data = await res.json()
      setResponse({ status: res.status, data })
    } catch (error: any) {
      setResponse({ error: error.message })
    } finally {
      setLoading(false)
    }
  }

  const testGetProfileStatus = async () => {
    setLoading(true)
    setResponse(null)

    try {
      const res = await fetch(`http://localhost:8000/api/v1/profile/status/${userId}`, {
        headers: getAuthHeaders()
      })
      const data = await res.json()
      setResponse({ status: res.status, data })
    } catch (error: any) {
      setResponse({ error: error.message })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-pink-50 p-4">
      <div className="max-w-4xl mx-auto">
        <div className="flex items-center gap-4 mb-8">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push('/')}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Home
          </Button>
          <div className="flex items-center gap-2">
            <User className="w-6 h-6 text-purple-600" />
            <h1 className="text-2xl font-bold text-gray-900">Profile API Testing</h1>
          </div>
        </div>

        {/* Input Section */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Sparkles className="w-5 h-5 text-purple-600" />
              Test Configuration
            </CardTitle>
            <CardDescription>
              Configure user ID and authentication token for profile API testing
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                User ID
              </label>
              <Input
                type="text"
                placeholder="Enter user ID"
                value={userId}
                onChange={(e) => setUserId(e.target.value)}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                JWT Token
              </label>
              <Input
                type="text"
                placeholder="Enter JWT token"
                value={token}
                onChange={(e) => setToken(e.target.value)}
              />
            </div>
          </CardContent>
        </Card>

        {/* API Testing Buttons */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Profile Cards</CardTitle>
              <CardDescription>Get user profile cards</CardDescription>
            </CardHeader>
            <CardContent>
              <Button
                onClick={testGetProfileCards}
                disabled={loading || !userId || !token}
                className="w-full"
              >
                <Eye className="w-4 h-4 mr-2" />
                Get Profile Cards
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Generate Final Profile</CardTitle>
              <CardDescription>Generate complete user profile</CardDescription>
            </CardHeader>
            <CardContent>
              <Button
                onClick={testGenerateFinalProfile}
                disabled={loading || !userId || !token}
                className="w-full"
              >
                <Sparkles className="w-4 h-4 mr-2" />
                Generate Profile
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Profile Analysis</CardTitle>
              <CardDescription>Get detailed profile analysis</CardDescription>
            </CardHeader>
            <CardContent>
              <Button
                onClick={testGetProfileAnalysis}
                disabled={loading || !userId || !token}
                className="w-full"
              >
                <BarChart3 className="w-4 h-4 mr-2" />
                Get Analysis
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Profile Status</CardTitle>
              <CardDescription>Check profile generation status</CardDescription>
            </CardHeader>
            <CardContent>
              <Button
                onClick={testGetProfileStatus}
                disabled={loading || !userId || !token}
                className="w-full"
              >
                <CheckCircle className="w-4 h-4 mr-2" />
                Check Status
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Response Display */}
        {response && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                {response.error ? (
                  <XCircle className="w-5 h-5 text-red-500" />
                ) : (
                  <CheckCircle className="w-5 h-5 text-green-500" />
                )}
                API Response
              </CardTitle>
            </CardHeader>
            <CardContent>
              <pre className="bg-gray-100 p-4 rounded-lg overflow-auto text-sm">
                {JSON.stringify(response, null, 2)}
              </pre>
            </CardContent>
          </Card>
        )}

        {loading && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
            <div className="bg-white p-6 rounded-lg">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto"></div>
              <p className="mt-2 text-center">Loading...</p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
