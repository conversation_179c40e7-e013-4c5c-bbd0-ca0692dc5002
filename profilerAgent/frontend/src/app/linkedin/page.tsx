'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/Button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { Input } from '@/components/ui/Input'
import { Briefcase, ArrowLeft, ExternalLink, Shield, Trash2, User, CheckCircle, XCircle } from 'lucide-react'
import { useRouter } from 'next/navigation'

export default function LinkedInPage() {
  const router = useRouter()
  const [userId, setUserId] = useState('')
  const [linkedinUrl, setLinkedinUrl] = useState('')
  const [response, setResponse] = useState<any>(null)
  const [loading, setLoading] = useState(false)

  const testLinkedInAuth = async () => {
    setLoading(true)
    setResponse(null)

    try {
      const res = await fetch(`http://localhost:8000/api/v1/linkedin/auth?user_id=${userId}`)
      const data = await res.json()
      setResponse({ status: res.status, data })
      
      // 如果获取到授权URL，可以选择打开新窗口
      if (data.success && data.data?.auth_url) {
        const shouldOpen = confirm('是否在新窗口中打开LinkedIn授权页面？')
        if (shouldOpen) {
          window.open(data.data.auth_url, '_blank')
        }
      }
    } catch (error: any) {
      setResponse({ error: error.message })
    } finally {
      setLoading(false)
    }
  }

  const testLinkedInStatus = async () => {
    setLoading(true)
    setResponse(null)

    try {
      const res = await fetch(`http://localhost:8000/api/v1/linkedin/status/${userId}`)
      const data = await res.json()
      setResponse({ status: res.status, data })
    } catch (error: any) {
      setResponse({ error: error.message })
    } finally {
      setLoading(false)
    }
  }

  const testVerifyProfile = async () => {
    setLoading(true)
    setResponse(null)

    try {
      const res = await fetch(`http://localhost:8000/api/v1/linkedin/verify/${userId}?linkedin_url=${encodeURIComponent(linkedinUrl)}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      })

      const data = await res.json()
      setResponse({ status: res.status, data })
    } catch (error: any) {
      setResponse({ error: error.message })
    } finally {
      setLoading(false)
    }
  }

  const testGetProfile = async () => {
    setLoading(true)
    setResponse(null)

    try {
      const res = await fetch(`http://localhost:8000/api/v1/linkedin/profile/${userId}`)
      const data = await res.json()
      setResponse({ status: res.status, data })
    } catch (error: any) {
      setResponse({ error: error.message })
    } finally {
      setLoading(false)
    }
  }

  const testDisconnect = async () => {
    setLoading(true)
    setResponse(null)

    try {
      const res = await fetch(`http://localhost:8000/api/v1/linkedin/disconnect/${userId}`, {
        method: 'DELETE'
      })

      const data = await res.json()
      setResponse({ status: res.status, data })
    } catch (error: any) {
      setResponse({ error: error.message })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center mb-8">
          <Button 
            variant="ghost" 
            onClick={() => router.push('/')}
            className="mr-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            返回主页
          </Button>
          <div className="flex items-center">
            <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center mr-4">
              <Briefcase className="w-6 h-6 text-purple-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-800">LinkedIn集成模块</h1>
              <p className="text-gray-600">测试LinkedIn OAuth和职业背景验证</p>
            </div>
          </div>
        </div>

        <div className="max-w-6xl mx-auto grid lg:grid-cols-2 gap-8">
          {/* Input Form */}
          <Card>
            <CardHeader>
              <CardTitle>测试数据</CardTitle>
              <CardDescription>
                输入用户ID和LinkedIn资料进行测试
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Input
                label="用户ID"
                placeholder="user_12345"
                value={userId}
                onChange={(e) => setUserId(e.target.value)}
              />

              <Input
                label="LinkedIn资料URL"
                placeholder="https://www.linkedin.com/in/username"
                value={linkedinUrl}
                onChange={(e) => setLinkedinUrl(e.target.value)}
              />
              
              <div className="bg-purple-50 p-4 rounded-lg">
                <h4 className="font-medium text-purple-900 mb-2">LinkedIn集成流程</h4>
                <ol className="text-sm text-purple-800 space-y-1">
                  <li>1. 开始OAuth授权</li>
                  <li>2. 用户完成LinkedIn登录</li>
                  <li>3. 系统获取资料信息</li>
                  <li>4. 验证职业背景</li>
                </ol>
              </div>
            </CardContent>
          </Card>

          {/* API Tests */}
          <Card>
            <CardHeader>
              <CardTitle>LinkedIn API测试</CardTitle>
              <CardDescription>
                测试不同的LinkedIn集成端点
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button
                onClick={testLinkedInAuth}
                disabled={loading || !userId}
                loading={loading}
                className="w-full"
              >
                <ExternalLink className="w-4 h-4 mr-2" />
                开始LinkedIn授权
              </Button>

              <Button
                onClick={testLinkedInStatus}
                disabled={loading || !userId}
                loading={loading}
                className="w-full"
                variant="outline"
              >
                <Shield className="w-4 h-4 mr-2" />
                检查验证状态
              </Button>

              <Button
                onClick={testVerifyProfile}
                disabled={loading || !userId || !linkedinUrl}
                loading={loading}
                className="w-full"
                variant="secondary"
              >
                <CheckCircle className="w-4 h-4 mr-2" />
                手动验证资料
              </Button>

              <Button
                onClick={testGetProfile}
                disabled={loading || !userId}
                loading={loading}
                className="w-full"
                variant="ghost"
              >
                <User className="w-4 h-4 mr-2" />
                获取资料信息
              </Button>

              <Button
                onClick={testDisconnect}
                disabled={loading || !userId}
                loading={loading}
                className="w-full"
                variant="outline"
              >
                <Trash2 className="w-4 h-4 mr-2" />
                断开LinkedIn连接
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Response Display */}
        {response && (
          <Card className="max-w-6xl mx-auto mt-8">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                {response.error ? (
                  <XCircle className="w-5 h-5 text-red-500" />
                ) : (
                  <CheckCircle className="w-5 h-5 text-green-500" />
                )}
                <span>API响应</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <pre className="bg-gray-100 p-4 rounded-lg overflow-auto text-sm">
                {JSON.stringify(response, null, 2)}
              </pre>
            </CardContent>
          </Card>
        )}

        {/* API Documentation */}
        <Card className="max-w-6xl mx-auto mt-8">
          <CardHeader>
            <CardTitle>LinkedIn API文档</CardTitle>
            <CardDescription>
              可用的LinkedIn集成端点说明
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="border-l-4 border-blue-500 pl-4">
                <h3 className="font-semibold">GET /api/v1/linkedin/auth</h3>
                <p className="text-sm text-gray-600">开始LinkedIn OAuth授权流程</p>
              </div>
              <div className="border-l-4 border-green-500 pl-4">
                <h3 className="font-semibold">GET /api/v1/linkedin/callback</h3>
                <p className="text-sm text-gray-600">LinkedIn OAuth回调处理</p>
              </div>
              <div className="border-l-4 border-purple-500 pl-4">
                <h3 className="font-semibold">GET /api/v1/linkedin/status/{`{user_id}`}</h3>
                <p className="text-sm text-gray-600">获取用户LinkedIn验证状态</p>
              </div>
              <div className="border-l-4 border-orange-500 pl-4">
                <h3 className="font-semibold">POST /api/v1/linkedin/verify/{`{user_id}`}</h3>
                <p className="text-sm text-gray-600">手动提交LinkedIn资料URL进行验证</p>
              </div>
              <div className="border-l-4 border-red-500 pl-4">
                <h3 className="font-semibold">DELETE /api/v1/linkedin/disconnect/{`{user_id}`}</h3>
                <p className="text-sm text-gray-600">断开LinkedIn连接</p>
              </div>
              <div className="border-l-4 border-teal-500 pl-4">
                <h3 className="font-semibold">GET /api/v1/linkedin/profile/{`{user_id}`}</h3>
                <p className="text-sm text-gray-600">获取用户的LinkedIn资料信息</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* OAuth Info */}
        <Card className="max-w-6xl mx-auto mt-8">
          <CardHeader>
            <CardTitle>OAuth授权说明</CardTitle>
            <CardDescription>
              了解LinkedIn OAuth集成流程
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold mb-2">🔐 安全授权</h4>
                <p className="text-sm text-gray-600 mb-4">
                  使用LinkedIn官方OAuth 2.0协议，确保用户数据安全
                </p>
              </div>
              <div>
                <h4 className="font-semibold mb-2">📋 资料同步</h4>
                <p className="text-sm text-gray-600 mb-4">
                  自动获取用户的职业信息、教育背景和技能标签
                </p>
              </div>
              <div>
                <h4 className="font-semibold mb-2">✅ 身份验证</h4>
                <p className="text-sm text-gray-600 mb-4">
                  验证用户的真实身份和职业背景，提升匹配质量
                </p>
              </div>
              <div>
                <h4 className="font-semibold mb-2">🎯 精准匹配</h4>
                <p className="text-sm text-gray-600 mb-4">
                  基于职业、行业和教育背景进行更精准的用户匹配
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
} 