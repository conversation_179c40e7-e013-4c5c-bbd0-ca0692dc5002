'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/Button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { Input } from '@/components/ui/Input'
import { Textarea } from '@/components/ui/Textarea'
import { 
  Mic, ArrowLeft, Play, Pause, RotateCcw, CheckCircle, 
  XCircle, MessageCircle, Brain, User, Phone, Clock, 
  BarChart3, Hash, Database
} from 'lucide-react'
import { useRouter } from 'next/navigation'

interface InterviewSession {
  session_id: string
  user_id: string
  current_stage: string
  duration: number
  questions_asked: number
  responses_received: number
  is_active: boolean
}

interface InterviewResponse {
  success: boolean
  completed?: boolean
  session_id?: string
  current_question?: string
  next_question?: string
  stage?: string
  progress?: {
    stage: string
    questions_asked: number
    responses_received: number
    duration: number
  }
  analysis_result?: {
    mbti_type: string
    confidence: number
    personality_traits: string[]
  }
  error?: string
  message?: string
}

export default function TestVoicePage() {
  const router = useRouter()
  const [phoneNumber, setPhoneNumber] = useState('+15103650664')
  const [currentResponse, setCurrentResponse] = useState('')
  const [session, setSession] = useState<InterviewSession | null>(null)
  const [currentQuestion, setCurrentQuestion] = useState('')
  const [conversation, setConversation] = useState<Array<{question: string, response: string, stage: string}>>([])
  const [loading, setLoading] = useState(false)
  const [interviewCompleted, setInterviewCompleted] = useState(false)
  const [analysisResult, setAnalysisResult] = useState<any>(null)
  const [error, setError] = useState('')

  // 开始面试
  const startInterview = async () => {
    setLoading(true)
    setError('')
    setConversation([])
    setInterviewCompleted(false)
    setAnalysisResult(null)

    try {
      const response = await fetch('/api/test-voice/start', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ phone_number: phoneNumber })
      })

      const data: InterviewResponse = await response.json()
      
      if (data.success && data.session_id && data.current_question) {
        setSession({
          session_id: data.session_id,
          user_id: data.session_id,
          current_stage: data.stage || 'greeting',
          duration: 0,
          questions_asked: 1,
          responses_received: 0,
          is_active: true
        })
        setCurrentQuestion(data.current_question)
      } else {
        setError(data.message || 'Failed to start interview')
      }
    } catch (err: any) {
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  // 提交回答
  const submitResponse = async () => {
    if (!session || !currentResponse.trim()) return

    setLoading(true)
    setError('')

    try {
      const response = await fetch('/api/test-voice/respond', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          session_id: session.session_id,
          response: currentResponse.trim()
        })
      })

      const data: InterviewResponse = await response.json()
      
      if (data.success) {
        // 添加到对话历史
        setConversation(prev => [...prev, {
          question: currentQuestion,
          response: currentResponse,
          stage: session.current_stage
        }])

        if (data.completed) {
          // 面试完成
          setInterviewCompleted(true)
          setAnalysisResult(data.analysis_result)
          setSession(prev => prev ? {...prev, is_active: false} : null)
          setCurrentQuestion('')
        } else if (data.next_question) {
          // 继续面试
          setCurrentQuestion(data.next_question)
          setSession(prev => prev ? {
            ...prev,
            current_stage: data.stage || prev.current_stage,
            questions_asked: data.progress?.questions_asked || prev.questions_asked + 1,
            responses_received: data.progress?.responses_received || prev.responses_received + 1,
            duration: data.progress?.duration || prev.duration
          } : null)
        }

        setCurrentResponse('')
      } else {
        setError(data.message || 'Failed to process response')
      }
    } catch (err: any) {
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  // 重置面试
  const resetInterview = async () => {
    if (session) {
      try {
        await fetch(`/api/test-voice/reset/${phoneNumber}`, {
          method: 'POST'
        })
      } catch (err) {
        console.error('Reset failed:', err)
      }
    }
    
    setSession(null)
    setCurrentQuestion('')
    setConversation([])
    setCurrentResponse('')
    setInterviewCompleted(false)
    setAnalysisResult(null)
    setError('')
  }

  // 获取进度百分比
  const getProgress = () => {
    if (!session) return 0
    const stages = ['greeting', 'professional', 'personality', 'interests', 'relationships', 'closing']
    const currentIndex = stages.indexOf(session.current_stage)
    return ((currentIndex + 1) / stages.length) * 100
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Header */}
        <div className="flex items-center mb-8">
          <Button 
            variant="ghost" 
            onClick={() => router.push('/')}
            className="mr-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Dashboard
          </Button>
          <div className="flex items-center">
            <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-blue-500 rounded-xl flex items-center justify-center mr-4">
              <Mic className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-800">Voice Interview Test</h1>
              <p className="text-gray-600">Test the voice interview system with text input</p>
            </div>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <Card className="mb-6 border-red-200 bg-red-50">
            <CardContent className="pt-6">
              <div className="flex items-center text-red-600">
                <XCircle className="w-5 h-5 mr-2" />
                <span>{error}</span>
              </div>
            </CardContent>
          </Card>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column - Interview Interface */}
          <div className="lg:col-span-2 space-y-6">
            {/* Setup Card */}
            {!session && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Phone className="w-5 h-5 mr-2" />
                    Start Interview
                  </CardTitle>
                  <CardDescription>
                    Enter a phone number to start the voice interview test
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Phone Number
                    </label>
                    <Input
                      type="tel"
                      value={phoneNumber}
                      onChange={(e) => setPhoneNumber(e.target.value)}
                      placeholder="+1234567890"
                      className="font-mono"
                    />
                  </div>
                  <Button 
                    onClick={startInterview}
                    disabled={loading || !phoneNumber.trim()}
                    className="w-full"
                  >
                    {loading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Starting Interview...
                      </>
                    ) : (
                      <>
                        <Play className="w-4 h-4 mr-2" />
                        Start Interview
                      </>
                    )}
                  </Button>
                </CardContent>
              </Card>
            )}

            {/* Interview Progress */}
            {session && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center">
                      <MessageCircle className="w-5 h-5 mr-2" />
                      Interview in Progress
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={resetInterview}
                    >
                      <RotateCcw className="w-4 h-4 mr-2" />
                      Reset
                    </Button>
                  </CardTitle>
                  <CardDescription>
                    Stage: {session.current_stage.toUpperCase()} • Progress: {Math.round(getProgress())}%
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Progress Bar */}
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-gradient-to-r from-green-500 to-blue-500 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${getProgress()}%` }}
                    ></div>
                  </div>

                  {/* Current Question */}
                  {currentQuestion && !interviewCompleted && (
                    <div className="bg-blue-50 p-4 rounded-lg border-l-4 border-blue-400">
                      <div className="flex items-start">
                        <Brain className="w-5 h-5 text-blue-500 mr-2 mt-0.5" />
                        <div>
                          <p className="font-medium text-blue-800">AI Interviewer:</p>
                          <p className="text-blue-700 mt-1">{currentQuestion}</p>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Response Input */}
                  {currentQuestion && !interviewCompleted && (
                    <div className="space-y-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Your Response:
                      </label>
                      <Textarea
                        value={currentResponse}
                        onChange={(e) => setCurrentResponse(e.target.value)}
                        placeholder="Type your response here..."
                        rows={3}
                        className="resize-none"
                      />
                      <Button 
                        onClick={submitResponse}
                        disabled={loading || !currentResponse.trim()}
                        className="w-full"
                      >
                        {loading ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                            Processing...
                          </>
                        ) : (
                          <>
                            <MessageCircle className="w-4 h-4 mr-2" />
                            Submit Response
                          </>
                        )}
                      </Button>
                    </div>
                  )}

                  {/* Interview Completed */}
                  {interviewCompleted && (
                    <div className="bg-green-50 p-4 rounded-lg border-l-4 border-green-400">
                      <div className="flex items-center">
                        <CheckCircle className="w-5 h-5 text-green-500 mr-2" />
                        <div>
                          <p className="font-medium text-green-800">Interview Completed!</p>
                          <p className="text-green-700 text-sm mt-1">
                            Your personality analysis has been generated.
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Conversation History */}
            {conversation.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <MessageCircle className="w-5 h-5 mr-2" />
                    Conversation History
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4 max-h-96 overflow-y-auto">
                    {conversation.map((exchange, index) => (
                      <div key={index} className="space-y-2">
                        <div className="bg-blue-50 p-3 rounded-lg">
                          <div className="flex items-center text-xs text-blue-600 mb-1">
                            <Brain className="w-3 h-3 mr-1" />
                            AI • {exchange.stage.toUpperCase()}
                          </div>
                          <p className="text-blue-800">{exchange.question}</p>
                        </div>
                        <div className="bg-gray-50 p-3 rounded-lg">
                          <div className="flex items-center text-xs text-gray-600 mb-1">
                            <User className="w-3 h-3 mr-1" />
                            You
                          </div>
                          <p className="text-gray-800">{exchange.response}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Right Column - Session Info & Analysis */}
          <div className="space-y-6">
            {/* Session Info */}
            {session && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Hash className="w-5 h-5 mr-2" />
                    Session Info
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Session ID:</span>
                    <span className="font-mono text-xs">{session.session_id}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Stage:</span>
                    <span className="font-medium">{session.current_stage}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Questions:</span>
                    <span>{session.questions_asked}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Responses:</span>
                    <span>{session.responses_received}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Duration:</span>
                    <span className="flex items-center">
                      <Clock className="w-3 h-3 mr-1" />
                      {session.duration}s
                    </span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Status:</span>
                    <span className={`px-2 py-1 rounded-full text-xs ${
                      session.is_active 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {session.is_active ? 'Active' : 'Completed'}
                    </span>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Analysis Results */}
            {analysisResult && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <BarChart3 className="w-5 h-5 mr-2" />
                    Analysis Results
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-3">
                      <span className="text-white font-bold text-lg">
                        {analysisResult.mbti_type}
                      </span>
                    </div>
                    <p className="font-medium text-gray-800">MBTI Type</p>
                    <p className="text-sm text-gray-600">
                      Confidence: {Math.round((analysisResult.confidence || 0) * 100)}%
                    </p>
                  </div>

                  {analysisResult.personality_traits && (
                    <div>
                      <p className="font-medium text-gray-800 mb-2">Personality Traits:</p>
                      <div className="flex flex-wrap gap-2">
                        {Array.isArray(analysisResult.personality_traits) 
                          ? analysisResult.personality_traits.map((trait: string, index: number) => (
                              <span 
                                key={index}
                                className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs"
                              >
                                {trait}
                              </span>
                            ))
                          : Object.keys(analysisResult.personality_traits).map((trait: string, index: number) => (
                              <span 
                                key={index}
                                className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs"
                              >
                                {trait}
                              </span>
                            ))
                        }
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Database Status */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Database className="w-5 h-5 mr-2" />
                  Database Status
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Users Table</span>
                  <CheckCircle className="w-4 h-4 text-green-500" />
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Voice Sessions</span>
                  <CheckCircle className="w-4 h-4 text-green-500" />
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">User Profiles</span>
                  <CheckCircle className="w-4 h-4 text-green-500" />
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Profile Cards</span>
                  <CheckCircle className="w-4 h-4 text-green-500" />
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
} 