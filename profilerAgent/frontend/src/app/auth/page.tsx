'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/Button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { Input } from '@/components/ui/Input'
import { Shield, ArrowLeft, Send, CheckCircle, XCircle } from 'lucide-react'
import { useRouter } from 'next/navigation'

export default function AuthPage() {
  const router = useRouter()
  const [phoneNumber, setPhoneNumber] = useState('')
  const [firstName, setFirstName] = useState('')
  const [lastName, setLastName] = useState('')
  const [verificationCode, setVerificationCode] = useState('')
  const [response, setResponse] = useState<any>(null)
  const [loading, setLoading] = useState(false)

  const testRegister = async () => {
    setLoading(true)
    setResponse(null)

    try {
      const res = await fetch('http://localhost:8000/api/v1/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          phone_number: phoneNumber,
          first_name: firstName,
          last_name: lastName
        })
      })

      const data = await res.json()
      setResponse({ status: res.status, data })
    } catch (error: any) {
      setResponse({ error: error.message })
    } finally {
      setLoading(false)
    }
  }

  const testVerifySMS = async () => {
    setLoading(true)
    setResponse(null)

    try {
      const res = await fetch('http://localhost:8000/api/v1/auth/verify-sms', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          phone_number: phoneNumber,
          verification_code: verificationCode
        })
      })

      const data = await res.json()
      setResponse({ status: res.status, data })
    } catch (error: any) {
      setResponse({ error: error.message })
    } finally {
      setLoading(false)
    }
  }

  const testLogin = async () => {
    setLoading(true)
    setResponse(null)

    try {
      const res = await fetch('http://localhost:8000/api/v1/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          phone_number: phoneNumber
        })
      })

      const data = await res.json()
      setResponse({ status: res.status, data })
    } catch (error: any) {
      setResponse({ error: error.message })
    } finally {
      setLoading(false)
    }
  }

  const testResendSMS = async () => {
    setLoading(true)
    setResponse(null)

    try {
      const res = await fetch('http://localhost:8000/api/v1/auth/resend-sms', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          phone_number: phoneNumber
        })
      })

      const data = await res.json()
      setResponse({ status: res.status, data })
    } catch (error: any) {
      setResponse({ error: error.message })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center mb-8">
          <Button 
            variant="ghost" 
            onClick={() => router.push('/')}
            className="mr-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            返回主页
          </Button>
          <div className="flex items-center">
            <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mr-4">
              <Shield className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-800">用户认证模块</h1>
              <p className="text-gray-600">测试用户注册、登录和验证功能</p>
            </div>
          </div>
        </div>

        <div className="max-w-6xl mx-auto grid lg:grid-cols-2 gap-8">
          {/* Input Form */}
          <Card>
            <CardHeader>
              <CardTitle>测试数据</CardTitle>
              <CardDescription>
                填写测试数据用于API调用
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Input
                label="手机号码"
                placeholder="+8613380029302"
                value={phoneNumber}
                onChange={(e) => setPhoneNumber(e.target.value)}
              />

              <div className="grid grid-cols-2 gap-4">
                <Input
                  label="名字"
                  placeholder="张"
                  value={firstName}
                  onChange={(e) => setFirstName(e.target.value)}
                />
                <Input
                  label="姓氏"
                  placeholder="三"
                  value={lastName}
                  onChange={(e) => setLastName(e.target.value)}
                />
              </div>

              <Input
                label="验证码"
                placeholder="123456"
                value={verificationCode}
                onChange={(e) => setVerificationCode(e.target.value)}
              />
            </CardContent>
          </Card>

          {/* API Tests */}
          <Card>
            <CardHeader>
              <CardTitle>认证API测试</CardTitle>
              <CardDescription>
                测试不同的认证端点
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button
                onClick={testRegister}
                disabled={loading || !phoneNumber || !firstName || !lastName}
                loading={loading}
                className="w-full"
              >
                <Send className="w-4 h-4 mr-2" />
                测试用户注册
              </Button>

              <Button
                onClick={testVerifySMS}
                disabled={loading || !phoneNumber || !verificationCode}
                loading={loading}
                className="w-full"
                variant="secondary"
              >
                <CheckCircle className="w-4 h-4 mr-2" />
                测试SMS验证
              </Button>

              <Button
                onClick={testLogin}
                disabled={loading || !phoneNumber}
                loading={loading}
                className="w-full"
                variant="outline"
              >
                <Send className="w-4 h-4 mr-2" />
                测试用户登录
              </Button>

              <Button
                onClick={testResendSMS}
                disabled={loading || !phoneNumber}
                loading={loading}
                className="w-full"
                variant="ghost"
              >
                <Send className="w-4 h-4 mr-2" />
                重新发送SMS
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Response Display */}
        {response && (
          <Card className="max-w-6xl mx-auto mt-8">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                {response.error ? (
                  <XCircle className="w-5 h-5 text-red-500" />
                ) : (
                  <CheckCircle className="w-5 h-5 text-green-500" />
                )}
                <span>API响应</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <pre className="bg-gray-100 p-4 rounded-lg overflow-auto text-sm">
                {JSON.stringify(response, null, 2)}
              </pre>
            </CardContent>
          </Card>
        )}

        {/* API Documentation */}
        <Card className="max-w-6xl mx-auto mt-8">
          <CardHeader>
            <CardTitle>认证API文档</CardTitle>
            <CardDescription>
              可用的认证端点说明
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="border-l-4 border-blue-500 pl-4">
                <h3 className="font-semibold">POST /api/v1/auth/register</h3>
                <p className="text-sm text-gray-600">用户注册，自动发送SMS验证码</p>
              </div>
              <div className="border-l-4 border-green-500 pl-4">
                <h3 className="font-semibold">POST /api/v1/auth/verify-sms</h3>
                <p className="text-sm text-gray-600">验证SMS验证码，返回JWT token</p>
              </div>
              <div className="border-l-4 border-purple-500 pl-4">
                <h3 className="font-semibold">POST /api/v1/auth/login</h3>
                <p className="text-sm text-gray-600">用户登录</p>
              </div>
              <div className="border-l-4 border-orange-500 pl-4">
                <h3 className="font-semibold">POST /api/v1/auth/resend-sms</h3>
                <p className="text-sm text-gray-600">重新发送SMS验证码</p>
              </div>
              <div className="border-l-4 border-red-500 pl-4">
                <h3 className="font-semibold">POST /api/v1/auth/logout</h3>
                <p className="text-sm text-gray-600">用户登出</p>
              </div>
              <div className="border-l-4 border-teal-500 pl-4">
                <h3 className="font-semibold">GET /api/v1/auth/me</h3>
                <p className="text-sm text-gray-600">获取当前用户信息</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
} 