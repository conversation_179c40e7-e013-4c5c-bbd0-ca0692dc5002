'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { Input } from '@/components/ui/Input'
import { Mic, ArrowLeft, Phone, Play, BarChart3, CheckCircle, XCircle } from 'lucide-react'
import { useRouter } from 'next/navigation'

export default function VoicePage() {
  const router = useRouter()
  const [userId, setUserId] = useState('')
  const [response, setResponse] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [lastApiCall, setLastApiCall] = useState<string>('')

  const testVoiceStatus = async () => {
    setLoading(true)
    setResponse(null)
    setLastApiCall(`GET /voice/status/${userId}`)

    try {
      const res = await fetch(`http://localhost:8000/voice/status/${userId}`)
      const data = await res.json()
      setResponse({
        status: res.status,
        data,
        timestamp: new Date().toISOString(),
        api: `GET /voice/status/${userId}`
      })
    } catch (error: any) {
      setResponse({
        error: error.message,
        timestamp: new Date().toISOString(),
        api: `GET /voice/status/${userId}`
      })
    } finally {
      setLoading(false)
    }
  }

  const testInitiateCall = async () => {
    setLoading(true)
    setResponse(null)
    setLastApiCall(`POST /voice/initiate/${userId}`)

    try {
      const res = await fetch(`http://localhost:8000/voice/initiate/${userId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      })

      const data = await res.json()
      setResponse({
        status: res.status,
        data,
        timestamp: new Date().toISOString(),
        api: `POST /voice/initiate/${userId}`
      })
    } catch (error: any) {
      setResponse({
        error: error.message,
        timestamp: new Date().toISOString(),
        api: `POST /voice/initiate/${userId}`
      })
    } finally {
      setLoading(false)
    }
  }

  const testVoiceAnalysis = async () => {
    setLoading(true)
    setResponse(null)
    setLastApiCall(`GET /voice/analysis/${userId}`)

    try {
      const res = await fetch(`http://localhost:8000/voice/analysis/${userId}`)
      const data = await res.json()
      setResponse({
        status: res.status,
        data,
        timestamp: new Date().toISOString(),
        api: `GET /voice/analysis/${userId}`
      })
    } catch (error: any) {
      setResponse({
        error: error.message,
        timestamp: new Date().toISOString(),
        api: `GET /voice/analysis/${userId}`
      })
    } finally {
      setLoading(false)
    }
  }

  const testHealthCheck = async () => {
    setLoading(true)
    setResponse(null)
    setLastApiCall('GET /health')

    try {
      const res = await fetch(`http://localhost:8000/health`)
      const data = await res.json()
      setResponse({
        status: res.status,
        data,
        timestamp: new Date().toISOString(),
        api: 'GET /health'
      })
    } catch (error: any) {
      setResponse({
        error: error.message,
        timestamp: new Date().toISOString(),
        api: 'GET /health'
      })
    } finally {
      setLoading(false)
    }
  }

  const clearResponse = () => {
    setResponse(null)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center mb-8">
          <Button 
            variant="ghost" 
            onClick={() => router.push('/')}
            className="mr-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            返回主页
          </Button>
          <div className="flex items-center">
            <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mr-4">
              <Mic className="w-6 h-6 text-green-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-800">语音服务模块</h1>
              <p className="text-gray-600">测试AI语音面试和分析功能</p>
            </div>
          </div>
        </div>

        <div className="max-w-6xl mx-auto grid lg:grid-cols-2 gap-8">
          {/* Input Form */}
          <Card>
            <CardHeader>
              <CardTitle>测试数据</CardTitle>
              <CardDescription>
                输入用户ID进行语音服务测试
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">用户ID</label>
                <Input
                  placeholder="输入用户ID，例如：user_12345"
                  value={userId}
                  onChange={(e) => setUserId(e.target.value)}
                />
                <div className="flex gap-2 flex-wrap">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setUserId('test_user_001')}
                  >
                    测试用户1
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setUserId('test_user_002')}
                  >
                    测试用户2
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setUserId('')}
                  >
                    清空
                  </Button>
                </div>
              </div>

              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">语音服务流程</h4>
                <ol className="text-sm text-blue-800 space-y-1">
                  <li>1. 健康检查 - 确认服务运行</li>
                  <li>2. 检查语音状态 - 查看用户状态</li>
                  <li>3. 发起语音通话 - 开始outgoing call</li>
                  <li>4. 获取分析结果 - 查看AI分析</li>
                </ol>
              </div>

              {lastApiCall && (
                <div className="bg-gray-50 p-3 rounded-lg">
                  <p className="text-sm text-gray-600">
                    <span className="font-medium">最后调用:</span> {lastApiCall}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* API Tests */}
          <Card>
            <CardHeader>
              <CardTitle>语音API测试</CardTitle>
              <CardDescription>
                测试不同的语音服务端点
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button
                onClick={testVoiceStatus}
                disabled={loading || !userId}
                loading={loading}
                className="w-full"
                variant="outline"
              >
                <CheckCircle className="w-4 h-4 mr-2" />
                检查语音状态
              </Button>

              <Button
                onClick={testInitiateCall}
                disabled={loading || !userId}
                loading={loading}
                className="w-full"
              >
                <Phone className="w-4 h-4 mr-2" />
                发起语音通话
              </Button>

              <Button
                onClick={testVoiceAnalysis}
                disabled={loading || !userId}
                loading={loading}
                className="w-full"
                variant="secondary"
              >
                <BarChart3 className="w-4 h-4 mr-2" />
                获取语音分析
              </Button>

              <Button
                onClick={testHealthCheck}
                disabled={loading}
                loading={loading}
                className="w-full"
                variant="ghost"
              >
                <CheckCircle className="w-4 h-4 mr-2" />
                健康检查
              </Button>

              <Button
                onClick={clearResponse}
                disabled={loading}
                className="w-full"
                variant="outline"
              >
                <XCircle className="w-4 h-4 mr-2" />
                清空响应
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Response Display */}
        {response && (
          <Card className="max-w-6xl mx-auto mt-8">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  {response.error ? (
                    <XCircle className="w-5 h-5 text-red-500" />
                  ) : (
                    <CheckCircle className="w-5 h-5 text-green-500" />
                  )}
                  <span>API响应</span>
                </div>
                <div className="text-sm text-gray-500">
                  {response.timestamp && new Date(response.timestamp).toLocaleTimeString()}
                </div>
              </CardTitle>
              {response.api && (
                <CardDescription>
                  <code className="bg-gray-100 px-2 py-1 rounded text-sm">{response.api}</code>
                </CardDescription>
              )}
            </CardHeader>
            <CardContent>
              {response.error ? (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <h4 className="font-medium text-red-800 mb-2">错误信息</h4>
                  <p className="text-red-700">{response.error}</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {response.status && (
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium">HTTP状态:</span>
                      <span className={`px-2 py-1 rounded text-sm ${
                        response.status >= 200 && response.status < 300
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {response.status}
                      </span>
                    </div>
                  )}
                  <div>
                    <h4 className="font-medium mb-2">响应数据</h4>
                    <pre className="bg-gray-100 p-4 rounded-lg overflow-auto text-sm">
                      {JSON.stringify(response.data || response, null, 2)}
                    </pre>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* API Documentation */}
        <Card className="max-w-6xl mx-auto mt-8">
          <CardHeader>
            <CardTitle>语音API文档</CardTitle>
            <CardDescription>
              可用的语音服务端点说明
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="border-l-4 border-gray-500 pl-4">
                <h3 className="font-semibold">GET /api/v1/health</h3>
                <p className="text-sm text-gray-600">系统健康检查，确认服务运行状态</p>
              </div>
              <div className="border-l-4 border-blue-500 pl-4">
                <h3 className="font-semibold">GET /voice/status/{`{user_id}`}</h3>
                <p className="text-sm text-gray-600">获取用户语音通话状态</p>
              </div>
              <div className="border-l-4 border-green-500 pl-4">
                <h3 className="font-semibold">POST /voice/initiate/{`{user_id}`}</h3>
                <p className="text-sm text-gray-600">发起语音通话，返回Twilio通话信息</p>
              </div>
              <div className="border-l-4 border-purple-500 pl-4">
                <h3 className="font-semibold">GET /voice/analysis/{`{user_id}`}</h3>
                <p className="text-sm text-gray-600">获取用户语音分析结果</p>
              </div>
              <div className="border-l-4 border-orange-500 pl-4">
                <h3 className="font-semibold">POST /voice/retry/{`{user_id}`}</h3>
                <p className="text-sm text-gray-600">重新开始语音面试</p>
              </div>
              <div className="border-l-4 border-red-500 pl-4">
                <h3 className="font-semibold">POST /voice/webhook/incoming</h3>
                <p className="text-sm text-gray-600">Twilio来电webhook处理</p>
              </div>
              <div className="border-l-4 border-teal-500 pl-4">
                <h3 className="font-semibold">POST /api/v1/voice/webhook/recording</h3>
                <p className="text-sm text-gray-600">Twilio录音完成webhook处理</p>
              </div>
              <div className="border-l-4 border-indigo-500 pl-4">
                <h3 className="font-semibold">POST /api/v1/voice/webhook/status</h3>
                <p className="text-sm text-gray-600">Twilio通话状态webhook处理</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Features Info */}
        <Card className="max-w-6xl mx-auto mt-8">
          <CardHeader>
            <CardTitle>语音服务特性</CardTitle>
            <CardDescription>
              了解我们的AI语音分析能力
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold mb-2">🎙️ 智能语音面试</h4>
                <p className="text-sm text-gray-600 mb-4">
                  通过自然对话了解用户的性格特征、兴趣爱好和价值观
                </p>
              </div>
              <div>
                <h4 className="font-semibold mb-2">🧠 AI情感分析</h4>
                <p className="text-sm text-gray-600 mb-4">
                  分析语音中的情感色彩、语调变化和表达方式
                </p>
              </div>
              <div>
                <h4 className="font-semibold mb-2">📊 个性画像生成</h4>
                <p className="text-sm text-gray-600 mb-4">
                  基于语音数据生成详细的个性特征和匹配偏好
                </p>
              </div>
              <div>
                <h4 className="font-semibold mb-2">🔒 隐私保护</h4>
                <p className="text-sm text-gray-600 mb-4">
                  所有语音数据经过加密处理，确保用户隐私安全
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
} 