'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/Button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { Input } from '@/components/ui/Input'
import { Heart, ArrowLeft, Users, ThumbsUp, ThumbsDown, History, BarChart3, CheckCircle, XCircle } from 'lucide-react'
import { useRouter } from 'next/navigation'

export default function MatchesPage() {
  const router = useRouter()
  const [token, setToken] = useState('')
  const [matchId, setMatchId] = useState('')
  const [limit, setLimit] = useState('10')
  const [page, setPage] = useState('1')
  const [response, setResponse] = useState<any>(null)
  const [loading, setLoading] = useState(false)

  const getAuthHeaders = () => ({
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  })

  const testGetRecommendations = async () => {
    setLoading(true)
    setResponse(null)

    try {
      const res = await fetch(`http://localhost:8000/api/v1/matches/recommendations?limit=${limit}`, {
        headers: getAuthHeaders()
      })
      const data = await res.json()
      setResponse({ status: res.status, data })
    } catch (error: any) {
      setResponse({ error: error.message })
    } finally {
      setLoading(false)
    }
  }

  const testSubmitFeedback = async (action: 'interested' | 'passed') => {
    setLoading(true)
    setResponse(null)

    try {
      const res = await fetch('http://localhost:8000/api/v1/matches/feedback', {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify({
          match_id: matchId,
          action: action
        })
      })
      const data = await res.json()
      setResponse({ status: res.status, data })
    } catch (error: any) {
      setResponse({ error: error.message })
    } finally {
      setLoading(false)
    }
  }

  const testGetHistory = async () => {
    setLoading(true)
    setResponse(null)

    try {
      const res = await fetch(`http://localhost:8000/api/v1/matches/history?page=${page}&limit=${limit}`, {
        headers: getAuthHeaders()
      })
      const data = await res.json()
      setResponse({ status: res.status, data })
    } catch (error: any) {
      setResponse({ error: error.message })
    } finally {
      setLoading(false)
    }
  }

  const testGetMutualMatches = async () => {
    setLoading(true)
    setResponse(null)

    try {
      const res = await fetch('http://localhost:8000/api/v1/matches/mutual', {
        headers: getAuthHeaders()
      })
      const data = await res.json()
      setResponse({ status: res.status, data })
    } catch (error: any) {
      setResponse({ error: error.message })
    } finally {
      setLoading(false)
    }
  }

  const testGetStats = async () => {
    setLoading(true)
    setResponse(null)

    try {
      const res = await fetch('http://localhost:8000/api/v1/matches/stats', {
        headers: getAuthHeaders()
      })
      const data = await res.json()
      setResponse({ status: res.status, data })
    } catch (error: any) {
      setResponse({ error: error.message })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 to-red-50 p-4">
      <div className="max-w-4xl mx-auto">
        <div className="flex items-center gap-4 mb-8">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push('/')}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Home
          </Button>
          <div className="flex items-center gap-2">
            <Heart className="w-6 h-6 text-pink-600" />
            <h1 className="text-2xl font-bold text-gray-900">Matches API Testing</h1>
          </div>
        </div>

        {/* Input Section */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="w-5 h-5 text-pink-600" />
              Test Configuration
            </CardTitle>
            <CardDescription>
              Configure authentication and parameters for matches API testing
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                JWT Token
              </label>
              <Input
                type="text"
                placeholder="Enter JWT token"
                value={token}
                onChange={(e) => setToken(e.target.value)}
              />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Match ID (for feedback)
                </label>
                <Input
                  type="text"
                  placeholder="Enter match ID"
                  value={matchId}
                  onChange={(e) => setMatchId(e.target.value)}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Limit
                </label>
                <Input
                  type="number"
                  placeholder="10"
                  value={limit}
                  onChange={(e) => setLimit(e.target.value)}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Page
                </label>
                <Input
                  type="number"
                  placeholder="1"
                  value={page}
                  onChange={(e) => setPage(e.target.value)}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* API Testing Buttons */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Get Recommendations</CardTitle>
              <CardDescription>Get match recommendations</CardDescription>
            </CardHeader>
            <CardContent>
              <Button
                onClick={testGetRecommendations}
                disabled={loading || !token}
                className="w-full"
              >
                <Users className="w-4 h-4 mr-2" />
                Get Recommendations
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Submit Feedback</CardTitle>
              <CardDescription>Like or pass on a match</CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button
                onClick={() => testSubmitFeedback('interested')}
                disabled={loading || !token || !matchId}
                className="w-full bg-green-600 hover:bg-green-700"
              >
                <ThumbsUp className="w-4 h-4 mr-2" />
                Interested
              </Button>
              <Button
                onClick={() => testSubmitFeedback('passed')}
                disabled={loading || !token || !matchId}
                className="w-full bg-red-600 hover:bg-red-700"
              >
                <ThumbsDown className="w-4 h-4 mr-2" />
                Pass
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Match History</CardTitle>
              <CardDescription>Get match history with pagination</CardDescription>
            </CardHeader>
            <CardContent>
              <Button
                onClick={testGetHistory}
                disabled={loading || !token}
                className="w-full"
              >
                <History className="w-4 h-4 mr-2" />
                Get History
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Mutual Matches</CardTitle>
              <CardDescription>Get mutual matches</CardDescription>
            </CardHeader>
            <CardContent>
              <Button
                onClick={testGetMutualMatches}
                disabled={loading || !token}
                className="w-full"
              >
                <Heart className="w-4 h-4 mr-2" />
                Get Mutual Matches
              </Button>
            </CardContent>
          </Card>

          <Card className="md:col-span-2">
            <CardHeader>
              <CardTitle className="text-lg">Match Statistics</CardTitle>
              <CardDescription>Get match statistics and analytics</CardDescription>
            </CardHeader>
            <CardContent>
              <Button
                onClick={testGetStats}
                disabled={loading || !token}
                className="w-full"
              >
                <BarChart3 className="w-4 h-4 mr-2" />
                Get Statistics
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Response Display */}
        {response && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                {response.error ? (
                  <XCircle className="w-5 h-5 text-red-500" />
                ) : (
                  <CheckCircle className="w-5 h-5 text-green-500" />
                )}
                API Response
              </CardTitle>
            </CardHeader>
            <CardContent>
              <pre className="bg-gray-100 p-4 rounded-lg overflow-auto text-sm">
                {JSON.stringify(response, null, 2)}
              </pre>
            </CardContent>
          </Card>
        )}

        {loading && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
            <div className="bg-white p-6 rounded-lg">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-pink-600 mx-auto"></div>
              <p className="mt-2 text-center">Loading...</p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
