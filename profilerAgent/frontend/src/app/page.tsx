'use client'

import { <PERSON><PERSON> } from '@/components/ui/Button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { Heart, Shield, Mic, Briefcase, User, Users, UserCircle } from 'lucide-react'
import { useRouter } from 'next/navigation'

export default function Home() {
  const router = useRouter()

  const navigateToAuth = () => {
    router.push('/auth')
  }

  const navigateToVoice = () => {
    router.push('/voice')
  }

  const navigateToLinkedIn = () => {
    router.push('/linkedin')
  }

  const navigateToProfile = () => {
    router.push('/profile')
  }

  const navigateToMatches = () => {
    router.push('/matches')
  }

  const navigateToUsers = () => {
    router.push('/users')
  }

  const navigateToTestVoice = () => {
    router.push('/test-voice')
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-500 rounded-3xl mb-6">
            <Heart className="w-10 h-10 text-white" />
          </div>
          <h1 className="text-4xl font-bold text-gray-800 mb-4">
            Dating App Dashboard
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Choose a service to get started with your dating journey
          </p>
        </div>

        {/* Navigation Cards */}
        <div className="max-w-6xl mx-auto grid md:grid-cols-3 gap-8 mb-12">
          {/* User Authentication */}
          <Card className="hover:shadow-lg transition-shadow cursor-pointer group">
            <CardHeader className="text-center pb-4">
              <div className="w-16 h-16 bg-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:bg-blue-200 transition-colors">
                <Shield className="w-8 h-8 text-blue-600" />
              </div>
              <CardTitle className="text-xl mb-2">用户认证</CardTitle>
              <CardDescription className="text-base">
                注册、登录、SMS验证等用户认证功能
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-0">
              <ul className="text-sm text-gray-600 mb-6 space-y-2">
                <li>• 用户注册与登录</li>
                <li>• SMS验证码验证</li>
                <li>• 用户信息管理</li>
                <li>• 安全认证流程</li>
              </ul>
              <Button 
                onClick={navigateToAuth}
                className="w-full"
                variant="primary"
              >
                进入认证模块
              </Button>
            </CardContent>
          </Card>

          {/* Voice Service */}
          <Card className="hover:shadow-lg transition-shadow cursor-pointer group">
            <CardHeader className="text-center pb-4">
              <div className="w-16 h-16 bg-green-100 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:bg-green-200 transition-colors">
                <Mic className="w-8 h-8 text-green-600" />
              </div>
              <CardTitle className="text-xl mb-2">语音服务</CardTitle>
              <CardDescription className="text-base">
                AI语音面试、录音分析和个性化匹配
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-0">
              <ul className="text-sm text-gray-600 mb-6 space-y-2">
                <li>• 语音通话发起</li>
                <li>• AI语音分析</li>
                <li>• 个性特征识别</li>
                <li>• 匹配算法优化</li>
              </ul>
              <Button 
                onClick={navigateToVoice}
                className="w-full"
                variant="primary"
              >
                进入语音模块
              </Button>
            </CardContent>
          </Card>

          {/* LinkedIn Integration */}
          <Card className="hover:shadow-lg transition-shadow cursor-pointer group">
            <CardHeader className="text-center pb-4">
              <div className="w-16 h-16 bg-purple-100 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:bg-purple-200 transition-colors">
                <Briefcase className="w-8 h-8 text-purple-600" />
              </div>
              <CardTitle className="text-xl mb-2">LinkedIn集成</CardTitle>
              <CardDescription className="text-base">
                职业背景验证和社交媒体集成
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-0">
              <ul className="text-sm text-gray-600 mb-6 space-y-2">
                <li>• LinkedIn OAuth授权</li>
                <li>• 职业背景验证</li>
                <li>• 社交资料同步</li>
                <li>• 专业信息匹配</li>
              </ul>
              <Button 
                onClick={navigateToLinkedIn}
                className="w-full"
                variant="primary"
              >
                进入LinkedIn模块
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* New API Testing Section */}
        <div className="max-w-6xl mx-auto mb-12">
          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold text-gray-800 mb-2">
              API Testing Modules
            </h2>
            <p className="text-gray-600">
              Test the new Profile, Matches, and Users API endpoints
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Profile API */}
            <Card className="hover:shadow-lg transition-shadow cursor-pointer group">
              <CardHeader className="text-center pb-4">
                <div className="w-16 h-16 bg-purple-100 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:bg-purple-200 transition-colors">
                  <User className="w-8 h-8 text-purple-600" />
                </div>
                <CardTitle className="text-xl mb-2">Profile API</CardTitle>
                <CardDescription className="text-base">
                  用户画像生成、分析和卡片功能
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-0">
                <ul className="text-sm text-gray-600 mb-6 space-y-2">
                  <li>• 获取画像卡片</li>
                  <li>• 生成最终画像</li>
                  <li>• 详细分析结果</li>
                  <li>• 画像生成状态</li>
                </ul>
                <Button
                  onClick={navigateToProfile}
                  className="w-full"
                  variant="primary"
                >
                  测试Profile API
                </Button>
              </CardContent>
            </Card>

            {/* Matches API */}
            <Card className="hover:shadow-lg transition-shadow cursor-pointer group">
              <CardHeader className="text-center pb-4">
                <div className="w-16 h-16 bg-pink-100 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:bg-pink-200 transition-colors">
                  <Users className="w-8 h-8 text-pink-600" />
                </div>
                <CardTitle className="text-xl mb-2">Matches API</CardTitle>
                <CardDescription className="text-base">
                  智能匹配推荐和反馈系统
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-0">
                <ul className="text-sm text-gray-600 mb-6 space-y-2">
                  <li>• 获取匹配推荐</li>
                  <li>• 提交匹配反馈</li>
                  <li>• 查看匹配历史</li>
                  <li>• 相互匹配管理</li>
                </ul>
                <Button
                  onClick={navigateToMatches}
                  className="w-full"
                  variant="primary"
                >
                  测试Matches API
                </Button>
              </CardContent>
            </Card>

            {/* Users API */}
            <Card className="hover:shadow-lg transition-shadow cursor-pointer group">
              <CardHeader className="text-center pb-4">
                <div className="w-16 h-16 bg-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:bg-blue-200 transition-colors">
                  <UserCircle className="w-8 h-8 text-blue-600" />
                </div>
                <CardTitle className="text-xl mb-2">Users API</CardTitle>
                <CardDescription className="text-base">
                  用户信息和偏好设置管理
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-0">
                <ul className="text-sm text-gray-600 mb-6 space-y-2">
                  <li>• 用户信息管理</li>
                  <li>• 偏好设置更新</li>
                  <li>• 用户状态查询</li>
                  <li>• 账户管理操作</li>
                </ul>
                <Button
                  onClick={navigateToUsers}
                  className="w-full"
                  variant="primary"
                >
                  测试Users API
                </Button>
              </CardContent>
            </Card>

            {/* Test Voice API */}
            <Card className="hover:shadow-lg transition-shadow cursor-pointer group border-2 border-green-200">
              <CardHeader className="text-center pb-4">
                <div className="w-16 h-16 bg-green-100 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:bg-green-200 transition-colors">
                  <Mic className="w-8 h-8 text-green-600" />
                </div>
                <CardTitle className="text-xl mb-2">Test Voice API</CardTitle>
                <CardDescription className="text-base">
                  语音面试测试工具 (无需Twilio)
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-0">
                <ul className="text-sm text-gray-600 mb-6 space-y-2">
                  <li>• 文本模拟语音面试</li>
                  <li>• 完整的6阶段对话</li>
                  <li>• 实时AI分析</li>
                  <li>• 数据库完整更新</li>
                </ul>
                <Button
                  onClick={navigateToTestVoice}
                  className="w-full bg-green-600 hover:bg-green-700"
                  variant="primary"
                >
                  开始测试面试
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center mt-16">
          <p className="text-gray-500">
            选择上方任一模块开始体验我们的约会应用功能
          </p>
        </div>
      </div>
    </div>
  )
}
