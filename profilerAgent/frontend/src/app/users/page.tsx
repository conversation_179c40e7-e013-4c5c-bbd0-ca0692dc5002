'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { Input } from '@/components/ui/Input'
import { Textarea } from '@/components/ui/Textarea'
import { UserCircle, ArrowLeft, Settings, Eye, Edit, Trash2, CheckCircle, XCircle } from 'lucide-react'
import { useRouter } from 'next/navigation'

export default function UsersPage() {
  const router = useRouter()
  const [token, setToken] = useState('')
  const [response, setResponse] = useState<any>(null)
  const [loading, setLoading] = useState(false)

  // Profile update fields
  const [firstName, setFirstName] = useState('')
  const [lastName, setLastName] = useState('')
  const [age, setAge] = useState('')
  const [city, setCity] = useState('')
  const [profession, setProfession] = useState('')
  const [bio, setBio] = useState('')

  // Preferences fields
  const [ageRangeMin, setAgeRangeMin] = useState('')
  const [ageRangeMax, setAgeRangeMax] = useState('')
  const [maxDistance, setMaxDistance] = useState('')
  const [interests, setInterests] = useState('')

  const getAuthHeaders = () => ({
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  })

  const testGetProfile = async () => {
    setLoading(true)
    setResponse(null)

    try {
      const res = await fetch('http://localhost:8000/api/v1/users/profile', {
        headers: getAuthHeaders()
      })
      const data = await res.json()
      setResponse({ status: res.status, data })
    } catch (error: any) {
      setResponse({ error: error.message })
    } finally {
      setLoading(false)
    }
  }

  const testUpdateProfile = async () => {
    setLoading(true)
    setResponse(null)

    const updateData: any = {}
    if (firstName) updateData.first_name = firstName
    if (lastName) updateData.last_name = lastName
    if (age) updateData.age = parseInt(age)
    if (city) updateData.city = city
    if (profession) updateData.profession = profession
    if (bio) updateData.bio = bio

    try {
      const res = await fetch('http://localhost:8000/api/v1/users/profile', {
        method: 'PUT',
        headers: getAuthHeaders(),
        body: JSON.stringify(updateData)
      })
      const data = await res.json()
      setResponse({ status: res.status, data })
    } catch (error: any) {
      setResponse({ error: error.message })
    } finally {
      setLoading(false)
    }
  }

  const testGetPreferences = async () => {
    setLoading(true)
    setResponse(null)

    try {
      const res = await fetch('http://localhost:8000/api/v1/users/preferences', {
        headers: getAuthHeaders()
      })
      const data = await res.json()
      setResponse({ status: res.status, data })
    } catch (error: any) {
      setResponse({ error: error.message })
    } finally {
      setLoading(false)
    }
  }

  const testUpdatePreferences = async () => {
    setLoading(true)
    setResponse(null)

    const preferencesData: any = {}
    if (ageRangeMin) preferencesData.age_range_min = parseInt(ageRangeMin)
    if (ageRangeMax) preferencesData.age_range_max = parseInt(ageRangeMax)
    if (maxDistance) preferencesData.max_distance = parseInt(maxDistance)
    if (interests) preferencesData.interests = interests.split(',').map(i => i.trim())

    try {
      const res = await fetch('http://localhost:8000/api/v1/users/preferences', {
        method: 'PUT',
        headers: getAuthHeaders(),
        body: JSON.stringify(preferencesData)
      })
      const data = await res.json()
      setResponse({ status: res.status, data })
    } catch (error: any) {
      setResponse({ error: error.message })
    } finally {
      setLoading(false)
    }
  }

  const testGetStatus = async () => {
    setLoading(true)
    setResponse(null)

    try {
      const res = await fetch('http://localhost:8000/api/v1/users/status', {
        headers: getAuthHeaders()
      })
      const data = await res.json()
      setResponse({ status: res.status, data })
    } catch (error: any) {
      setResponse({ error: error.message })
    } finally {
      setLoading(false)
    }
  }

  const testDeleteAccount = async () => {
    if (!confirm('Are you sure you want to delete this account? This action cannot be undone.')) {
      return
    }

    setLoading(true)
    setResponse(null)

    try {
      const res = await fetch('http://localhost:8000/api/v1/users/account', {
        method: 'DELETE',
        headers: getAuthHeaders()
      })
      const data = await res.json()
      setResponse({ status: res.status, data })
    } catch (error: any) {
      setResponse({ error: error.message })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-50 p-4">
      <div className="max-w-4xl mx-auto">
        <div className="flex items-center gap-4 mb-8">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push('/')}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Home
          </Button>
          <div className="flex items-center gap-2">
            <UserCircle className="w-6 h-6 text-blue-600" />
            <h1 className="text-2xl font-bold text-gray-900">Users API Testing</h1>
          </div>
        </div>

        {/* Token Input */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="w-5 h-5 text-blue-600" />
              Authentication
            </CardTitle>
            <CardDescription>
              Enter JWT token for user API testing
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Input
              type="text"
              placeholder="Enter JWT token"
              value={token}
              onChange={(e) => setToken(e.target.value)}
            />
          </CardContent>
        </Card>

        {/* Profile Management */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Profile Management</CardTitle>
              <CardDescription>View and update user profile</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-2">
                <Input
                  placeholder="First Name"
                  value={firstName}
                  onChange={(e) => setFirstName(e.target.value)}
                />
                <Input
                  placeholder="Last Name"
                  value={lastName}
                  onChange={(e) => setLastName(e.target.value)}
                />
              </div>
              <div className="grid grid-cols-2 gap-2">
                <Input
                  type="number"
                  placeholder="Age"
                  value={age}
                  onChange={(e) => setAge(e.target.value)}
                />
                <Input
                  placeholder="City"
                  value={city}
                  onChange={(e) => setCity(e.target.value)}
                />
              </div>
              <Input
                placeholder="Profession"
                value={profession}
                onChange={(e) => setProfession(e.target.value)}
              />
              <Textarea
                placeholder="Bio"
                value={bio}
                onChange={(e) => setBio(e.target.value)}
                rows={3}
              />
              <div className="flex gap-2">
                <Button
                  onClick={testGetProfile}
                  disabled={loading || !token}
                  variant="outline"
                  className="flex-1"
                >
                  <Eye className="w-4 h-4 mr-2" />
                  Get Profile
                </Button>
                <Button
                  onClick={testUpdateProfile}
                  disabled={loading || !token}
                  className="flex-1"
                >
                  <Edit className="w-4 h-4 mr-2" />
                  Update Profile
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Preferences Management</CardTitle>
              <CardDescription>View and update user preferences</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-2">
                <Input
                  type="number"
                  placeholder="Min Age"
                  value={ageRangeMin}
                  onChange={(e) => setAgeRangeMin(e.target.value)}
                />
                <Input
                  type="number"
                  placeholder="Max Age"
                  value={ageRangeMax}
                  onChange={(e) => setAgeRangeMax(e.target.value)}
                />
              </div>
              <Input
                type="number"
                placeholder="Max Distance (km)"
                value={maxDistance}
                onChange={(e) => setMaxDistance(e.target.value)}
              />
              <Textarea
                placeholder="Interests (comma separated)"
                value={interests}
                onChange={(e) => setInterests(e.target.value)}
                rows={3}
              />
              <div className="flex gap-2">
                <Button
                  onClick={testGetPreferences}
                  disabled={loading || !token}
                  variant="outline"
                  className="flex-1"
                >
                  <Eye className="w-4 h-4 mr-2" />
                  Get Preferences
                </Button>
                <Button
                  onClick={testUpdatePreferences}
                  disabled={loading || !token}
                  className="flex-1"
                >
                  <Edit className="w-4 h-4 mr-2" />
                  Update Preferences
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Status and Account Management */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">User Status</CardTitle>
              <CardDescription>Check user status and permissions</CardDescription>
            </CardHeader>
            <CardContent>
              <Button
                onClick={testGetStatus}
                disabled={loading || !token}
                className="w-full"
              >
                <CheckCircle className="w-4 h-4 mr-2" />
                Get Status
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg text-red-600">Account Management</CardTitle>
              <CardDescription>Dangerous operations</CardDescription>
            </CardHeader>
            <CardContent>
              <Button
                onClick={testDeleteAccount}
                disabled={loading || !token}
                className="w-full bg-red-600 hover:bg-red-700"
              >
                <Trash2 className="w-4 h-4 mr-2" />
                Delete Account
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Response Display */}
        {response && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                {response.error ? (
                  <XCircle className="w-5 h-5 text-red-500" />
                ) : (
                  <CheckCircle className="w-5 h-5 text-green-500" />
                )}
                API Response
              </CardTitle>
            </CardHeader>
            <CardContent>
              <pre className="bg-gray-100 p-4 rounded-lg overflow-auto text-sm">
                {JSON.stringify(response, null, 2)}
              </pre>
            </CardContent>
          </Card>
        )}

        {loading && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
            <div className="bg-white p-6 rounded-lg">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-2 text-center">Loading...</p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
