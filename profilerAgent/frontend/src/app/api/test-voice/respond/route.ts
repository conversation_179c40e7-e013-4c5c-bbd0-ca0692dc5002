import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const { session_id, response } = await request.json()

    if (!session_id || !response) {
      return NextResponse.json(
        { success: false, message: 'Session ID and response are required' },
        { status: 400 }
      )
    }

    // 调用后端的TestVoiceService
    const apiResponse = await fetch('http://localhost:8000/api/v1/test-voice/respond', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ session_id, response })
    })

    const data = await apiResponse.json()
    
    return NextResponse.json(data, { status: apiResponse.status })
  } catch (error) {
    console.error('Test voice respond error:', error)
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    )
  }
} 