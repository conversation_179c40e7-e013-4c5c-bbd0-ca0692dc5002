@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

:root {
  /* 主色系 - 基于 HeyEmily.ai 优化 */
  --primary-50: #EEF2FF;
  --primary-100: #E0E7FF;
  --primary-500: #667EEA;
  --primary-600: #5B21B6;
  --primary-700: #764BA2;

  /* 强调色 - 温暖约会感 */
  --accent-400: #FF6B9D;
  --accent-500: #F472B6;
  --accent-600: #EC4899;

  /* 中性色系 */
  --neutral-50: #FAFBFC;
  --neutral-100: #F8FAFC;
  --neutral-200: #E5E7EB;
  --neutral-300: #D1D5DB;
  --neutral-400: #9CA3AF;
  --neutral-500: #6B7280;
  --neutral-600: #4B5563;
  --neutral-700: #374151;
  --neutral-800: #1F2937;
  --neutral-900: #111827;

  /* 状态色 */
  --success: #10B981;
  --warning: #F59E0B;
  --error: #EF4444;
  --info: #3B82F6;

  /* 应用主题 */
  --background: var(--neutral-50);
  --foreground: var(--neutral-800);
  --card-background: #ffffff;
  --border-light: var(--neutral-200);
}

@theme inline {
  /* 颜色系统 */
  --color-primary-50: var(--primary-50);
  --color-primary-100: var(--primary-100);
  --color-primary-500: var(--primary-500);
  --color-primary-600: var(--primary-600);
  --color-primary-700: var(--primary-700);

  --color-accent-400: var(--accent-400);
  --color-accent-500: var(--accent-500);
  --color-accent-600: var(--accent-600);

  --color-neutral-50: var(--neutral-50);
  --color-neutral-100: var(--neutral-100);
  --color-neutral-200: var(--neutral-200);
  --color-neutral-300: var(--neutral-300);
  --color-neutral-400: var(--neutral-400);
  --color-neutral-500: var(--neutral-500);
  --color-neutral-600: var(--neutral-600);
  --color-neutral-700: var(--neutral-700);
  --color-neutral-800: var(--neutral-800);
  --color-neutral-900: var(--neutral-900);

  --color-success: var(--success);
  --color-warning: var(--warning);
  --color-error: var(--error);
  --color-info: var(--info);

  --color-background: var(--background);
  --color-foreground: var(--foreground);

  /* 字体系统 */
  --font-sans: 'Inter', system-ui, -apple-system, sans-serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', monospace;

  /* 间距系统 */
  --spacing-18: 4.5rem;
  --spacing-88: 22rem;

  /* 圆角系统 */
  --radius-xl: 12px;
  --radius-2xl: 16px;
  --radius-3xl: 24px;

  /* 阴影系统 */
  --shadow-soft: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-medium: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-large: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-hover: 0 4px 12px rgba(102, 126, 234, 0.15);
  --shadow-focus: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 基础组件样式 */
.btn {
  @apply inline-flex items-center justify-center rounded-xl px-6 py-3 text-sm font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
}

.btn-primary {
  @apply bg-gradient-to-r from-primary-500 to-primary-700 text-white hover:shadow-hover hover:-translate-y-0.5 focus:ring-primary-500;
}

.btn-secondary {
  @apply bg-white text-primary-500 border border-neutral-200 hover:border-primary-500 hover:shadow-medium focus:ring-primary-500;
}

.card {
  @apply bg-white rounded-xl border border-neutral-200 p-5 shadow-soft transition-all duration-200 hover:shadow-medium hover:-translate-y-0.5;
}

.input {
  @apply w-full px-4 py-3 border border-neutral-200 rounded-lg text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500;
}
