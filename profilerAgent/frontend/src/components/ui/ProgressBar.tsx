'use client'

import React from 'react'
import { cn } from '@/lib/utils'
import { motion } from 'framer-motion'

export interface ProgressBarProps {
  value: number // 0-100
  max?: number
  size?: 'sm' | 'md' | 'lg'
  variant?: 'default' | 'success' | 'warning' | 'error' | 'gradient'
  showLabel?: boolean
  label?: string
  animated?: boolean
  className?: string
}

const ProgressBar: React.FC<ProgressBarProps> = ({
  value,
  max = 100,
  size = 'md',
  variant = 'default',
  showLabel = false,
  label,
  animated = true,
  className
}) => {
  const percentage = Math.min(Math.max((value / max) * 100, 0), 100)
  
  const sizeStyles = {
    sm: 'h-2',
    md: 'h-3',
    lg: 'h-4'
  }
  
  const variantStyles = {
    default: 'bg-primary-500',
    success: 'bg-success',
    warning: 'bg-warning',
    error: 'bg-error',
    gradient: 'bg-gradient-to-r from-primary-500 to-accent-500'
  }
  
  return (
    <div className={cn('w-full', className)}>
      {(showLabel || label) && (
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm font-medium text-neutral-700">
            {label || '进度'}
          </span>
          <span className="text-sm text-neutral-500">
            {Math.round(percentage)}%
          </span>
        </div>
      )}
      
      <div className={cn(
        'w-full bg-neutral-200 rounded-full overflow-hidden',
        sizeStyles[size]
      )}>
        <motion.div
          className={cn(
            'h-full rounded-full transition-all duration-300',
            variantStyles[variant]
          )}
          initial={animated ? { width: 0 } : { width: `${percentage}%` }}
          animate={{ width: `${percentage}%` }}
          transition={{ 
            duration: animated ? 0.8 : 0,
            ease: 'easeOut'
          }}
        />
      </div>
    </div>
  )
}

// 圆形进度条组件
export interface CircularProgressProps {
  value: number // 0-100
  size?: number
  strokeWidth?: number
  variant?: 'default' | 'success' | 'warning' | 'error' | 'gradient'
  showLabel?: boolean
  label?: string
  animated?: boolean
  className?: string
}

const CircularProgress: React.FC<CircularProgressProps> = ({
  value,
  size = 120,
  strokeWidth = 8,
  variant = 'default',
  showLabel = true,
  label,
  animated = true,
  className
}) => {
  const percentage = Math.min(Math.max(value, 0), 100)
  const radius = (size - strokeWidth) / 2
  const circumference = radius * 2 * Math.PI
  const strokeDasharray = circumference
  const strokeDashoffset = circumference - (percentage / 100) * circumference
  
  const variantColors = {
    default: '#667EEA',
    success: '#10B981',
    warning: '#F59E0B',
    error: '#EF4444',
    gradient: 'url(#gradient)'
  }
  
  return (
    <div className={cn('relative inline-flex items-center justify-center', className)}>
      <svg
        width={size}
        height={size}
        className="transform -rotate-90"
      >
        {variant === 'gradient' && (
          <defs>
            <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" stopColor="#667EEA" />
              <stop offset="100%" stopColor="#FF6B9D" />
            </linearGradient>
          </defs>
        )}
        
        {/* 背景圆环 */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="#E5E7EB"
          strokeWidth={strokeWidth}
          fill="none"
        />
        
        {/* 进度圆环 */}
        <motion.circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke={variantColors[variant]}
          strokeWidth={strokeWidth}
          fill="none"
          strokeLinecap="round"
          strokeDasharray={strokeDasharray}
          initial={animated ? { strokeDashoffset: circumference } : { strokeDashoffset }}
          animate={{ strokeDashoffset }}
          transition={{ 
            duration: animated ? 1 : 0,
            ease: 'easeOut'
          }}
        />
      </svg>
      
      {showLabel && (
        <div className="absolute inset-0 flex flex-col items-center justify-center">
          <span className="text-2xl font-bold text-neutral-800">
            {Math.round(percentage)}%
          </span>
          {label && (
            <span className="text-sm text-neutral-500 mt-1">
              {label}
            </span>
          )}
        </div>
      )}
    </div>
  )
}

// 步骤进度条组件
export interface StepProgressProps {
  steps: Array<{
    label: string
    completed: boolean
    current?: boolean
  }>
  variant?: 'horizontal' | 'vertical'
  className?: string
}

const StepProgress: React.FC<StepProgressProps> = ({
  steps,
  variant = 'horizontal',
  className
}) => {
  const isHorizontal = variant === 'horizontal'
  
  return (
    <div className={cn(
      'flex',
      isHorizontal ? 'items-center space-x-4' : 'flex-col space-y-4',
      className
    )}>
      {steps.map((step, index) => (
        <div
          key={index}
          className={cn(
            'flex items-center',
            isHorizontal ? 'flex-col' : 'flex-row space-x-3'
          )}
        >
          {/* 步骤圆圈 */}
          <div className={cn(
            'flex items-center justify-center w-8 h-8 rounded-full border-2 transition-all duration-200',
            step.completed 
              ? 'bg-primary-500 border-primary-500 text-white'
              : step.current
              ? 'border-primary-500 text-primary-500 bg-primary-50'
              : 'border-neutral-300 text-neutral-400'
          )}>
            {step.completed ? (
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
            ) : (
              <span className="text-sm font-medium">{index + 1}</span>
            )}
          </div>
          
          {/* 步骤标签 */}
          <span className={cn(
            'text-sm font-medium',
            isHorizontal ? 'mt-2 text-center' : '',
            step.completed || step.current ? 'text-neutral-800' : 'text-neutral-400'
          )}>
            {step.label}
          </span>
          
          {/* 连接线 */}
          {index < steps.length - 1 && (
            <div className={cn(
              'bg-neutral-300',
              isHorizontal 
                ? 'h-0.5 w-8 mt-4' 
                : 'w-0.5 h-8 ml-4',
              step.completed ? 'bg-primary-500' : ''
            )} />
          )}
        </div>
      ))}
    </div>
  )
}

export { ProgressBar, CircularProgress, StepProgress }
