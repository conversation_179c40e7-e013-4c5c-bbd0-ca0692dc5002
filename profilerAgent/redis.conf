# Redis 配置文件 - AI Agent专用缓存配置

# 基础配置
bind 0.0.0.0
port 6379
timeout 0
tcp-keepalive 300

# 内存配置 - AI Agent需要更多内存存储对话上下文
maxmemory 1gb
maxmemory-policy allkeys-lru

# 持久化配置
save 900 1
save 300 10
save 60 10000

# 日志配置
loglevel notice
logfile ""

# 数据库分区 - 为AI Agent不同类型的数据分配不同数据库
databases 16
# DB 0: 用户短期记忆缓存 (user_context:user_id)
# DB 1: 对话会话状态 (conversation:conversation_id)
# DB 2: AI模型响应缓存 (ai_cache:hash)
# DB 3: 用户画像缓存 (profile:user_id)
# DB 4: 匹配算法缓存 (matches:user_id)

# 安全配置（开发环境）
# requirepass your_redis_password

# 性能优化
tcp-backlog 511
timeout 0
tcp-keepalive 300

# 慢查询日志
slowlog-log-slower-than 10000
slowlog-max-len 128

# 客户端连接
maxclients 10000

# AOF持久化（可选）
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec

# 压缩配置
rdbcompression yes
rdbchecksum yes

# 内存使用优化
hash-max-ziplist-entries 512
hash-max-ziplist-value 64
list-max-ziplist-size -2
list-compress-depth 0
set-max-intset-entries 512
zset-max-ziplist-entries 128
zset-max-ziplist-value 64
