-- 数据库更新脚本：Session持久化支持
-- 执行这个脚本来添加Session持久化所需的约束和索引

-- 1. 添加唯一约束确保每个call_sid只有一条记录
ALTER TABLE voice_sessions ADD CONSTRAINT unique_twilio_call_sid UNIQUE (twilio_call_sid);

-- 2. 添加索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_voice_sessions_call_sid ON voice_sessions(twilio_call_sid);
CREATE INDEX IF NOT EXISTS idx_voice_sessions_status ON voice_sessions(session_status);
CREATE INDEX IF NOT EXISTS idx_voice_sessions_activity ON voice_sessions(last_activity);
CREATE INDEX IF NOT EXISTS idx_voice_sessions_user_status ON voice_sessions(user_id, session_status);

-- 3. 添加清理过期Session的函数
CREATE OR REPLACE FUNCTION cleanup_expired_sessions()
RETURNS INTEGER AS $$
DECLARE
    expired_count INTEGER;
BEGIN
    -- 标记超过2小时无活动的Session为过期
    UPDATE voice_sessions 
    SET session_status = 'expired'
    WHERE session_status = 'active' 
    AND last_activity < NOW() - INTERVAL '2 hours';
    
    GET DIAGNOSTICS expired_count = ROW_COUNT;
    
    RETURN expired_count;
END;
$$ LANGUAGE plpgsql;

-- 4. 创建定时清理任务（可选）
-- 注意：这需要pg_cron扩展，如果没有可以忽略
-- SELECT cron.schedule('cleanup-expired-sessions', '*/30 * * * *', 'SELECT cleanup_expired_sessions();');

-- 5. 验证表结构
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'voice_sessions' 
ORDER BY ordinal_position;

-- 6. 验证索引
SELECT indexname, indexdef 
FROM pg_indexes 
WHERE tablename = 'voice_sessions';

COMMENT ON COLUMN voice_sessions.session_data IS 'JSON格式的完整Session数据';
COMMENT ON COLUMN voice_sessions.session_status IS 'Session状态: active, completed, expired, error';
COMMENT ON COLUMN voice_sessions.last_activity IS '最后活动时间，用于清理过期Session';
COMMENT ON COLUMN voice_sessions.from_number IS '来电号码，便于查询和调试';
COMMENT ON COLUMN voice_sessions.current_stage IS '当前对话阶段';
COMMENT ON COLUMN voice_sessions.questions_asked IS '已提问数量';
COMMENT ON COLUMN voice_sessions.responses_received IS '已收到回答数量';
