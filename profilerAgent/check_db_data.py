#!/usr/bin/env python3
"""
检查数据库中的语音面试相关数据
"""

import asyncio
import sys
import os

# 添加项目路径
project_root = os.path.dirname(__file__)
sys.path.insert(0, project_root)

from agent import initialize_agent_system, get_agent_interface, cleanup_agent_system

async def check_database_data(user_id: str):
    """检查数据库数据"""
    try:
        print("🔧 Initializing system...")
        await initialize_agent_system()
        agent = get_agent_interface()
        
        print(f"\n=== Checking Database Data for User: {user_id} ===")
        
        # 1. 检查用户信息
        user = await agent.user_manager.get_user(user_id)
        if user:
            print(f"✓ User found: {user.first_name} {user.last_name}")
            print(f"  Phone: {user.phone_number}")
            print(f"  SMS verified: {user.sms_verified}")
            print(f"  Voice completed: {user.voice_call_completed}")
            print(f"  Status: {user.verification_status.value}")
        else:
            print("❌ User not found")
            return
        
        # 2. 直接查询数据库
        from backend.database.connection import get_async_db_session
        from sqlalchemy import text
        
        async with get_async_db_session() as session:
            # 检查 voice_sessions
            print(f"\n--- Voice Sessions ---")
            result = await session.execute(
                text("SELECT id, twilio_call_sid, call_duration, completed_at FROM voice_sessions WHERE user_id = :user_id ORDER BY created_at DESC LIMIT 3"),
                {"user_id": user_id}
            )
            voice_sessions = result.fetchall()
            if voice_sessions:
                for vs in voice_sessions:
                    print(f"✓ Session: {vs[1]} (duration: {vs[2]}s, completed: {vs[3]})")
            else:
                print("❌ No voice sessions found")
            
            # 检查 user_profiles
            print(f"\n--- User Profiles ---")
            result = await session.execute(
                text("SELECT mbti_type, verification_level, last_updated FROM user_profiles WHERE user_id = :user_id"),
                {"user_id": user_id}
            )
            profile = result.fetchone()
            if profile:
                print(f"✓ Profile: MBTI={profile[0]}, Level={profile[1]}, Updated={profile[2]}")
            else:
                print("❌ No user profile found")
            
            # 检查 profile_cards
            print(f"\n--- Profile Cards ---")
            result = await session.execute(
                text("SELECT card_type, title, confidence, created_at FROM profile_cards WHERE user_id = :user_id ORDER BY created_at DESC"),
                {"user_id": user_id}
            )
            cards = result.fetchall()
            if cards:
                for card in cards:
                    print(f"✓ Card: {card[0]} - {card[1]} (confidence: {card[2]}, created: {card[3]})")
            else:
                print("❌ No profile cards found")
        
        await cleanup_agent_system()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    user_id = "afa4d49d-4f12-45ac-9e0c-49dfcd339637"  # 测试用户ID
    asyncio.run(check_database_data(user_id)) 