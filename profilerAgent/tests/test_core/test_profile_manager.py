"""
Tests for ProfileManager
"""

import pytest
import json
import sys
import os

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'agent', 'core'))

from profile_manager import ProfileManager

# 简化的测试数据
SAMPLE_PROFILES = {
    "partial_profile": {
        "basic_info": {
            "name": "张三",
            "age": 28,
            "city": "北京"
        },
        "interests": {
            "hobbies": ["编程", "阅读"]
        }
    }
}

def assert_dict_contains(actual, expected):
    """断言字典包含期望的键值对"""
    for key, value in expected.items():
        assert key in actual
        if isinstance(value, dict) and isinstance(actual[key], dict):
            assert_dict_contains(actual[key], value)
        else:
            assert actual[key] == value

def assert_profile_structure(profile):
    """断言档案结构正确"""
    expected_sections = ["basic_info", "personality", "interests", "relationship", "lifestyle", "values"]
    for section in expected_sections:
        assert section in profile
        assert isinstance(profile[section], dict)

class TestProfileManager:
    """ProfileManager测试类"""
    
    @pytest.mark.asyncio
    async def test_create_profile(self, profile_manager):
        """测试创建档案"""
        manager = profile_manager
        user_id = "test_create_profile"
        
        # 创建空档案
        result = await manager.create_profile(user_id)
        assert result is True
        
        # 验证档案已创建
        profile = await manager.get_profile(user_id)
        assert profile is not None
        assert_profile_structure(profile)
        
        # 验证所有部分都是空字典
        for section in profile.values():
            assert isinstance(section, dict)
    
    @pytest.mark.asyncio
    async def test_create_profile_with_initial_data(self, profile_manager):
        """测试使用初始数据创建档案"""
        manager = profile_manager
        user_id = "test_create_with_data"
        initial_data = SAMPLE_PROFILES["partial_profile"]
        
        # 创建带初始数据的档案
        result = await manager.create_profile(user_id, initial_data)
        assert result is True
        
        # 验证档案内容
        profile = await manager.get_profile(user_id)
        assert profile is not None
        assert_dict_contains(profile, initial_data)
    
    @pytest.mark.asyncio
    async def test_get_nonexistent_profile(self, profile_manager):
        """测试获取不存在的档案"""
        manager = profile_manager
        
        profile = await manager.get_profile("nonexistent_user")
        assert profile is None
    
    @pytest.mark.asyncio
    async def test_update_profile(self, profile_manager):
        """测试更新档案"""
        manager = profile_manager
        user_id = "test_update_profile"
        
        # 先创建档案
        initial_data = SAMPLE_PROFILES["partial_profile"]
        await manager.create_profile(user_id, initial_data)
        
        # 更新档案
        update_data = {
            "basic_info": {
                "occupation": "软件工程师",  # 新增字段
                "education": "本科"  # 新增字段
            },
            "interests": {
                "hobbies": ["编程", "阅读", "爬山"],  # 扩展现有字段
                "music": ["古典", "爵士"]  # 新增字段
            }
        }
        
        result = await manager.update_profile(user_id, update_data)
        assert result is True
        
        # 验证更新结果
        profile = await manager.get_profile(user_id)
        
        # 验证基本信息合并正确
        assert profile["basic_info"]["name"] == "张三"  # 原有数据保留
        assert profile["basic_info"]["age"] == 28  # 原有数据保留
        assert profile["basic_info"]["occupation"] == "软件工程师"  # 新增数据
        assert profile["basic_info"]["education"] == "本科"  # 新增数据
        
        # 验证兴趣合并正确
        assert set(profile["interests"]["hobbies"]) == {"编程", "阅读", "爬山"}
        assert profile["interests"]["music"] == ["古典", "爵士"]
    
    @pytest.mark.asyncio
    async def test_update_nonexistent_profile(self, profile_manager):
        """测试更新不存在的档案（应该创建新档案）"""
        manager = profile_manager
        user_id = "test_update_nonexistent"
        
        update_data = SAMPLE_PROFILES["partial_profile"]
        
        # 更新不存在的档案
        result = await manager.update_profile(user_id, update_data)
        assert result is True
        
        # 验证档案已创建
        profile = await manager.get_profile(user_id)
        assert profile is not None
        assert_dict_contains(profile, update_data)
    
    @pytest.mark.asyncio
    async def test_extract_and_update_from_conversation(self, profile_manager):
        """测试从对话中提取信息并更新档案"""
        manager = profile_manager
        user_id = "test_extract_conversation"
        
        # 先创建空档案
        await manager.create_profile(user_id)
        
        # 模拟对话输入
        user_input = "我叫张三，今年28岁，在北京做软件工程师，喜欢编程和爬山"
        ai_response = "很高兴认识你张三！"
        
        # 从对话中提取并更新
        result = await manager.extract_and_update_from_conversation(
            user_id, user_input, ai_response
        )
        assert result is True
        
        # 验证提取的信息
        profile = await manager.get_profile(user_id)
        basic_info = profile["basic_info"]
        
        # 验证提取的基本信息
        assert basic_info.get("age") == 28
        assert basic_info.get("city") == "北京"
        assert basic_info.get("occupation") == "软件工程师"
        
        # 验证提取的兴趣
        interests = profile["interests"]
        assert "编程" in interests.get("hobbies", [])
        assert "爬山" in interests.get("hobbies", [])
    
    def test_calculate_profile_completeness(self, profile_manager):
        """测试计算档案完整度"""
        manager = profile_manager
        
        # 测试空档案
        empty_profile = SAMPLE_PROFILES["empty_profile"]
        overall, sections = manager.calculate_profile_completeness(empty_profile)
        assert overall == 0.0
        assert all(score == 0.0 for score in sections.values())
        
        # 测试部分完成的档案
        partial_profile = SAMPLE_PROFILES["partial_profile"]
        overall, sections = manager.calculate_profile_completeness(partial_profile)
        assert 0.0 < overall < 1.0
        assert sections["basic_info"] > 0.0  # 有一些基本信息
        assert sections["interests"] > 0.0  # 有一些兴趣
        assert sections["personality"] == 0.0  # 没有性格信息
        
        # 测试完整档案
        complete_profile = SAMPLE_PROFILES["complete_profile"]
        overall, sections = manager.calculate_profile_completeness(complete_profile)
        assert overall == 1.0
        assert all(score == 1.0 for score in sections.values())
    
    def test_get_missing_required_fields(self, profile_manager):
        """测试获取缺失的必须字段"""
        manager = profile_manager
        
        # 测试空档案
        empty_profile = SAMPLE_PROFILES["empty_profile"]
        missing = manager.get_missing_required_fields(empty_profile)
        
        # 应该包含所有必须字段
        expected_missing = ["basic_info.name", "basic_info.age", "basic_info.city", "basic_info.occupation"]
        assert all(field in missing for field in expected_missing)
        
        # 测试部分完成的档案
        partial_profile = SAMPLE_PROFILES["partial_profile"]
        missing = manager.get_missing_required_fields(partial_profile)
        
        # 应该只缺少occupation
        assert "basic_info.occupation" in missing
        assert "basic_info.name" not in missing  # 已有
        assert "basic_info.age" not in missing  # 已有
        assert "basic_info.city" not in missing  # 已有
    
    def test_deep_merge_profiles(self, profile_manager):
        """测试深度合并档案"""
        manager = profile_manager
        
        base_profile = {
            "basic_info": {
                "name": "张三",
                "age": 28
            },
            "interests": {
                "hobbies": ["编程", "阅读"]
            }
        }
        
        new_data = {
            "basic_info": {
                "city": "北京",  # 新字段
                "occupation": "软件工程师"  # 新字段
            },
            "interests": {
                "hobbies": ["爬山"],  # 扩展列表
                "music": ["古典"]  # 新字段
            },
            "personality": {  # 新部分
                "mbti": "INTJ"
            }
        }
        
        merged = manager._deep_merge_profiles(base_profile, new_data)
        
        # 验证基本信息合并
        assert merged["basic_info"]["name"] == "张三"  # 保留原有
        assert merged["basic_info"]["age"] == 28  # 保留原有
        assert merged["basic_info"]["city"] == "北京"  # 新增
        assert merged["basic_info"]["occupation"] == "软件工程师"  # 新增
        
        # 验证兴趣列表合并（去重）
        hobbies = merged["interests"]["hobbies"]
        assert "编程" in hobbies
        assert "阅读" in hobbies
        assert "爬山" in hobbies
        assert len(set(hobbies)) == len(hobbies)  # 无重复
        
        # 验证新部分添加
        assert merged["personality"]["mbti"] == "INTJ"
    
    def test_extract_info_from_text(self, simple_profile_manager):
        """测试从文本提取信息"""
        manager = simple_profile_manager
        
        # 测试包含多种信息的文本
        text = "我叫张三，今年28岁，在北京做软件工程师，喜欢编程、看书和爬山"
        extracted = manager._extract_info_from_text(text)
        
        # 验证基本信息提取
        basic_info = extracted.get("basic_info", {})
        assert basic_info.get("age") == 28
        assert basic_info.get("city") == "北京"
        assert basic_info.get("occupation") == "软件工程师"
        
        # 验证兴趣提取
        interests = extracted.get("interests", {})
        hobbies = interests.get("hobbies", [])
        assert "编程" in hobbies
        assert "阅读" in hobbies
        assert "爬山" in hobbies
    
    def test_calculate_confidence_scores(self, profile_manager):
        """测试计算置信度分数"""
        manager = profile_manager
        
        new_data = {
            "basic_info": {
                "name": "张三",  # 短文本
                "occupation": "软件工程师"  # 中等长度
            },
            "interests": {
                "hobbies": ["编程", "阅读", "爬山"]  # 多个值
            }
        }
        
        scores = manager._calculate_confidence_scores(new_data)
        
        # 验证置信度分数
        assert "basic_info.name" in scores
        assert "basic_info.occupation" in scores
        assert "interests.hobbies" in scores
        
        # 多个值的置信度应该更高
        assert scores["interests.hobbies"] >= scores["basic_info.name"]
    
    @pytest.mark.asyncio
    async def test_redis_caching(self, profile_manager, mock_redis):
        """测试Redis缓存功能"""
        manager = profile_manager
        user_id = "test_redis_cache"
        
        # 创建档案
        profile_data = SAMPLE_PROFILES["partial_profile"]
        await manager.create_profile(user_id, profile_data)
        
        # 验证缓存
        cached_data = await mock_redis.get(f"profile:{user_id}")
        assert cached_data is not None
        
        cached_profile = json.loads(cached_data)
        assert_dict_contains(cached_profile, profile_data)
        
        # 测试从缓存获取
        profile = await manager.get_profile(user_id)
        assert_dict_contains(profile, profile_data)
    
    @pytest.mark.asyncio
    async def test_cache_invalidation(self, profile_manager, mock_redis):
        """测试缓存失效"""
        manager = profile_manager
        user_id = "test_cache_invalidation"
        
        # 创建档案
        await manager.create_profile(user_id, SAMPLE_PROFILES["partial_profile"])
        
        # 验证缓存存在
        cached_data = await mock_redis.get(f"profile:{user_id}")
        assert cached_data is not None
        
        # 更新档案
        update_data = {"basic_info": {"occupation": "产品经理"}}
        await manager.update_profile(user_id, update_data)
        
        # 验证缓存已更新（通过检查新的缓存内容）
        new_cached_data = await mock_redis.get(f"profile:{user_id}")
        assert new_cached_data is not None
        
        new_cached_profile = json.loads(new_cached_data)
        assert new_cached_profile["basic_info"]["occupation"] == "产品经理"
