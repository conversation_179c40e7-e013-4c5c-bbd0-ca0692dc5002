"""
Tests for MemoryManager
"""

import pytest
import json
import sys
import os
from datetime import datetime

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'agent', 'core'))

from memory_manager import MemoryManager, ConversationMessage, ImportantTopic

class TestConversationMessage:
    """ConversationMessage测试类"""
    
    def test_conversation_message_creation(self):
        """测试对话消息创建"""
        message = ConversationMessage(
            role="user",
            content="Hello, this is a test message",
            timestamp=datetime.now(),
            metadata={"test": True}
        )
        
        assert message.role == "user"
        assert message.content == "Hello, this is a test message"
        assert message.metadata["test"] is True
    
    def test_conversation_message_serialization(self):
        """测试对话消息序列化"""
        original_message = ConversationMessage(
            role="assistant",
            content="Hello! How can I help you?",
            timestamp=datetime.now()
        )
        
        # 序列化
        message_dict = original_message.to_dict()
        assert isinstance(message_dict, dict)
        assert message_dict["role"] == "assistant"
        assert message_dict["content"] == "Hello! How can I help you?"
        assert "timestamp" in message_dict
        
        # 反序列化
        restored_message = ConversationMessage.from_dict(message_dict)
        assert restored_message.role == original_message.role
        assert restored_message.content == original_message.content
        assert restored_message.timestamp == original_message.timestamp

class TestImportantTopic:
    """ImportantTopic测试类"""
    
    def test_important_topic_creation(self):
        """测试重要话题创建"""
        topic = ImportantTopic(
            topic="工作",
            context="我是软件工程师",
            last_mentioned=datetime.now(),
            importance_score=1.2,
            related_keywords=["软件工程师", "编程"]
        )
        
        assert topic.topic == "工作"
        assert topic.context == "我是软件工程师"
        assert topic.importance_score == 1.2
        assert "软件工程师" in topic.related_keywords
    
    def test_important_topic_serialization(self):
        """测试重要话题序列化"""
        original_topic = ImportantTopic(
            topic="爱好",
            context="我喜欢编程和爬山",
            last_mentioned=datetime.now(),
            importance_score=1.1
        )
        
        # 序列化
        topic_dict = original_topic.to_dict()
        assert isinstance(topic_dict, dict)
        assert topic_dict["topic"] == "爱好"
        
        # 反序列化
        restored_topic = ImportantTopic.from_dict(topic_dict)
        assert restored_topic.topic == original_topic.topic
        assert restored_topic.context == original_topic.context
        assert restored_topic.importance_score == original_topic.importance_score

class TestMemoryManager:
    """MemoryManager测试类"""
    
    @pytest.mark.asyncio
    async def test_add_message(self, memory_manager):
        """测试添加消息"""
        manager = memory_manager
        user_id = "test_add_message"
        
        # 添加用户消息
        result = await manager.add_message(
            user_id=user_id,
            role="user",
            content="我叫张三，是软件工程师",
            metadata={"test": True}
        )
        
        assert result is True
        
        # 添加助手消息
        result = await manager.add_message(
            user_id=user_id,
            role="assistant",
            content="很高兴认识你张三！"
        )
        
        assert result is True
        
        # 验证短期记忆
        context = await manager.get_conversation_context(user_id)
        messages = context["short_term_memory"]
        
        assert len(messages) == 2
        assert messages[0]["role"] == "user"
        assert messages[0]["content"] == "我叫张三，是软件工程师"
        assert messages[1]["role"] == "assistant"
        assert messages[1]["content"] == "很高兴认识你张三！"
    
    @pytest.mark.asyncio
    async def test_sliding_window_memory(self, memory_manager):
        """测试滑动窗口记忆"""
        manager = memory_manager
        user_id = "test_sliding_window"
        
        # 添加超过限制的消息数量
        for i in range(15):  # 超过默认限制10
            await manager.add_message(
                user_id=user_id,
                role="user" if i % 2 == 0 else "assistant",
                content=f"Message {i}"
            )
        
        # 验证只保留最近的消息
        context = await manager.get_conversation_context(user_id)
        messages = context["short_term_memory"]
        
        assert len(messages) == 10  # 应该只保留10条
        # 验证是最近的消息
        assert messages[-1]["content"] == "Message 14"
        assert messages[0]["content"] == "Message 5"  # 15-10+1 = 6, 但索引从0开始所以是5
    
    @pytest.mark.asyncio
    async def test_important_topic_detection(self, memory_manager, mock_redis):
        """测试重要话题检测"""
        manager = memory_manager
        user_id = "test_topic_detection"
        
        # 添加包含重要话题的消息
        await manager.add_message(
            user_id=user_id,
            role="user",
            content="我在一家科技公司做软件工程师，主要负责后端开发"
        )
        
        # 验证重要话题被检测到
        context = await manager.get_conversation_context(user_id)
        topics = context["important_topics"]
        
        # 应该检测到工作相关话题
        work_topics = [t for t in topics if t["topic"] == "工作"]
        assert len(work_topics) > 0
        
        work_topic = work_topics[0]
        assert "软件工程师" in work_topic["context"]
    
    @pytest.mark.asyncio
    async def test_build_llm_context(self, memory_manager):
        """测试构建LLM上下文"""
        manager = memory_manager
        user_id = "test_llm_context"
        
        # 添加一些对话历史
        conversation = SAMPLE_CONVERSATIONS["basic_info_collection"]
        for msg in conversation:
            await manager.add_message(user_id, msg["role"], msg["content"])
        
        # 构建LLM上下文
        user_profile = SAMPLE_PROFILES["partial_profile"]
        context_messages = await manager.build_llm_context(
            user_id=user_id,
            current_input="你在北京做什么工作？",
            user_profile=user_profile
        )
        
        # 验证上下文结构
        assert len(context_messages) > 0
        assert context_messages[0]["role"] == "system"  # 系统提示
        assert context_messages[-1]["role"] == "user"  # 当前用户输入
        assert context_messages[-1]["content"] == "你在北京做什么工作？"
        
        # 验证系统提示包含用户档案信息
        system_prompt = context_messages[0]["content"]
        assert "张三" in system_prompt  # 用户姓名
        assert "28" in system_prompt  # 用户年龄
    
    @pytest.mark.asyncio
    async def test_conversation_summary_generation(self, memory_manager):
        """测试对话摘要生成"""
        manager = memory_manager
        user_id = "test_summary"
        
        # 添加包含多种话题的对话
        messages = [
            {"role": "user", "content": "我叫张三，在北京工作"},
            {"role": "assistant", "content": "很高兴认识你！"},
            {"role": "user", "content": "我喜欢编程和爬山"},
            {"role": "assistant", "content": "很棒的爱好！"},
            {"role": "user", "content": "我和家人关系很好"},
            {"role": "assistant", "content": "家庭和睦很重要"}
        ]
        
        for msg in messages:
            await manager.add_message(user_id, msg["role"], msg["content"])
        
        # 获取对话上下文
        context = await manager.get_conversation_context(user_id)
        summary = context["conversation_summary"]
        
        # 验证摘要包含关键信息
        assert len(summary) > 0
        assert "工作" in summary or "爱好" in summary or "家" in summary
    
    @pytest.mark.asyncio
    async def test_clear_memory(self, memory_manager, mock_redis):
        """测试清除记忆"""
        manager = memory_manager
        user_id = "test_clear_memory"
        
        # 添加一些消息和话题
        await manager.add_message(user_id, "user", "测试消息")
        
        # 验证记忆存在
        context = await manager.get_conversation_context(user_id)
        assert len(context["short_term_memory"]) > 0
        
        # 清除记忆
        result = await manager.clear_memory(user_id)
        assert result is True
        
        # 验证记忆已清除
        context = await manager.get_conversation_context(user_id)
        assert len(context["short_term_memory"]) == 0
        assert len(context["important_topics"]) == 0
    
    @pytest.mark.asyncio
    async def test_redis_persistence(self, memory_manager, mock_redis):
        """测试Redis持久化"""
        manager = memory_manager
        user_id = "test_redis_persistence"
        
        # 添加消息
        await manager.add_message(user_id, "user", "测试Redis持久化")
        
        # 验证数据已保存到Redis
        memory_data = await mock_redis.get(f"short_memory:{user_id}")
        assert memory_data is not None
        
        messages = json.loads(memory_data)
        assert len(messages) == 1
        assert messages[0]["content"] == "测试Redis持久化"
    
    @pytest.mark.asyncio
    async def test_important_topic_persistence(self, memory_manager, mock_redis):
        """测试重要话题持久化"""
        manager = memory_manager
        user_id = "test_topic_persistence"
        
        # 添加包含重要话题的消息
        await manager.add_message(user_id, "user", "我在公司做项目管理工作")
        
        # 验证重要话题已保存
        topics_data = await mock_redis.get(f"important_topics:{user_id}")
        assert topics_data is not None
        
        topics = json.loads(topics_data)
        work_topics = [t for t in topics if t["topic"] == "工作"]
        assert len(work_topics) > 0
    
    @pytest.mark.asyncio
    async def test_topic_importance_scoring(self, memory_manager):
        """测试话题重要性评分"""
        manager = memory_manager
        user_id = "test_importance_scoring"
        
        # 多次提及同一话题
        await manager.add_message(user_id, "user", "我在公司做软件开发")
        await manager.add_message(user_id, "user", "工作很忙，但我喜欢编程")
        await manager.add_message(user_id, "user", "公司的项目很有挑战性")
        
        # 获取重要话题
        context = await manager.get_conversation_context(user_id)
        topics = context["important_topics"]
        
        # 找到工作相关话题
        work_topics = [t for t in topics if t["topic"] == "工作"]
        assert len(work_topics) > 0
        
        # 验证重要性分数有所提升
        work_topic = work_topics[0]
        assert work_topic["importance_score"] > 1.0  # 应该比初始值1.0更高
    
    def test_system_prompt_building(self, memory_manager):
        """测试系统提示构建"""
        manager = memory_manager
        
        # 测试无档案的系统提示
        prompt = manager._build_system_prompt()
        assert "约会档案助手" in prompt
        assert "自然对话" in prompt
        
        # 测试有档案的系统提示
        user_profile = SAMPLE_PROFILES["partial_profile"]
        prompt_with_profile = manager._build_system_prompt(user_profile)
        
        assert "约会档案助手" in prompt_with_profile
        assert "张三" in prompt_with_profile
        assert "28" in prompt_with_profile
        assert "北京" in prompt_with_profile
    
    def test_profile_summarization(self, memory_manager):
        """测试档案摘要"""
        manager = memory_manager
        
        # 测试完整档案摘要
        complete_profile = SAMPLE_PROFILES["complete_profile"]
        summary = manager._summarize_profile(complete_profile)
        
        assert "姓名：张三" in summary
        assert "年龄：28" in summary
        assert "城市：北京" in summary
        assert "职业：软件工程师" in summary
        assert "兴趣：" in summary
        
        # 测试空档案摘要
        empty_profile = SAMPLE_PROFILES["empty_profile"]
        empty_summary = manager._summarize_profile(empty_profile)
        assert empty_summary == ""
    
    @pytest.mark.asyncio
    async def test_context_limit_handling(self, memory_manager):
        """测试上下文长度限制处理"""
        manager = memory_manager
        user_id = "test_context_limit"
        
        # 添加很多消息
        for i in range(20):
            await manager.add_message(
                user_id=user_id,
                role="user" if i % 2 == 0 else "assistant",
                content=f"这是第{i}条很长的消息，包含很多内容来测试上下文长度限制处理机制"
            )
        
        # 构建LLM上下文
        context_messages = await manager.build_llm_context(
            user_id=user_id,
            current_input="当前输入"
        )
        
        # 验证上下文长度合理（系统提示 + 最近消息 + 当前输入）
        # 应该不超过10条消息（系统提示 + 8条历史 + 当前输入）
        assert len(context_messages) <= 10
        
        # 验证包含最近的消息
        user_messages = [msg for msg in context_messages if msg["role"] == "user"]
        if len(user_messages) > 1:  # 除了当前输入
            # 最后一条用户消息应该是最近的
            assert "第19条" in user_messages[-2]["content"] or "第17条" in user_messages[-2]["content"]
