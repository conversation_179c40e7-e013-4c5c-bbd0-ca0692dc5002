"""
Core tests specific conftest
"""

import pytest
import pytest_asyncio
import asyncio
import sys
import os
from unittest.mock import AsyncMock

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'agent', 'core'))

from database_manager import DatabaseManager
from registration_state import RegistrationStateManager
from profile_manager import ProfileManager
from memory_manager import MemoryManager

@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环"""
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()

@pytest.fixture
async def mock_redis():
    """模拟Redis客户端"""
    redis_mock = AsyncMock()
    
    # 模拟Redis存储
    redis_storage = {}
    
    async def mock_get(key):
        return redis_storage.get(key)
    
    async def mock_set(key, value):
        redis_storage[key] = value
        return True
    
    async def mock_setex(key, ttl, value):
        redis_storage[key] = value
        return True
    
    async def mock_delete(key):
        if key in redis_storage:
            del redis_storage[key]
            return 1
        return 0
    
    redis_mock.get = mock_get
    redis_mock.set = mock_set
    redis_mock.setex = mock_setex
    redis_mock.delete = mock_delete
    
    # 添加存储访问方法（用于测试验证）
    redis_mock._storage = redis_storage
    
    return redis_mock

@pytest_asyncio.fixture
async def mock_db_manager():
    """模拟数据库管理器"""
    db_mock = DatabaseManager()
    # 强制使用内存存储，不尝试连接真实数据库
    db_mock.is_connected = False
    # 不调用connect()，直接使用内存存储
    return db_mock

@pytest.fixture
def simple_profile_manager():
    """简单的档案管理器fixture"""
    manager = ProfileManager(db_manager=None, redis_client=None)
    return manager
