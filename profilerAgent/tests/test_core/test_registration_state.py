"""
Tests for RegistrationState
"""

import pytest
import json
import sys
import os
from datetime import datetime

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'agent', 'core'))

from registration_state import (
    RegistrationStateManager, RegistrationStatus, RegistrationProgress,
    RequiredInfo, OptionalInfo
)

class TestRegistrationProgress:
    """RegistrationProgress测试类"""
    
    def test_registration_progress_initialization(self):
        """测试注册进度初始化"""
        progress = RegistrationProgress(
            user_id="test_user",
            phone_number="+1234567890",
            status=RegistrationStatus.PHONE_VERIFICATION_PENDING,
            required_info={},
            optional_info={},
            conversation_context={},
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        # 验证自动初始化
        assert len(progress.required_info) == 4  # name, age, city, occupation
        assert len(progress.optional_info) == 5  # interests, personality, etc.
        assert progress.call_history == []
        assert "current_topic" in progress.conversation_context
    
    def test_calculate_completion_rates(self):
        """测试完成率计算"""
        progress = RegistrationProgress(
            user_id="test_user",
            phone_number="+1234567890",
            status=RegistrationStatus.VOICE_REGISTRATION_INCOMPLETE,
            required_info={},
            optional_info={},
            conversation_context={},
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        # 设置部分必须信息已收集
        progress.required_info["name"].collected = True
        progress.required_info["age"].collected = True
        # city和occupation未收集
        
        # 设置部分可选信息已收集
        progress.optional_info["interests"].collected = True
        progress.optional_info["personality"].collected = True
        progress.optional_info["lifestyle"].collected = True
        # relationship_goals和values未收集
        
        required_complete, optional_rate = progress.calculate_completion_rates()
        
        assert required_complete is False  # 必须信息未完成
        assert optional_rate == 0.6  # 3/5 = 0.6
    
    def test_is_registration_complete(self):
        """测试注册完成判断"""
        progress = RegistrationProgress(
            user_id="test_user",
            phone_number="+1234567890",
            status=RegistrationStatus.VOICE_REGISTRATION_INCOMPLETE,
            required_info={},
            optional_info={},
            conversation_context={},
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        # 未完成状态
        assert progress.is_registration_complete() is False
        
        # 完成所有必须信息
        for field in progress.required_info.values():
            field.collected = True
        
        # 完成75%可选信息
        optional_fields = list(progress.optional_info.values())
        for i in range(4):  # 4/5 = 80% > 75%
            optional_fields[i].collected = True
        
        assert progress.is_registration_complete() is True
    
    def test_get_next_required_info(self):
        """测试获取下一个必须信息"""
        progress = RegistrationProgress(
            user_id="test_user",
            phone_number="+1234567890",
            status=RegistrationStatus.VOICE_REGISTRATION_INCOMPLETE,
            required_info={},
            optional_info={},
            conversation_context={},
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        # 初始状态，应该返回第一个未收集的字段
        next_field = progress.get_next_required_info()
        assert next_field in ["name", "age", "city", "occupation"]
        
        # 收集name后
        progress.required_info["name"].collected = True
        next_field = progress.get_next_required_info()
        assert next_field != "name"
        assert next_field in ["age", "city", "occupation"]
        
        # 收集所有必须信息后
        for field in progress.required_info.values():
            field.collected = True
        
        next_field = progress.get_next_required_info()
        assert next_field is None
    
    def test_update_required_info(self):
        """测试更新必须信息"""
        progress = RegistrationProgress(
            user_id="test_user",
            phone_number="+1234567890",
            status=RegistrationStatus.VOICE_REGISTRATION_INCOMPLETE,
            required_info={},
            optional_info={},
            conversation_context={},
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        # 更新姓名信息
        progress.update_required_info("name", "张三", "你叫什么名字？")
        
        name_info = progress.required_info["name"]
        assert name_info.collected is True
        assert name_info.value == "张三"
        assert name_info.last_question == "你叫什么名字？"
        assert name_info.last_attempt_time is not None
    
    def test_increment_attempts(self):
        """测试增加尝试次数"""
        progress = RegistrationProgress(
            user_id="test_user",
            phone_number="+1234567890",
            status=RegistrationStatus.VOICE_REGISTRATION_INCOMPLETE,
            required_info={},
            optional_info={},
            conversation_context={},
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        # 增加尝试次数
        progress.increment_attempts("age", "你今年多大？")
        progress.increment_attempts("age", "你的年龄是？")
        
        age_info = progress.required_info["age"]
        assert age_info.attempts == 2
        assert age_info.last_question == "你的年龄是？"
        assert age_info.collected is False
    
    def test_add_call_record(self):
        """测试添加通话记录"""
        progress = RegistrationProgress(
            user_id="test_user",
            phone_number="+1234567890",
            status=RegistrationStatus.VOICE_REGISTRATION_INCOMPLETE,
            required_info={},
            optional_info={},
            conversation_context={},
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        # 添加通话记录
        progress.add_call_record(300, "user_hangup", "收集了基本信息")
        
        assert len(progress.call_history) == 1
        record = progress.call_history[0]
        assert record["duration"] == 300
        assert record["reason"] == "user_hangup"
        assert record["progress"] == "收集了基本信息"
        assert "timestamp" in record
    
    def test_to_dict_and_from_dict(self):
        """测试序列化和反序列化"""
        original_progress = RegistrationProgress(
            user_id="test_user",
            phone_number="+1234567890",
            status=RegistrationStatus.VOICE_REGISTRATION_INCOMPLETE,
            required_info={},
            optional_info={},
            conversation_context={},
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        # 设置一些数据
        original_progress.update_required_info("name", "张三")
        original_progress.add_call_record(300, "completed", "测试完成")
        
        # 序列化
        data_dict = original_progress.to_dict()
        assert isinstance(data_dict, dict)
        assert data_dict["status"] == RegistrationStatus.VOICE_REGISTRATION_INCOMPLETE.value
        
        # 反序列化
        restored_progress = RegistrationProgress.from_dict(data_dict)
        assert restored_progress.user_id == original_progress.user_id
        assert restored_progress.status == original_progress.status
        assert restored_progress.required_info["name"].collected is True
        assert restored_progress.required_info["name"].value == "张三"
        assert len(restored_progress.call_history) == 1

class TestRegistrationStateManager:
    """RegistrationStateManager测试类"""
    
    @pytest.mark.asyncio
    async def test_create_registration(self, registration_state_manager):
        """测试创建注册"""
        manager = registration_state_manager
        
        progress = await manager.create_registration("test_user", "+1234567890")
        
        assert progress is not None
        assert progress.user_id == "test_user"
        assert progress.phone_number == "+1234567890"
        assert progress.status == RegistrationStatus.PHONE_VERIFICATION_PENDING
        assert len(progress.required_info) == 4
        assert len(progress.optional_info) == 5
    
    @pytest.mark.asyncio
    async def test_get_registration_state(self, registration_state_manager, mock_redis):
        """测试获取注册状态"""
        manager = registration_state_manager
        
        # 先创建注册
        original_progress = await manager.create_registration("test_user", "+1234567890")
        
        # 获取注册状态
        retrieved_progress = await manager.get_registration_state("test_user")
        
        assert retrieved_progress is not None
        assert retrieved_progress.user_id == original_progress.user_id
        assert retrieved_progress.status == original_progress.status
    
    @pytest.mark.asyncio
    async def test_get_nonexistent_registration(self, registration_state_manager):
        """测试获取不存在的注册状态"""
        manager = registration_state_manager
        
        progress = await manager.get_registration_state("nonexistent_user")
        assert progress is None
    
    @pytest.mark.asyncio
    async def test_update_status(self, registration_state_manager):
        """测试更新注册状态"""
        manager = registration_state_manager
        
        # 创建注册
        await manager.create_registration("test_user", "+1234567890")
        
        # 更新状态
        result = await manager.update_status("test_user", RegistrationStatus.PHONE_VERIFIED)
        assert result is True
        
        # 验证状态已更新
        progress = await manager.get_registration_state("test_user")
        assert progress.status == RegistrationStatus.PHONE_VERIFIED
        assert progress.phone_verified_at is not None
    
    @pytest.mark.asyncio
    async def test_update_status_nonexistent_user(self, registration_state_manager):
        """测试更新不存在用户的状态"""
        manager = registration_state_manager
        
        result = await manager.update_status("nonexistent_user", RegistrationStatus.PHONE_VERIFIED)
        assert result is False
    
    @pytest.mark.asyncio
    async def test_is_registration_complete(self, registration_state_manager):
        """测试检查注册是否完成"""
        manager = registration_state_manager
        
        # 创建注册
        await manager.create_registration("test_user", "+1234567890")
        
        # 初始状态应该未完成
        is_complete = await manager.is_registration_complete("test_user")
        assert is_complete is False
        
        # 更新为已注册状态
        await manager.update_status("test_user", RegistrationStatus.REGISTERED)
        
        # 现在应该完成了
        is_complete = await manager.is_registration_complete("test_user")
        assert is_complete is True
    
    @pytest.mark.asyncio
    async def test_redis_caching(self, registration_state_manager, mock_redis):
        """测试Redis缓存功能"""
        manager = registration_state_manager
        
        # 创建注册
        progress = await manager.create_registration("test_user", "+1234567890")
        
        # 验证数据已缓存到Redis
        cached_data = await mock_redis.get("registration_state:test_user")
        assert cached_data is not None
        
        # 验证缓存的数据正确
        cached_progress = json.loads(cached_data)
        assert cached_progress["user_id"] == "test_user"
        assert cached_progress["status"] == RegistrationStatus.PHONE_VERIFICATION_PENDING.value
    
    @pytest.mark.asyncio
    async def test_status_timestamps(self, registration_state_manager):
        """测试状态时间戳"""
        manager = registration_state_manager
        
        # 创建注册
        await manager.create_registration("test_user", "+1234567890")
        
        # 更新为手机已验证
        await manager.update_status("test_user", RegistrationStatus.PHONE_VERIFIED)
        progress = await manager.get_registration_state("test_user")
        assert progress.phone_verified_at is not None
        
        # 更新为语音注册中
        await manager.update_status("test_user", RegistrationStatus.VOICE_REGISTRATION_INCOMPLETE)
        progress = await manager.get_registration_state("test_user")
        assert progress.voice_started_at is not None
        
        # 更新为已注册
        await manager.update_status("test_user", RegistrationStatus.REGISTERED)
        progress = await manager.get_registration_state("test_user")
        assert progress.completed_at is not None
