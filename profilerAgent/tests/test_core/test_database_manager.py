"""
Tests for DatabaseManager
"""

import pytest
import json
import sys
import os
from datetime import datetime

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'agent', 'core'))

from database_manager import DatabaseManager

# 测试数据
SAMPLE_USERS = {
    "new_user": {
        "user_id": "new_user_001",
        "phone_number": "+1234567890",
        "name": None,
        "status": "pending"
    }
}

SAMPLE_PROFILES = {
    "partial_profile": {
        "basic_info": {
            "name": "张三",
            "age": 28,
            "city": "北京"
        },
        "interests": {
            "hobbies": ["编程", "阅读"]
        }
    }
}

class TestDatabaseManager:
    """DatabaseManager测试类"""
    
    @pytest.mark.asyncio
    async def test_database_connection(self):
        """测试数据库连接"""
        db = DatabaseManager()
        
        # 测试连接（应该失败并使用内存存储）
        result = await db.connect()
        
        # 在测试环境中，数据库连接可能失败，但应该有fallback
        assert db.is_connected is False  # 测试环境使用内存存储
        assert db._memory_store is not None
    
    @pytest.mark.asyncio
    async def test_create_user(self, mock_db_manager):
        """测试创建用户"""
        db = mock_db_manager
        user_data = SAMPLE_USERS["new_user"]
        
        # 创建用户
        result = await db.create_user(
            user_id=user_data["user_id"],
            phone_number=user_data["phone_number"],
            name=user_data["name"]
        )
        
        assert result is True
        
        # 验证用户已创建
        user = await db.get_user(user_data["user_id"])
        assert user is not None
        assert user["user_id"] == user_data["user_id"]
        assert user["phone_number"] == user_data["phone_number"]
        assert user["status"] == "pending"
    
    @pytest.mark.asyncio
    async def test_update_user_status(self, mock_db_manager):
        """测试更新用户状态"""
        db = mock_db_manager
        user_data = SAMPLE_USERS["new_user"]
        
        # 先创建用户
        await db.create_user(
            user_id=user_data["user_id"],
            phone_number=user_data["phone_number"],
            name=user_data["name"]
        )
        
        # 更新状态
        result = await db.update_user_status(user_data["user_id"], "active")
        assert result is True
        
        # 验证状态已更新
        user = await db.get_user(user_data["user_id"])
        assert user["status"] == "active"
    
    @pytest.mark.asyncio
    async def test_get_nonexistent_user(self, mock_db_manager):
        """测试获取不存在的用户"""
        db = mock_db_manager
        
        user = await db.get_user("nonexistent_user")
        assert user is None
    
    @pytest.mark.asyncio
    async def test_create_user_profile(self, mock_db_manager):
        """测试创建用户档案"""
        db = mock_db_manager
        user_id = "test_user_profile"
        profile_data = SAMPLE_PROFILES["partial_profile"]
        
        # 创建档案
        result = await db.create_user_profile(user_id, profile_data)
        assert result is True
        
        # 验证档案已创建
        profile = await db.get_user_profile(user_id)
        assert profile is not None
        assert profile["user_id"] == user_id
        assert profile["profile_data"] == profile_data
        assert profile["conversation_count"] == 0
    
    @pytest.mark.asyncio
    async def test_update_user_profile(self, mock_db_manager):
        """测试更新用户档案"""
        db = mock_db_manager
        user_id = "test_user_profile_update"

        # 先创建用户（满足外键约束）
        await db.create_user(user_id, "+1234567890", "Test User")

        # 再创建档案
        initial_profile = SAMPLE_PROFILES["partial_profile"]
        await db.create_user_profile(user_id, initial_profile)
        
        # 更新档案
        updated_data = {
            "basic_info": {
                "name": "张三",
                "age": 28,
                "city": "北京",
                "occupation": "软件工程师"  # 新增字段
            },
            "interests": {
                "hobbies": ["编程", "阅读", "爬山"]  # 更新字段
            }
        }
        
        confidence_scores = {
            "basic_info.occupation": 0.9,
            "interests.hobbies": 0.85
        }
        
        result = await db.update_user_profile(user_id, updated_data, confidence_scores)
        assert result is True
        
        # 验证档案已更新
        profile = await db.get_user_profile(user_id)
        assert profile["profile_data"] == updated_data
        assert profile["confidence_scores"] == confidence_scores
        assert profile["conversation_count"] == 1  # 应该增加1
    
    @pytest.mark.asyncio
    async def test_get_nonexistent_profile(self, mock_db_manager):
        """测试获取不存在的档案"""
        db = mock_db_manager
        
        profile = await db.get_user_profile("nonexistent_user")
        assert profile is None
    
    @pytest.mark.asyncio
    async def test_create_conversation(self, mock_db_manager):
        """测试创建对话"""
        db = mock_db_manager
        user_id = "test_conversation_user"
        conversation_type = "voice"

        # 先创建用户（满足外键约束）
        await db.create_user(user_id, "+1234567890", "Test User")

        # 创建对话
        conversation_id = await db.create_conversation(user_id, conversation_type)
        
        assert conversation_id is not None
        assert isinstance(conversation_id, str)
        assert len(conversation_id) > 0
    
    @pytest.mark.asyncio
    async def test_add_message(self, mock_db_manager):
        """测试添加消息"""
        db = mock_db_manager
        user_id = "test_message_user"
        
        # 先创建对话
        conversation_id = await db.create_conversation(user_id, "chat")
        assert conversation_id is not None
        
        # 添加消息
        result = await db.add_message(
            conversation_id=conversation_id,
            role="user",
            content="Hello, this is a test message",
            metadata={"test": True}
        )
        
        assert result is True
        
        # 获取消息验证
        messages = await db.get_recent_messages(conversation_id, limit=1)
        assert len(messages) == 1
        assert messages[0]["role"] == "user"
        assert messages[0]["content"] == "Hello, this is a test message"
        assert messages[0]["metadata"]["test"] is True
    
    @pytest.mark.asyncio
    async def test_get_recent_messages(self, mock_db_manager):
        """测试获取最近消息"""
        db = mock_db_manager
        user_id = "test_recent_messages_user"

        # 先创建用户（满足外键约束）
        await db.create_user(user_id, "+1234567890", "Test User")

        # 创建对话
        conversation_id = await db.create_conversation(user_id, "chat")
        
        # 添加多条消息
        messages_to_add = [
            {"role": "assistant", "content": "Hello!"},
            {"role": "user", "content": "Hi there!"},
            {"role": "assistant", "content": "How are you?"},
            {"role": "user", "content": "I'm good, thanks!"},
            {"role": "assistant", "content": "Great to hear!"}
        ]
        
        for msg in messages_to_add:
            await db.add_message(conversation_id, msg["role"], msg["content"])
        
        # 获取最近3条消息
        recent_messages = await db.get_recent_messages(conversation_id, limit=3)
        
        assert len(recent_messages) == 3
        # 应该是最后3条消息，按时间顺序
        assert recent_messages[0]["content"] == "How are you?"
        assert recent_messages[1]["content"] == "I'm good, thanks!"
        assert recent_messages[2]["content"] == "Great to hear!"
    
    @pytest.mark.asyncio
    async def test_get_messages_empty_conversation(self, mock_db_manager):
        """测试获取空对话的消息"""
        db = mock_db_manager
        
        # 获取不存在的对话的消息
        messages = await db.get_recent_messages("nonexistent_conversation", limit=10)
        assert messages == []
    
    @pytest.mark.asyncio
    async def test_memory_store_fallback(self):
        """测试内存存储fallback功能"""
        db = DatabaseManager()
        db.is_connected = False  # 强制使用内存存储
        
        user_id = "memory_test_user"
        
        # 测试用户操作
        await db.create_user(user_id, "+1234567890", "Test User")
        user = await db.get_user(user_id)
        assert user is not None
        assert user["user_id"] == user_id
        
        # 测试档案操作
        profile_data = {"basic_info": {"name": "Test"}}
        await db.create_user_profile(user_id, profile_data)
        profile = await db.get_user_profile(user_id)
        assert profile is not None
        assert profile["profile_data"] == profile_data
        
        # 测试对话操作
        conversation_id = await db.create_conversation(user_id, "test")
        assert conversation_id is not None
        
        await db.add_message(conversation_id, "user", "test message")
        messages = await db.get_recent_messages(conversation_id)
        assert len(messages) == 1
        assert messages[0]["content"] == "test message"
