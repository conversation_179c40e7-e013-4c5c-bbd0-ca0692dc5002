"""
Pytest configuration and shared fixtures
"""

import pytest
import asyncio
import json
from unittest.mock import AsyncMock, MagicMock
from datetime import datetime
from typing import Dict, Any

# 添加项目路径
import sys
import os

# 设置路径，直接导入core模块
project_root = os.path.join(os.path.dirname(__file__), '..')
sys.path.insert(0, project_root)

# 直接从文件导入，避免agent包的复杂导入链
sys.path.insert(0, os.path.join(project_root, 'agent', 'core'))

from database_manager import DatabaseManager
from registration_state import RegistrationStateManager, RegistrationStatus
from profile_manager import ProfileManager
from memory_manager import MemoryManager

@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环"""
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()

@pytest.fixture
async def mock_redis():
    """模拟Redis客户端"""
    redis_mock = AsyncMock()
    
    # 模拟Redis存储
    redis_storage = {}
    
    async def mock_get(key):
        return redis_storage.get(key)
    
    async def mock_set(key, value):
        redis_storage[key] = value
        return True
    
    async def mock_setex(key, ttl, value):
        redis_storage[key] = value
        return True
    
    async def mock_delete(key):
        if key in redis_storage:
            del redis_storage[key]
            return 1
        return 0
    
    async def mock_incr(key):
        current = int(redis_storage.get(key, 0))
        redis_storage[key] = str(current + 1)
        return current + 1
    
    async def mock_expire(key, ttl):
        return True
    
    redis_mock.get = mock_get
    redis_mock.set = mock_set
    redis_mock.setex = mock_setex
    redis_mock.delete = mock_delete
    redis_mock.incr = mock_incr
    redis_mock.expire = mock_expire
    
    # 添加存储访问方法（用于测试验证）
    redis_mock._storage = redis_storage
    
    return redis_mock

@pytest.fixture
async def mock_db_manager():
    """模拟数据库管理器"""
    db_mock = DatabaseManager()
    db_mock.is_connected = False  # 使用内存存储
    await db_mock.connect()
    return db_mock

@pytest.fixture
async def registration_state_manager(mock_redis, mock_db_manager):
    """注册状态管理器fixture"""
    manager = RegistrationStateManager(redis_client=mock_redis, db_manager=mock_db_manager)
    return manager

@pytest.fixture
async def profile_manager(mock_redis, mock_db_manager):
    """档案管理器fixture"""
    manager = ProfileManager(db_manager=mock_db_manager, redis_client=mock_redis)
    return manager

@pytest.fixture
async def memory_manager(mock_redis, mock_db_manager):
    """记忆管理器fixture"""
    manager = MemoryManager(redis_client=mock_redis, db_manager=mock_db_manager)
    return manager

@pytest.fixture
def sample_user_data():
    """示例用户数据"""
    return {
        "user_id": "test_user_123",
        "phone_number": "+1234567890",
        "name": "张三",
        "basic_info": {
            "name": "张三",
            "age": 28,
            "city": "北京",
            "occupation": "软件工程师"
        },
        "interests": {
            "hobbies": ["编程", "阅读", "爬山"],
            "music": ["古典", "爵士"],
            "sports": ["羽毛球", "游泳"]
        },
        "personality": {
            "mbti": "INTJ",
            "traits": ["内向", "理性", "计划性强"],
            "social_energy": "introvert"
        },
        "relationship": {
            "looking_for": "长期关系",
            "preferences": ["有共同兴趣", "性格互补"],
            "deal_breakers": ["吸烟", "不爱运动"]
        }
    }

@pytest.fixture
def sample_conversation_messages():
    """示例对话消息"""
    return [
        {"role": "assistant", "content": "你好！我是你的约会档案助手。你叫什么名字？"},
        {"role": "user", "content": "我叫张三"},
        {"role": "assistant", "content": "很高兴认识你张三！你今年多大了？"},
        {"role": "user", "content": "28岁"},
        {"role": "assistant", "content": "你现在在哪个城市生活？"},
        {"role": "user", "content": "北京"},
        {"role": "assistant", "content": "你在北京做什么工作？"},
        {"role": "user", "content": "我是软件工程师，主要做后端开发"},
        {"role": "assistant", "content": "软件工程师很有挑战性！你平时有什么兴趣爱好？"},
        {"role": "user", "content": "我喜欢编程、看书，周末经常去爬山"}
    ]

@pytest.fixture
def sample_registration_progress():
    """示例注册进度数据"""
    return {
        "user_id": "test_user_123",
        "phone_number": "+1234567890",
        "status": RegistrationStatus.VOICE_REGISTRATION_INCOMPLETE,
        "required_info": {
            "name": {"collected": True, "value": "张三", "attempts": 1},
            "age": {"collected": True, "value": "28", "attempts": 1},
            "city": {"collected": True, "value": "北京", "attempts": 1},
            "occupation": {"collected": False, "attempts": 2, "last_question": "你做什么工作？"}
        },
        "optional_info": {
            "interests": {"collected": True, "completion_rate": 0.8},
            "personality": {"collected": False, "completion_rate": 0.0},
            "relationship_goals": {"collected": False, "completion_rate": 0.0},
            "lifestyle": {"collected": False, "completion_rate": 0.0},
            "values": {"collected": False, "completion_rate": 0.0}
        },
        "conversation_context": {
            "current_topic": "职业信息",
            "resistant_topics": [],
            "successful_topics": ["基本信息", "兴趣爱好"],
            "last_question": "你做什么工作？"
        },
        "created_at": datetime.now(),
        "updated_at": datetime.now()
    }

# 测试工具函数
def assert_dict_contains(actual: Dict[str, Any], expected: Dict[str, Any]):
    """断言字典包含期望的键值对"""
    for key, value in expected.items():
        assert key in actual, f"Key '{key}' not found in actual dict"
        if isinstance(value, dict) and isinstance(actual[key], dict):
            assert_dict_contains(actual[key], value)
        else:
            assert actual[key] == value, f"Value mismatch for key '{key}': expected {value}, got {actual[key]}"

def assert_profile_structure(profile: Dict[str, Any]):
    """断言档案结构正确"""
    expected_sections = ["basic_info", "personality", "interests", "relationship", "lifestyle", "values"]
    for section in expected_sections:
        assert section in profile, f"Profile section '{section}' missing"
        assert isinstance(profile[section], dict), f"Profile section '{section}' should be a dict"
