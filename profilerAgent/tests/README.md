# AI Agent 测试文档

## 📁 测试结构

```
tests/
├── __init__.py
├── conftest.py                    # pytest配置和共享fixtures
├── test_core/                     # Core组件测试
│   ├── __init__.py
│   ├── test_database_manager.py   # 数据库管理器测试
│   ├── test_registration_state.py # 注册状态管理测试
│   ├── test_profile_manager.py    # 档案管理器测试
│   └── test_memory_manager.py     # 记忆管理器测试
├── test_services/                 # Services组件测试
│   └── __init__.py
├── fixtures/                      # 测试数据和工具
│   ├── __init__.py
│   ├── sample_data.py            # 示例测试数据
│   └── mock_responses.py         # 模拟响应（待添加）
└── README.md                     # 本文档
```

## 🚀 运行测试

### 基本用法

```bash
# 运行所有测试
python run_tests.py

# 运行特定测试文件
python run_tests.py tests/test_core/test_database_manager.py

# 运行特定测试类
python run_tests.py tests/test_core/test_database_manager.py::TestDatabaseManager

# 运行特定测试方法
python run_tests.py tests/test_core/test_database_manager.py::TestDatabaseManager::test_create_user
```

### 高级选项

```bash
# 详细输出
python run_tests.py -v

# 生成覆盖率报告
python run_tests.py -c

# 只运行core组件测试
python run_tests.py --core

# 只运行services组件测试
python run_tests.py --services
```

### 直接使用pytest

```bash
# 运行所有测试
pytest tests/

# 运行core测试
pytest tests/test_core/

# 详细输出 + 覆盖率
pytest tests/ -v --cov=agent --cov-report=html
```

## 🧪 测试组件说明

### 1. DatabaseManager 测试

**文件**: `test_core/test_database_manager.py`

**测试内容**:
- ✅ 数据库连接和fallback机制
- ✅ 用户CRUD操作
- ✅ 用户档案CRUD操作  
- ✅ 对话和消息管理
- ✅ 内存存储备用功能

**关键测试用例**:
```python
test_database_connection()          # 数据库连接测试
test_create_user()                  # 创建用户
test_update_user_status()           # 更新用户状态
test_create_user_profile()          # 创建用户档案
test_update_user_profile()          # 更新用户档案
test_create_conversation()          # 创建对话
test_add_message()                  # 添加消息
test_get_recent_messages()          # 获取最近消息
test_memory_store_fallback()        # 内存存储fallback
```

### 2. RegistrationState 测试

**文件**: `test_core/test_registration_state.py`

**测试内容**:
- ✅ 注册进度数据结构
- ✅ 完成率计算
- ✅ 必须信息收集跟踪
- ✅ 状态管理和更新
- ✅ 序列化/反序列化
- ✅ Redis缓存功能

**关键测试用例**:
```python
test_registration_progress_initialization()  # 注册进度初始化
test_calculate_completion_rates()            # 完成率计算
test_is_registration_complete()              # 注册完成判断
test_get_next_required_info()                # 获取下一个必须信息
test_update_required_info()                  # 更新必须信息
test_increment_attempts()                    # 增加尝试次数
test_to_dict_and_from_dict()                # 序列化测试
test_create_registration()                   # 创建注册
test_update_status()                         # 更新状态
test_redis_caching()                         # Redis缓存
```

### 3. ProfileManager 测试

**文件**: `test_core/test_profile_manager.py`

**测试内容**:
- ✅ 档案创建和更新
- ✅ 智能档案合并
- ✅ 完整度计算
- ✅ 信息提取
- ✅ 置信度计算
- ✅ Redis缓存管理

**关键测试用例**:
```python
test_create_profile()                        # 创建档案
test_update_profile()                        # 更新档案
test_extract_and_update_from_conversation()  # 从对话提取信息
test_calculate_profile_completeness()        # 计算完整度
test_get_missing_required_fields()           # 获取缺失字段
test_deep_merge_profiles()                   # 深度合并档案
test_extract_info_from_text()                # 文本信息提取
test_calculate_confidence_scores()           # 置信度计算
test_redis_caching()                         # Redis缓存
test_cache_invalidation()                    # 缓存失效
```

### 4. MemoryManager 测试

**文件**: `test_core/test_memory_manager.py`

**测试内容**:
- ✅ 短期记忆管理
- ✅ 滑动窗口机制
- ✅ 重要话题检测
- ✅ LLM上下文构建
- ✅ 对话摘要生成
- ✅ Redis持久化

**关键测试用例**:
```python
test_add_message()                           # 添加消息
test_sliding_window_memory()                 # 滑动窗口记忆
test_important_topic_detection()             # 重要话题检测
test_build_llm_context()                     # 构建LLM上下文
test_conversation_summary_generation()       # 对话摘要生成
test_clear_memory()                          # 清除记忆
test_redis_persistence()                     # Redis持久化
test_important_topic_persistence()           # 重要话题持久化
test_topic_importance_scoring()              # 话题重要性评分
test_system_prompt_building()                # 系统提示构建
test_context_limit_handling()                # 上下文长度限制
```

## 🔧 测试工具和Fixtures

### 共享Fixtures (conftest.py)

- `mock_redis`: 模拟Redis客户端
- `mock_db_manager`: 模拟数据库管理器
- `registration_state_manager`: 注册状态管理器
- `profile_manager`: 档案管理器
- `memory_manager`: 记忆管理器
- `sample_user_data`: 示例用户数据
- `sample_conversation_messages`: 示例对话消息
- `sample_registration_progress`: 示例注册进度

### 测试数据 (fixtures/sample_data.py)

- `SAMPLE_USERS`: 用户测试数据
- `SAMPLE_PROFILES`: 档案测试数据
- `SAMPLE_REGISTRATION_PROGRESS`: 注册进度测试数据
- `SAMPLE_CONVERSATIONS`: 对话测试数据
- `SAMPLE_IMPORTANT_TOPICS`: 重要话题测试数据
- `SAMPLE_CONFIDENCE_SCORES`: 置信度分数测试数据

### 测试工具函数

- `assert_dict_contains()`: 断言字典包含期望键值对
- `assert_profile_structure()`: 断言档案结构正确

## 📊 测试覆盖率

运行覆盖率测试：
```bash
python run_tests.py -c
```

覆盖率报告将生成在 `htmlcov/` 目录中，打开 `htmlcov/index.html` 查看详细报告。

## 🐛 调试测试

### 运行单个测试并查看详细输出
```bash
python run_tests.py tests/test_core/test_database_manager.py::TestDatabaseManager::test_create_user -v
```

### 在测试中添加断点
```python
import pytest

def test_something():
    # 你的测试代码
    pytest.set_trace()  # 设置断点
    # 更多测试代码
```

### 查看测试输出
```bash
pytest tests/ -v -s  # -s 显示print输出
```

## 📝 编写新测试

### 测试命名规范
- 测试文件: `test_*.py`
- 测试类: `Test*`
- 测试方法: `test_*`

### 异步测试
```python
@pytest.mark.asyncio
async def test_async_function():
    result = await some_async_function()
    assert result is not None
```

### 使用fixtures
```python
def test_with_fixture(mock_redis, sample_user_data):
    # 使用fixtures进行测试
    pass
```

### 参数化测试
```python
@pytest.mark.parametrize("input,expected", [
    ("test1", "result1"),
    ("test2", "result2"),
])
def test_parametrized(input, expected):
    assert process(input) == expected
```

## 🎯 测试最佳实践

1. **独立性**: 每个测试应该独立，不依赖其他测试的结果
2. **清晰性**: 测试名称应该清楚描述测试的内容
3. **完整性**: 测试应该覆盖正常情况、边界情况和异常情况
4. **快速性**: 测试应该快速运行，避免不必要的延迟
5. **可维护性**: 测试代码应该易于理解和维护

## 🔄 持续集成

这些测试设计为可以在CI/CD环境中运行：

```bash
# CI环境中的测试命令
python run_tests.py --core -v -c
```

确保所有测试在提交代码前都能通过。
