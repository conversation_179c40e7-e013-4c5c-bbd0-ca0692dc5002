"""
Simple tests for RegistrationAgent - avoiding complex imports
"""

import pytest
import sys
import os
from unittest.mock import AsyncMock, MagicMock

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'agent', 'services'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'agent', 'core'))

# 直接导入需要的模块
from registration_agent import RegistrationAgent, RegistrationResponse

class MockAIService:
    """简单的AI服务Mock"""
    
    async def extract_structured_data(self, prompt):
        # 简单的信息提取mock
        if "张三" in prompt:
            return {"name": "张三"}
        elif "28" in prompt:
            return {"age": 28}
        elif "北京" in prompt:
            return {"city": "北京"}
        elif "程序员" in prompt:
            return {"occupation": "程序员"}
        else:
            return {}

class TestRegistrationAgentSimple:
    """RegistrationAgent简单测试"""
    
    @pytest.fixture
    def mock_components(self):
        """创建简单的mock组件"""
        registration_manager = AsyncMock()
        profile_manager = AsyncMock()
        memory_manager = AsyncMock()
        db_manager = AsyncMock()
        ai_service = MockAIService()
        
        return {
            "registration_manager": registration_manager,
            "profile_manager": profile_manager,
            "memory_manager": memory_manager,
            "db_manager": db_manager,
            "ai_service": ai_service
        }
    
    def test_registration_agent_initialization(self, mock_components):
        """测试RegistrationAgent初始化"""
        agent = RegistrationAgent(**mock_components)
        
        assert agent.registration_manager is not None
        assert agent.profile_manager is not None
        assert agent.memory_manager is not None
        assert agent.db_manager is not None
        assert agent.ai_service is not None
        
        # 检查配置
        assert agent.max_attempts_per_field == 5
        assert agent.required_completion_rate == 1.0
        assert agent.optional_completion_rate == 0.75
        
        # 检查问题模板
        assert "name" in agent.question_templates
        assert "age" in agent.question_templates
        assert "city" in agent.question_templates
        assert "occupation" in agent.question_templates
    
    def test_registration_response_creation(self):
        """测试RegistrationResponse创建"""
        response = RegistrationResponse(
            content="测试内容",
            should_continue=True,
            registration_complete=False,
            progress=0.5
        )
        
        assert response.content == "测试内容"
        assert response.should_continue is True
        assert response.registration_complete is False
        assert response.progress == 0.5
        
        # 测试转换为字典
        response_dict = response.to_dict()
        assert response_dict["content"] == "测试内容"
        assert response_dict["should_continue"] is True
        assert response_dict["registration_complete"] is False
        assert response_dict["progress"] == 0.5
    
    def test_get_question_for_field(self, mock_components):
        """测试获取字段问题"""
        agent = RegistrationAgent(**mock_components)
        
        # 测试name字段的问题
        question1 = agent._get_question_for_field("name", 0)
        question2 = agent._get_question_for_field("name", 1)
        question3 = agent._get_question_for_field("name", 10)  # 超出范围
        
        assert isinstance(question1, str)
        assert isinstance(question2, str)
        assert isinstance(question3, str)
        assert len(question1) > 0
        assert len(question2) > 0
        assert len(question3) > 0
        
        # 测试不存在的字段
        question_unknown = agent._get_question_for_field("unknown_field", 0)
        assert isinstance(question_unknown, str)
    
    def test_simple_extract_info(self, mock_components):
        """测试简单信息提取"""
        agent = RegistrationAgent(**mock_components)
        
        # 测试年龄提取
        result1 = agent._simple_extract_info("我今年28岁")
        assert "age" in result1
        assert result1["age"] == 28
        
        result2 = agent._simple_extract_info("我28岁了")
        assert "age" in result2
        assert result2["age"] == 28
        
        # 测试城市提取
        result3 = agent._simple_extract_info("我住在北京")
        assert "city" in result3
        assert result3["city"] == "北京"
        
        # 测试职业提取
        result4 = agent._simple_extract_info("我是程序员")
        assert "occupation" in result4
        assert result4["occupation"] == "程序员"
        
        # 测试无信息
        result5 = agent._simple_extract_info("今天天气不错")
        assert len(result5) == 0
    
    def test_build_profile_from_progress(self, mock_components):
        """测试从注册进度构建档案"""
        agent = RegistrationAgent(**mock_components)
        
        # 创建mock的注册进度
        from unittest.mock import MagicMock
        
        mock_progress = MagicMock()
        
        # 模拟必须信息
        mock_required_info = {
            "name": MagicMock(collected=True, value="张三"),
            "age": MagicMock(collected=True, value="28"),
            "city": MagicMock(collected=True, value="北京"),
            "occupation": MagicMock(collected=True, value="程序员")
        }
        
        # 模拟可选信息
        mock_optional_info = {
            "interests": MagicMock(collected=True, data={"hobbies": ["编程", "阅读"]}),
            "personality": MagicMock(collected=True, data={"traits": ["内向", "理性"]})
        }
        
        mock_progress.required_info = mock_required_info
        mock_progress.optional_info = mock_optional_info
        
        # 构建档案
        profile = agent._build_profile_from_progress(mock_progress)
        
        # 验证档案结构
        assert "basic_info" in profile
        assert "personality" in profile
        assert "interests" in profile
        assert "relationship" in profile
        assert "lifestyle" in profile
        assert "values" in profile
        
        # 验证基本信息
        basic_info = profile["basic_info"]
        assert basic_info["name"] == "张三"
        assert basic_info["age"] == 28  # 应该转换为整数
        assert basic_info["city"] == "北京"
        assert basic_info["occupation"] == "程序员"
        
        # 验证可选信息
        assert profile["interests"]["hobbies"] == ["编程", "阅读"]
        assert profile["personality"]["traits"] == ["内向", "理性"]
    
    @pytest.mark.asyncio
    async def test_extract_information_from_input(self, mock_components):
        """测试从输入提取信息"""
        agent = RegistrationAgent(**mock_components)
        
        # 创建mock的注册进度
        mock_progress = MagicMock()
        
        # 测试AI提取成功的情况
        user_input = "我叫张三，今年28岁"
        extracted = await agent._extract_information_from_input(user_input, mock_progress)
        
        # 应该提取到姓名
        assert "name" in extracted
        assert extracted["name"] == "张三"
        
        # 测试fallback提取
        # 修改AI服务使其抛出异常，触发fallback
        agent.ai_service.extract_structured_data = AsyncMock(side_effect=Exception("AI服务错误"))
        
        extracted_fallback = await agent._extract_information_from_input("我今年30岁", mock_progress)
        
        # fallback应该能提取年龄
        assert "age" in extracted_fallback
        assert extracted_fallback["age"] == 30
    
    def test_question_templates_completeness(self, mock_components):
        """测试问题模板完整性"""
        agent = RegistrationAgent(**mock_components)
        
        required_fields = ["name", "age", "city", "occupation"]
        
        for field in required_fields:
            assert field in agent.question_templates
            templates = agent.question_templates[field]
            assert isinstance(templates, list)
            assert len(templates) >= 3  # 至少有3个不同的问题
            
            # 每个模板都应该是非空字符串
            for template in templates:
                assert isinstance(template, str)
                assert len(template.strip()) > 0
    
    def test_response_types(self, mock_components):
        """测试响应类型"""
        agent = RegistrationAgent(**mock_components)
        
        # 测试不同类型的响应
        response1 = RegistrationResponse("测试", should_continue=True)
        response2 = RegistrationResponse("完成", registration_complete=True)
        response3 = RegistrationResponse("进度", progress=0.75)
        
        assert response1.should_continue is True
        assert response1.registration_complete is False
        
        assert response2.registration_complete is True
        assert response2.should_continue is True  # 默认值
        
        assert response3.progress == 0.75
        assert response3.collected_info == {}  # 默认值
