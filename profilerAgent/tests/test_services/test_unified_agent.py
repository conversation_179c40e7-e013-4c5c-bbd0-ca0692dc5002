"""
Tests for UnifiedAgent
"""

import pytest
import sys
import os

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

# 直接从agent.services导入
from agent.services.unified_agent import UnifiedAgent, UnifiedAgentResponse
from agent.services.registration_agent import RegistrationAgent
from agent.services.regular_conversation_agent import RegularConversationAgent
from agent.services.ai_service import AIService

# Mock AI Service for testing
class MockAIService(AIService):
    def __init__(self):
        pass
    
    async def chat(self, messages):
        # 简单的mock响应
        last_message = messages[-1]["content"] if messages else ""
        
        if "名字" in last_message or "叫什么" in last_message:
            return type('Response', (), {'content': '很高兴认识你！你今年多大了？'})()
        elif "年龄" in last_message or "多大" in last_message:
            return type('Response', (), {'content': '你现在在哪个城市生活？'})()
        elif "城市" in last_message or "住在" in last_message:
            return type('Response', (), {'content': '你做什么工作？'})()
        elif "工作" in last_message or "职业" in last_message:
            return type('Response', (), {'content': '很好！你平时有什么兴趣爱好？'})()
        else:
            return type('Response', (), {'content': '很有趣！还有什么想分享的吗？'})()
    
    async def generate_text(self, prompt):
        if "开场白" in prompt or "greeting" in prompt:
            return "你好！很高兴见到你。最近怎么样？"
        elif "告别" in prompt or "farewell" in prompt:
            return "再见！期待下次聊天。"
        else:
            return "这是一个测试回复。"
    
    async def extract_structured_data(self, prompt):
        # 简单的信息提取mock
        if "张三" in prompt:
            return {"name": "张三"}
        elif "28" in prompt:
            return {"age": 28}
        elif "北京" in prompt:
            return {"city": "北京"}
        elif "程序员" in prompt:
            return {"occupation": "程序员"}
        else:
            return {}

class TestUnifiedAgent:
    """UnifiedAgent测试类"""
    
    @pytest.fixture
    def mock_components(self):
        """创建mock组件"""
        from unittest.mock import AsyncMock
        
        # Mock core components
        registration_manager = AsyncMock()
        profile_manager = AsyncMock()
        memory_manager = AsyncMock()
        db_manager = AsyncMock()
        ai_service = MockAIService()
        
        # 设置默认返回值
        registration_manager.is_registration_complete.return_value = False
        registration_manager.get_registration_state.return_value = None
        db_manager.get_user.return_value = None
        profile_manager.get_profile.return_value = None
        memory_manager.get_conversation_context.return_value = {"important_topics": []}
        
        return {
            "registration_manager": registration_manager,
            "profile_manager": profile_manager,
            "memory_manager": memory_manager,
            "db_manager": db_manager,
            "ai_service": ai_service
        }
    
    @pytest.mark.asyncio
    async def test_unified_agent_initialization(self, mock_components):
        """测试UnifiedAgent初始化"""
        agent = UnifiedAgent(**mock_components)
        
        assert agent.registration_manager is not None
        assert agent.profile_manager is not None
        assert agent.memory_manager is not None
        assert agent.db_manager is not None
        assert agent.registration_agent is not None
        assert agent.conversation_agent is not None
    
    @pytest.mark.asyncio
    async def test_new_user_flow(self, mock_components):
        """测试新用户流程"""
        agent = UnifiedAgent(**mock_components)
        user_id = "new_user_001"
        
        # 模拟新用户状态
        mock_components["registration_manager"].is_registration_complete.return_value = False
        mock_components["registration_manager"].get_registration_state.return_value = None
        mock_components["db_manager"].get_user.return_value = None
        
        # 开始对话
        response = await agent.start_conversation(user_id, phone_number="+1234567890")
        
        assert isinstance(response, UnifiedAgentResponse)
        assert response.agent_type == "registration"
        assert response.should_continue is True
        assert "你好" in response.content or "名字" in response.content
    
    @pytest.mark.asyncio
    async def test_registration_in_progress_flow(self, mock_components):
        """测试注册进行中流程"""
        agent = UnifiedAgent(**mock_components)
        user_id = "partial_user_001"
        
        # 模拟注册进行中状态
        from unittest.mock import MagicMock
        from registration_state import RegistrationProgress, RegistrationStatus
        from datetime import datetime
        
        mock_progress = MagicMock()
        mock_progress.user_id = user_id
        mock_progress.status = RegistrationStatus.VOICE_REGISTRATION_INCOMPLETE
        mock_progress.is_registration_complete.return_value = False
        mock_progress.get_next_required_info.return_value = "name"
        mock_progress.calculate_completion_rates.return_value = (False, 0.0)
        
        mock_components["registration_manager"].is_registration_complete.return_value = False
        mock_components["registration_manager"].get_registration_state.return_value = mock_progress
        
        # 处理用户输入
        response = await agent.process_input(user_id, "我叫张三")
        
        assert isinstance(response, UnifiedAgentResponse)
        assert response.agent_type == "registration"
        assert response.should_continue is True
    
    @pytest.mark.asyncio
    async def test_registered_user_flow(self, mock_components):
        """测试已注册用户流程"""
        agent = UnifiedAgent(**mock_components)
        user_id = "registered_user_001"
        
        # 模拟已注册用户状态
        mock_components["registration_manager"].is_registration_complete.return_value = True
        mock_components["db_manager"].get_user.return_value = {"user_id": user_id, "status": "active"}
        mock_components["profile_manager"].get_profile.return_value = {
            "basic_info": {"name": "张三", "age": 28}
        }
        
        # 开始对话
        response = await agent.start_conversation(user_id)
        
        assert isinstance(response, UnifiedAgentResponse)
        assert response.agent_type == "conversation"
        assert response.should_continue is True
        
        # 处理对话输入
        response = await agent.process_input(user_id, "最近工作很忙")
        
        assert isinstance(response, UnifiedAgentResponse)
        assert response.agent_type == "conversation"
        assert response.should_continue is True
    
    @pytest.mark.asyncio
    async def test_phone_number_extraction(self, mock_components):
        """测试手机号提取"""
        agent = UnifiedAgent(**mock_components)
        
        # 测试各种手机号格式
        test_cases = [
            ("我的手机号是13812345678", "13812345678"),
            ("电话：+86 138 1234 5678", "+86 138 1234 5678"),
            ("call me at ****** 123 4567", "****** 123 4567"),
            ("没有手机号", None)
        ]
        
        for text, expected in test_cases:
            result = agent._extract_phone_number(text)
            if expected:
                assert result is not None
                # 简单验证包含数字
                assert any(char.isdigit() for char in result)
            else:
                assert result is None
    
    @pytest.mark.asyncio
    async def test_get_user_info(self, mock_components):
        """测试获取用户信息"""
        agent = UnifiedAgent(**mock_components)
        user_id = "test_user_info"
        
        # 测试已注册用户
        mock_components["registration_manager"].is_registration_complete.return_value = True
        mock_components["profile_manager"].get_profile.return_value = {
            "basic_info": {"name": "测试用户"}
        }
        mock_components["memory_manager"].get_conversation_context.return_value = {
            "important_topics": [{"topic": "工作", "context": "程序员"}]
        }
        
        user_info = await agent.get_user_info(user_id)
        
        assert user_info["user_id"] == user_id
        assert user_info["status"] == "registered"
        assert "profile" in user_info
        assert "recent_topics" in user_info
    
    @pytest.mark.asyncio
    async def test_error_handling(self, mock_components):
        """测试错误处理"""
        agent = UnifiedAgent(**mock_components)
        user_id = "error_user"
        
        # 模拟组件错误
        mock_components["registration_manager"].is_registration_complete.side_effect = Exception("Test error")
        
        # 应该优雅地处理错误
        response = await agent.process_input(user_id, "测试输入")
        
        assert isinstance(response, UnifiedAgentResponse)
        assert response.agent_type == "error"
        assert "系统出现问题" in response.content
        assert response.should_continue is False
    
    @pytest.mark.asyncio
    async def test_response_conversion(self, mock_components):
        """测试响应转换"""
        agent = UnifiedAgent(**mock_components)
        
        # 测试注册响应转换
        from registration_agent import RegistrationResponse
        reg_response = RegistrationResponse(
            content="测试注册响应",
            should_continue=True,
            registration_complete=False,
            progress=0.5
        )
        
        unified_response = agent._convert_registration_response(reg_response)
        
        assert isinstance(unified_response, UnifiedAgentResponse)
        assert unified_response.content == "测试注册响应"
        assert unified_response.agent_type == "registration"
        assert unified_response.should_continue is True
        assert unified_response.progress == 0.5
        
        # 测试对话响应转换
        from regular_conversation_agent import ConversationResponse
        conv_response = ConversationResponse(
            content="测试对话响应",
            should_end=False,
            conversation_id="test_conv_123"
        )
        
        unified_response = agent._convert_conversation_response(conv_response)
        
        assert isinstance(unified_response, UnifiedAgentResponse)
        assert unified_response.content == "测试对话响应"
        assert unified_response.agent_type == "conversation"
        assert unified_response.should_continue is True
        assert unified_response.conversation_id == "test_conv_123"
