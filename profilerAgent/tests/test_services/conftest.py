"""
Services tests specific conftest
"""

import pytest
import pytest_asyncio
import asyncio
import sys
import os
from unittest.mock import AsyncMock, MagicMock

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'agent', 'core'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'agent', 'services'))

@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环"""
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()

@pytest_asyncio.fixture
async def mock_redis():
    """模拟Redis客户端"""
    redis_mock = AsyncMock()
    
    # 模拟Redis存储
    redis_storage = {}
    
    async def mock_get(key):
        return redis_storage.get(key)
    
    async def mock_set(key, value):
        redis_storage[key] = value
        return True
    
    async def mock_setex(key, ttl, value):
        redis_storage[key] = value
        return True
    
    async def mock_delete(key):
        if key in redis_storage:
            del redis_storage[key]
            return 1
        return 0
    
    redis_mock.get = mock_get
    redis_mock.set = mock_set
    redis_mock.setex = mock_setex
    redis_mock.delete = mock_delete
    
    # 添加存储访问方法（用于测试验证）
    redis_mock._storage = redis_storage
    
    return redis_mock

@pytest_asyncio.fixture
async def mock_db_manager():
    """模拟数据库管理器"""
    db_mock = AsyncMock()
    
    # 模拟内存存储
    users = {}
    profiles = {}
    conversations = {}
    messages = {}
    
    async def mock_create_user(user_id, phone_number, name=None):
        users[user_id] = {
            "user_id": user_id,
            "phone_number": phone_number,
            "name": name,
            "status": "pending"
        }
        return True
    
    async def mock_get_user(user_id):
        return users.get(user_id)
    
    async def mock_update_user_status(user_id, status):
        if user_id in users:
            users[user_id]["status"] = status
            return True
        return False
    
    async def mock_create_user_profile(user_id, profile_data):
        profiles[user_id] = profile_data
        return True
    
    async def mock_get_user_profile(user_id):
        return profiles.get(user_id)
    
    async def mock_create_conversation(user_id, conversation_type):
        conv_id = f"conv_{len(conversations)}"
        conversations[conv_id] = {
            "id": conv_id,
            "user_id": user_id,
            "type": conversation_type,
            "status": "active"
        }
        return conv_id
    
    async def mock_add_message(conversation_id, role, content):
        if conversation_id not in messages:
            messages[conversation_id] = []
        
        message = {
            "id": f"msg_{len(messages[conversation_id])}",
            "conversation_id": conversation_id,
            "role": role,
            "content": content,
            "timestamp": "2024-01-01T00:00:00"
        }
        messages[conversation_id].append(message)
        return message["id"]
    
    # 设置mock方法
    db_mock.create_user = mock_create_user
    db_mock.get_user = mock_get_user
    db_mock.update_user_status = mock_update_user_status
    db_mock.create_user_profile = mock_create_user_profile
    db_mock.get_user_profile = mock_get_user_profile
    db_mock.create_conversation = mock_create_conversation
    db_mock.add_message = mock_add_message
    
    # 添加存储访问方法（用于测试验证）
    db_mock._users = users
    db_mock._profiles = profiles
    db_mock._conversations = conversations
    db_mock._messages = messages
    
    return db_mock

@pytest_asyncio.fixture
async def mock_registration_manager(mock_redis, mock_db_manager):
    """模拟注册状态管理器"""
    manager_mock = AsyncMock()
    
    # 模拟注册状态存储
    registrations = {}
    
    async def mock_create_registration(user_id, phone_number):
        from registration_state import RegistrationProgress, RegistrationStatus
        from datetime import datetime
        
        progress = RegistrationProgress(
            user_id=user_id,
            phone_number=phone_number,
            status=RegistrationStatus.PHONE_VERIFIED
        )
        registrations[user_id] = progress
        return progress
    
    async def mock_get_registration_state(user_id):
        return registrations.get(user_id)
    
    async def mock_is_registration_complete(user_id):
        progress = registrations.get(user_id)
        return progress.is_registration_complete() if progress else False
    
    async def mock_update_status(user_id, status):
        if user_id in registrations:
            registrations[user_id].status = status
            return True
        return False
    
    # 设置mock方法
    manager_mock.create_registration = mock_create_registration
    manager_mock.get_registration_state = mock_get_registration_state
    manager_mock.is_registration_complete = mock_is_registration_complete
    manager_mock.update_status = mock_update_status
    
    # 添加存储访问方法
    manager_mock._registrations = registrations
    
    return manager_mock

@pytest_asyncio.fixture
async def mock_profile_manager(mock_redis, mock_db_manager):
    """模拟档案管理器"""
    manager_mock = AsyncMock()
    
    async def mock_create_profile(user_id, profile_data):
        await mock_db_manager.create_user_profile(user_id, profile_data)
        return True
    
    async def mock_get_profile(user_id):
        return await mock_db_manager.get_user_profile(user_id)
    
    async def mock_extract_and_update_from_conversation(user_id, user_input, ai_response):
        # 简单的mock实现
        return True
    
    # 设置mock方法
    manager_mock.create_profile = mock_create_profile
    manager_mock.get_profile = mock_get_profile
    manager_mock.extract_and_update_from_conversation = mock_extract_and_update_from_conversation
    
    return manager_mock

@pytest_asyncio.fixture
async def mock_memory_manager(mock_redis, mock_db_manager):
    """模拟记忆管理器"""
    manager_mock = AsyncMock()
    
    # 模拟记忆存储
    memories = {}
    
    async def mock_add_message(user_id, role, content):
        if user_id not in memories:
            memories[user_id] = []
        
        message = {
            "role": role,
            "content": content,
            "timestamp": "2024-01-01T00:00:00"
        }
        memories[user_id].append(message)
        return True
    
    async def mock_get_conversation_context(user_id):
        return {
            "important_topics": [],
            "recent_messages": memories.get(user_id, [])
        }
    
    async def mock_build_llm_context(user_id, user_input, profile):
        messages = memories.get(user_id, [])
        return messages + [{"role": "user", "content": user_input}]
    
    async def mock_clear_memory(user_id):
        if user_id in memories:
            del memories[user_id]
        return True
    
    # 设置mock方法
    manager_mock.add_message = mock_add_message
    manager_mock.get_conversation_context = mock_get_conversation_context
    manager_mock.build_llm_context = mock_build_llm_context
    manager_mock.clear_memory = mock_clear_memory
    
    # 添加存储访问方法
    manager_mock._memories = memories
    
    return manager_mock

@pytest.fixture
def mock_ai_service():
    """模拟AI服务"""
    from unittest.mock import MagicMock
    
    ai_mock = MagicMock()
    
    # 模拟chat方法
    def mock_chat(messages):
        last_message = messages[-1]["content"] if messages else ""
        
        if "名字" in last_message:
            return MagicMock(content="很高兴认识你！你今年多大了？")
        elif "年龄" in last_message:
            return MagicMock(content="你现在在哪个城市生活？")
        elif "城市" in last_message:
            return MagicMock(content="你做什么工作？")
        else:
            return MagicMock(content="很有趣！还有什么想分享的吗？")
    
    # 模拟generate_text方法
    def mock_generate_text(prompt):
        if "开场白" in prompt:
            return "你好！很高兴见到你。"
        elif "告别" in prompt:
            return "再见！期待下次聊天。"
        else:
            return "这是一个测试回复。"
    
    # 模拟extract_structured_data方法
    def mock_extract_structured_data(prompt):
        if "张三" in prompt:
            return {"name": "张三"}
        elif "28" in prompt:
            return {"age": 28}
        else:
            return {}
    
    ai_mock.chat = mock_chat
    ai_mock.generate_text = mock_generate_text
    ai_mock.extract_structured_data = mock_extract_structured_data
    
    return ai_mock
