"""
Independent tests for Agent logic - no complex imports
"""

import pytest
import re
from unittest.mock import MagicMock

class TestAgentLogic:
    """测试Agent的核心逻辑"""
    
    def test_phone_number_extraction(self):
        """测试手机号提取逻辑"""
        
        def extract_phone_number(text: str) -> str:
            """从文本中提取手机号"""
            # 匹配各种手机号格式
            patterns = [
                r'1[3-9]\d{9}',  # 中国手机号
                r'\+86\s*1[3-9]\d{9}',  # 带国家代码
                r'\+1\s*\d{10}',  # 美国手机号
                r'\+\d{1,3}\s*\d{10,11}'  # 其他国际号码
            ]
            
            for pattern in patterns:
                match = re.search(pattern, text.replace(' ', '').replace('-', ''))
                if match:
                    return match.group(0)
            
            return None
        
        # 测试用例
        test_cases = [
            ("我的手机号是13812345678", "13812345678"),
            ("电话：+86 138 1234 5678", "+86 138 1234 5678"),
            ("call me at ****** 123 4567", "****** 123 4567"),
            ("没有手机号", None),
            ("13912345678是我的号码", "13912345678"),
            ("联系方式：+86-138-1234-5678", "+86-138-1234-5678")
        ]
        
        for text, expected in test_cases:
            result = extract_phone_number(text)
            if expected:
                assert result is not None
                # 验证包含期望的数字
                assert any(char.isdigit() for char in result)
            else:
                assert result is None
    
    def test_simple_info_extraction(self):
        """测试简单信息提取逻辑"""
        
        def simple_extract_info(user_input: str) -> dict:
            """简单的信息提取（fallback）"""
            extracted = {}
            text_lower = user_input.lower()
            
            # 年龄提取
            age_patterns = [r"我(\d{1,2})岁", r"今年(\d{1,2})", r"(\d{1,2})岁"]
            for pattern in age_patterns:
                match = re.search(pattern, user_input)
                if match:
                    age = int(match.group(1))
                    if 18 <= age <= 80:
                        extracted["age"] = age
                        break
            
            # 城市提取
            cities = ["北京", "上海", "广州", "深圳", "杭州", "南京", "武汉", "成都", "西安", "重庆"]
            for city in cities:
                if city in user_input:
                    extracted["city"] = city
                    break
            
            # 职业提取
            occupations = {
                "程序员": "程序员", "软件工程师": "软件工程师", "工程师": "工程师",
                "医生": "医生", "护士": "护士", "教师": "教师", "老师": "教师",
                "销售": "销售", "设计师": "设计师", "会计": "会计", "律师": "律师"
            }
            for keyword, occupation in occupations.items():
                if keyword in text_lower:
                    extracted["occupation"] = occupation
                    break
            
            return extracted
        
        # 测试用例
        test_cases = [
            ("我今年28岁", {"age": 28}),
            ("我28岁了", {"age": 28}),
            ("我住在北京", {"city": "北京"}),
            ("在上海工作", {"city": "上海"}),
            ("我是程序员", {"occupation": "程序员"}),
            ("我是一名软件工程师", {"occupation": "软件工程师"}),
            ("我28岁，住在北京，是程序员", {"age": 28, "city": "北京", "occupation": "程序员"}),
            ("今天天气不错", {}),
            ("我100岁了", {}),  # 年龄超出范围
            ("我在火星工作", {})  # 未知城市
        ]
        
        for text, expected in test_cases:
            result = simple_extract_info(text)
            assert result == expected
    
    def test_conversation_end_detection(self):
        """测试对话结束检测逻辑"""
        
        def should_end_conversation(user_input: str, ai_response: str) -> bool:
            """判断是否应该结束对话"""
            # 检查用户是否表达了结束意图
            end_signals = [
                "再见", "拜拜", "结束", "不聊了", "挂了", "走了",
                "bye", "goodbye", "see you", "talk later"
            ]
            
            user_input_lower = user_input.lower()
            for signal in end_signals:
                if signal in user_input_lower:
                    return True
            
            # 检查AI是否给出了结束信号
            ai_end_signals = ["再见", "拜拜", "下次聊", "保重"]
            ai_response_lower = ai_response.lower()
            for signal in ai_end_signals:
                if signal in ai_response_lower:
                    return True
            
            return False
        
        # 测试用例
        test_cases = [
            ("再见", "好的，再见！", True),
            ("拜拜", "拜拜！", True),
            ("我要走了", "好的，保重！", True),
            ("bye", "goodbye!", True),
            ("继续聊天", "好的，我们继续", False),
            ("你好", "你好！", False),
            ("今天天气不错", "是的，天气很好", False),
            ("不想聊了", "理解，再见", True),
            ("我们聊点别的", "好的，聊什么？", False)
        ]
        
        for user_input, ai_response, expected in test_cases:
            result = should_end_conversation(user_input, ai_response)
            assert result == expected
    
    def test_profile_formatting(self):
        """测试档案格式化逻辑"""
        
        def format_profile_for_prompt(profile: dict) -> str:
            """格式化档案信息用于提示"""
            if not profile:
                return "暂无档案信息"
            
            formatted_parts = []
            
            # 基本信息
            basic_info = profile.get("basic_info", {})
            if basic_info:
                parts = []
                if basic_info.get("name"):
                    parts.append(f"姓名：{basic_info['name']}")
                if basic_info.get("age"):
                    parts.append(f"年龄：{basic_info['age']}")
                if basic_info.get("city"):
                    parts.append(f"城市：{basic_info['city']}")
                if basic_info.get("occupation"):
                    parts.append(f"职业：{basic_info['occupation']}")
                
                if parts:
                    formatted_parts.append("基本信息：" + "，".join(parts))
            
            # 兴趣爱好
            interests = profile.get("interests", {})
            if interests and interests.get("hobbies"):
                hobbies = interests["hobbies"]
                if isinstance(hobbies, list):
                    formatted_parts.append(f"兴趣爱好：{', '.join(hobbies)}")
            
            # 性格特征
            personality = profile.get("personality", {})
            if personality and personality.get("traits"):
                traits = personality["traits"]
                if isinstance(traits, list):
                    formatted_parts.append(f"性格特征：{', '.join(traits)}")
            
            return "\n".join(formatted_parts) if formatted_parts else "暂无详细档案信息"
        
        # 测试用例
        test_cases = [
            # 空档案
            ({}, "暂无档案信息"),
            (None, "暂无档案信息"),
            
            # 只有基本信息
            ({
                "basic_info": {
                    "name": "张三",
                    "age": 28,
                    "city": "北京"
                }
            }, "基本信息：姓名：张三，年龄：28，城市：北京"),
            
            # 完整档案
            ({
                "basic_info": {
                    "name": "李四",
                    "age": 25,
                    "city": "上海",
                    "occupation": "程序员"
                },
                "interests": {
                    "hobbies": ["编程", "阅读", "游泳"]
                },
                "personality": {
                    "traits": ["内向", "理性", "细心"]
                }
            }, "基本信息：姓名：李四，年龄：25，城市：上海，职业：程序员\n兴趣爱好：编程, 阅读, 游泳\n性格特征：内向, 理性, 细心"),
            
            # 部分信息缺失
            ({
                "basic_info": {
                    "name": "王五"
                },
                "interests": {
                    "hobbies": ["音乐"]
                }
            }, "基本信息：姓名：王五\n兴趣爱好：音乐")
        ]
        
        for profile, expected in test_cases:
            result = format_profile_for_prompt(profile)
            assert result == expected
    
    def test_question_selection_logic(self):
        """测试问题选择逻辑"""
        
        def get_question_for_field(field_name: str, attempt: int, question_templates: dict) -> str:
            """获取指定字段的问题"""
            templates = question_templates.get(field_name, ["请提供相关信息。"])
            attempt_index = min(attempt, len(templates) - 1)
            return templates[attempt_index]
        
        # 模拟问题模板
        question_templates = {
            "name": [
                "你叫什么名字？",
                "请告诉我你的名字。",
                "我需要知道你的姓名。"
            ],
            "age": [
                "你今年多大了？",
                "请告诉我你的年龄。"
            ]
        }
        
        # 测试正常情况
        assert get_question_for_field("name", 0, question_templates) == "你叫什么名字？"
        assert get_question_for_field("name", 1, question_templates) == "请告诉我你的名字。"
        assert get_question_for_field("name", 2, question_templates) == "我需要知道你的姓名。"
        
        # 测试超出范围的尝试次数
        assert get_question_for_field("name", 10, question_templates) == "我需要知道你的姓名。"
        
        # 测试不存在的字段
        assert get_question_for_field("unknown", 0, question_templates) == "请提供相关信息。"
        
        # 测试age字段
        assert get_question_for_field("age", 0, question_templates) == "你今年多大了？"
        assert get_question_for_field("age", 1, question_templates) == "请告诉我你的年龄。"
        assert get_question_for_field("age", 5, question_templates) == "请告诉我你的年龄。"  # 超出范围，使用最后一个
    
    def test_response_data_structure(self):
        """测试响应数据结构"""
        
        class RegistrationResponse:
            """注册响应数据结构"""
            
            def __init__(self, content: str, should_continue: bool = True, 
                         registration_complete: bool = False, next_question: str = None,
                         collected_info: dict = None, progress: float = 0.0):
                self.content = content
                self.should_continue = should_continue
                self.registration_complete = registration_complete
                self.next_question = next_question
                self.collected_info = collected_info or {}
                self.progress = progress
            
            def to_dict(self) -> dict:
                return {
                    "content": self.content,
                    "should_continue": self.should_continue,
                    "registration_complete": self.registration_complete,
                    "next_question": self.next_question,
                    "collected_info": self.collected_info,
                    "progress": self.progress
                }
        
        # 测试默认值
        response1 = RegistrationResponse("测试内容")
        assert response1.content == "测试内容"
        assert response1.should_continue is True
        assert response1.registration_complete is False
        assert response1.next_question is None
        assert response1.collected_info == {}
        assert response1.progress == 0.0
        
        # 测试自定义值
        response2 = RegistrationResponse(
            content="完成注册",
            should_continue=False,
            registration_complete=True,
            progress=1.0,
            collected_info={"name": "张三"}
        )
        assert response2.content == "完成注册"
        assert response2.should_continue is False
        assert response2.registration_complete is True
        assert response2.progress == 1.0
        assert response2.collected_info == {"name": "张三"}
        
        # 测试转换为字典
        response_dict = response2.to_dict()
        expected_dict = {
            "content": "完成注册",
            "should_continue": False,
            "registration_complete": True,
            "next_question": None,
            "collected_info": {"name": "张三"},
            "progress": 1.0
        }
        assert response_dict == expected_dict
