"""
Sample data for testing
"""

from datetime import datetime
from agent.core.registration_state import RegistrationStatus

# 用户测试数据
SAMPLE_USERS = {
    "new_user": {
        "user_id": "new_user_001",
        "phone_number": "+**********",
        "name": None,
        "status": "pending"
    },
    "verified_user": {
        "user_id": "verified_user_001", 
        "phone_number": "+1234567891",
        "name": "李四",
        "status": "phone_verified"
    },
    "registered_user": {
        "user_id": "registered_user_001",
        "phone_number": "+1234567892", 
        "name": "王五",
        "status": "active"
    }
}

# 档案测试数据
SAMPLE_PROFILES = {
    "empty_profile": {
        "basic_info": {},
        "personality": {},
        "interests": {},
        "relationship": {},
        "lifestyle": {},
        "values": {}
    },
    "partial_profile": {
        "basic_info": {
            "name": "张三",
            "age": 28,
            "city": "北京"
        },
        "interests": {
            "hobbies": ["编程", "阅读"]
        },
        "personality": {},
        "relationship": {},
        "lifestyle": {},
        "values": {}
    },
    "complete_profile": {
        "basic_info": {
            "name": "张三",
            "age": 28,
            "city": "北京",
            "occupation": "软件工程师",
            "education": "本科",
            "company": "科技公司"
        },
        "personality": {
            "mbti": "INTJ",
            "traits": ["内向", "理性", "计划性强"],
            "social_energy": "introvert",
            "communication_style": "直接"
        },
        "interests": {
            "hobbies": ["编程", "阅读", "爬山"],
            "music": ["古典", "爵士"],
            "sports": ["羽毛球", "游泳"],
            "entertainment": ["电影", "纪录片"],
            "travel": ["自然风光", "历史文化"]
        },
        "relationship": {
            "looking_for": "长期关系",
            "preferences": ["有共同兴趣", "性格互补", "价值观相近"],
            "deal_breakers": ["吸烟", "不爱运动", "不诚实"]
        },
        "lifestyle": {
            "work_schedule": "朝九晚六",
            "social_life": "小圈子社交",
            "health_habits": ["规律运动", "健康饮食"],
            "living_situation": "独居"
        },
        "values": {
            "life_goals": ["事业发展", "家庭幸福"],
            "family_values": "重视家庭",
            "career_priorities": "工作生活平衡",
            "personal_beliefs": "诚实守信"
        }
    }
}

# 注册进度测试数据
SAMPLE_REGISTRATION_PROGRESS = {
    "new_registration": {
        "user_id": "new_user_001",
        "phone_number": "+**********",
        "status": RegistrationStatus.PHONE_VERIFICATION_PENDING,
        "required_info": {
            "name": {"collected": False, "attempts": 0},
            "age": {"collected": False, "attempts": 0},
            "city": {"collected": False, "attempts": 0},
            "occupation": {"collected": False, "attempts": 0}
        },
        "optional_info": {
            "interests": {"collected": False, "completion_rate": 0.0},
            "personality": {"collected": False, "completion_rate": 0.0},
            "relationship_goals": {"collected": False, "completion_rate": 0.0},
            "lifestyle": {"collected": False, "completion_rate": 0.0},
            "values": {"collected": False, "completion_rate": 0.0}
        },
        "conversation_context": {
            "current_topic": None,
            "resistant_topics": [],
            "successful_topics": [],
            "last_question": None
        }
    },
    "partial_registration": {
        "user_id": "partial_user_001",
        "phone_number": "+1234567891",
        "status": RegistrationStatus.VOICE_REGISTRATION_INCOMPLETE,
        "required_info": {
            "name": {"collected": True, "value": "李四", "attempts": 1},
            "age": {"collected": True, "value": "25", "attempts": 1},
            "city": {"collected": False, "attempts": 2, "last_question": "你在哪个城市？"},
            "occupation": {"collected": False, "attempts": 0}
        },
        "optional_info": {
            "interests": {"collected": True, "completion_rate": 0.6},
            "personality": {"collected": False, "completion_rate": 0.0},
            "relationship_goals": {"collected": False, "completion_rate": 0.0},
            "lifestyle": {"collected": False, "completion_rate": 0.0},
            "values": {"collected": False, "completion_rate": 0.0}
        },
        "conversation_context": {
            "current_topic": "基本信息",
            "resistant_topics": [],
            "successful_topics": ["姓名", "年龄", "兴趣"],
            "last_question": "你在哪个城市？"
        }
    },
    "complete_registration": {
        "user_id": "complete_user_001",
        "phone_number": "+1234567892",
        "status": RegistrationStatus.REGISTERED,
        "required_info": {
            "name": {"collected": True, "value": "王五", "attempts": 1},
            "age": {"collected": True, "value": "30", "attempts": 1},
            "city": {"collected": True, "value": "上海", "attempts": 1},
            "occupation": {"collected": True, "value": "产品经理", "attempts": 1}
        },
        "optional_info": {
            "interests": {"collected": True, "completion_rate": 1.0},
            "personality": {"collected": True, "completion_rate": 0.8},
            "relationship_goals": {"collected": True, "completion_rate": 0.9},
            "lifestyle": {"collected": True, "completion_rate": 0.7},
            "values": {"collected": False, "completion_rate": 0.0}
        },
        "conversation_context": {
            "current_topic": "完成注册",
            "resistant_topics": ["价值观"],
            "successful_topics": ["基本信息", "兴趣", "性格", "关系目标", "生活方式"],
            "last_question": None
        }
    }
}

# 对话消息测试数据
SAMPLE_CONVERSATIONS = {
    "basic_info_collection": [
        {"role": "assistant", "content": "你好！我是你的约会档案助手。你叫什么名字？"},
        {"role": "user", "content": "我叫张三"},
        {"role": "assistant", "content": "很高兴认识你张三！你今年多大了？"},
        {"role": "user", "content": "28岁"},
        {"role": "assistant", "content": "你现在在哪个城市生活？"},
        {"role": "user", "content": "北京"},
        {"role": "assistant", "content": "你在北京做什么工作？"},
        {"role": "user", "content": "我是软件工程师"}
    ],
    "interests_collection": [
        {"role": "assistant", "content": "软件工程师很有挑战性！你平时有什么兴趣爱好？"},
        {"role": "user", "content": "我喜欢编程、看书，周末经常去爬山"},
        {"role": "assistant", "content": "爬山很棒！你喜欢什么类型的音乐？"},
        {"role": "user", "content": "古典音乐和爵士乐"},
        {"role": "assistant", "content": "很有品味！你平时做什么运动？"},
        {"role": "user", "content": "羽毛球和游泳"}
    ],
    "personality_collection": [
        {"role": "assistant", "content": "你觉得自己是内向还是外向的人？"},
        {"role": "user", "content": "比较内向，喜欢安静的环境"},
        {"role": "assistant", "content": "你在做决定时更倾向于理性分析还是跟随感觉？"},
        {"role": "user", "content": "我比较理性，喜欢分析利弊"},
        {"role": "assistant", "content": "你喜欢提前计划还是随性而为？"},
        {"role": "user", "content": "我喜欢有计划，这样比较有安全感"}
    ],
    "relationship_collection": [
        {"role": "assistant", "content": "你希望找什么样的关系？"},
        {"role": "user", "content": "希望找一个长期稳定的关系"},
        {"role": "assistant", "content": "你觉得理想的另一半应该有什么特质？"},
        {"role": "user", "content": "希望有共同兴趣，性格能互补"},
        {"role": "assistant", "content": "有什么是你绝对不能接受的？"},
        {"role": "user", "content": "不能接受吸烟和不诚实的人"}
    ]
}

# 重要话题测试数据
SAMPLE_IMPORTANT_TOPICS = [
    {
        "topic": "工作",
        "context": "我是软件工程师，主要做后端开发",
        "importance_score": 1.2,
        "related_keywords": ["软件工程师", "后端开发"]
    },
    {
        "topic": "爱好",
        "context": "我喜欢编程、看书，周末经常去爬山",
        "importance_score": 1.1,
        "related_keywords": ["编程", "看书", "爬山"]
    },
    {
        "topic": "关系",
        "context": "希望找一个长期稳定的关系",
        "importance_score": 1.3,
        "related_keywords": ["长期关系", "稳定"]
    }
]

# 置信度分数测试数据
SAMPLE_CONFIDENCE_SCORES = {
    "high_confidence": {
        "basic_info.name": 0.95,
        "basic_info.age": 0.9,
        "basic_info.occupation": 0.85,
        "interests.hobbies": 0.9
    },
    "medium_confidence": {
        "personality.mbti": 0.7,
        "relationship.preferences": 0.75,
        "lifestyle.work_schedule": 0.65
    },
    "low_confidence": {
        "values.life_goals": 0.5,
        "personality.communication_style": 0.45
    }
}
