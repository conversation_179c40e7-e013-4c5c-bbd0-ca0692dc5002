#!/usr/bin/env python3
"""
测试 ngrok WebSocket 连接
验证公网 WebSocket URL 是否正常工作
"""

import asyncio
import websockets
import json
import os
import sys
from datetime import datetime

def get_websocket_url():
    """获取 WebSocket URL"""
    base_url = os.getenv("TWILIO_WEBHOOK_URL", "https://localhost:8000")
    
    # 将 https 替换为 wss 用于 WebSocket
    if base_url.startswith("https://"):
        websocket_url = base_url.replace("https://", "wss://")
    elif base_url.startswith("http://"):
        websocket_url = base_url.replace("http://", "ws://")
    else:
        websocket_url = f"wss://{base_url}"
    
    # 添加 WebSocket 路径
    if not websocket_url.endswith("/"):
        websocket_url += "/"
    
    return websocket_url

async def test_ngrok_basic_websocket():
    """测试 ngrok 基础 WebSocket"""
    base_url = get_websocket_url()
    test_url = base_url + "ws-test/basic"
    
    print(f"🧪 Testing ngrok WebSocket: {test_url}")
    
    try:
        print("🔗 Connecting...")
        async with websockets.connect(test_url) as websocket:
            print("✅ Connected successfully!")
            
            # 等待欢迎消息
            welcome_msg = await websocket.recv()
            print(f"📥 Welcome: {json.loads(welcome_msg)}")
            
            # 发送 ping
            ping_msg = {"type": "ping"}
            await websocket.send(json.dumps(ping_msg))
            print(f"📤 Sent ping")
            
            # 接收 pong
            pong_msg = await websocket.recv()
            print(f"📥 Received: {json.loads(pong_msg)}")
            
            print("✅ ngrok WebSocket test successful!")
            
    except Exception as e:
        print(f"❌ ngrok WebSocket test failed: {e}")
        return False
    
    return True

async def test_ngrok_conversation_relay():
    """测试 ngrok ConversationRelay WebSocket"""
    base_url = get_websocket_url()
    relay_url = base_url + "conversation-relay/voice"
    
    print(f"🧪 Testing ngrok ConversationRelay: {relay_url}")
    
    try:
        print("🔗 Connecting...")
        async with websockets.connect(relay_url) as websocket:
            print("✅ Connected successfully!")
            
            # 发送 setup 消息
            setup_msg = {
                "type": "setup",
                "sessionId": "ngrok_test_session",
                "callSid": "ngrok_test_call",
                "accountSid": "test_account",
                "from": "+**********",
                "to": "+**********"
            }
            await websocket.send(json.dumps(setup_msg))
            print(f"📤 Sent setup")
            
            # 等待响应
            response = await websocket.recv()
            print(f"📥 Setup response: {json.loads(response)}")
            
            # 发送用户输入
            prompt_msg = {
                "type": "prompt",
                "text": "Hello from ngrok test!",
                "confidence": 0.95,
                "timestamp": datetime.now().isoformat()
            }
            await websocket.send(json.dumps(prompt_msg))
            print(f"📤 Sent prompt")
            
            # 接收确认和回复
            for i in range(2):  # 期望收到确认 + 回复
                response = await websocket.recv()
                data = json.loads(response)
                print(f"📥 Response {i+1}: {data}")
            
            print("✅ ngrok ConversationRelay test successful!")
            
    except Exception as e:
        print(f"❌ ngrok ConversationRelay test failed: {e}")
        return False
    
    return True

async def test_twiml_endpoint():
    """测试 TwiML 端点"""
    import aiohttp
    
    base_url = os.getenv("TWILIO_WEBHOOK_URL", "https://localhost:8000")
    twiml_url = base_url + "/conversation-relay/twiml/start"
    
    print(f"🧪 Testing TwiML endpoint: {twiml_url}")
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(twiml_url) as response:
                if response.status == 200:
                    twiml_content = await response.text()
                    print("✅ TwiML endpoint successful!")
                    print(f"📄 TwiML content preview:")
                    print(twiml_content[:200] + "..." if len(twiml_content) > 200 else twiml_content)
                    
                    # 检查是否包含正确的 WebSocket URL
                    expected_ws_url = get_websocket_url() + "conversation-relay/voice"
                    if expected_ws_url in twiml_content:
                        print(f"✅ WebSocket URL correctly configured: {expected_ws_url}")
                    else:
                        print(f"⚠️  WebSocket URL not found in TwiML")
                    
                    return True
                else:
                    print(f"❌ TwiML endpoint failed: {response.status}")
                    return False
                    
    except Exception as e:
        print(f"❌ TwiML endpoint test failed: {e}")
        return False

def print_configuration():
    """打印当前配置"""
    print("🔧 Current Configuration:")
    print(f"   TWILIO_WEBHOOK_URL: {os.getenv('TWILIO_WEBHOOK_URL', 'Not set')}")
    print(f"   WebSocket Base URL: {get_websocket_url()}")
    print(f"   Basic Test URL: {get_websocket_url()}ws-test/basic")
    print(f"   ConversationRelay URL: {get_websocket_url()}conversation-relay/voice")
    print(f"   TwiML URL: {os.getenv('TWILIO_WEBHOOK_URL', 'https://localhost:8000')}/conversation-relay/twiml/start")
    print()

async def main():
    """主测试函数"""
    print("🚀 ngrok WebSocket Test Suite")
    print("=" * 60)
    
    print_configuration()
    
    # 检查环境变量
    if not os.getenv("TWILIO_WEBHOOK_URL"):
        print("⚠️  Warning: TWILIO_WEBHOOK_URL not set, using localhost")
        print("   Please set: export TWILIO_WEBHOOK_URL=https://your-ngrok-url.ngrok-free.app")
        print()
    
    success_count = 0
    total_tests = 3
    
    # 测试基础 WebSocket
    print("1️⃣  Testing Basic WebSocket...")
    if await test_ngrok_basic_websocket():
        success_count += 1
    print()
    
    # 测试 ConversationRelay WebSocket
    print("2️⃣  Testing ConversationRelay WebSocket...")
    if await test_ngrok_conversation_relay():
        success_count += 1
    print()
    
    # 测试 TwiML 端点
    print("3️⃣  Testing TwiML Endpoint...")
    if await test_twiml_endpoint():
        success_count += 1
    print()
    
    # 总结
    print("=" * 60)
    print(f"📊 Test Results: {success_count}/{total_tests} tests passed")
    
    if success_count == total_tests:
        print("🎉 All tests passed! Your ngrok WebSocket setup is ready for Twilio!")
        print()
        print("🔗 Next steps:")
        print("   1. Use this TwiML URL in your Twilio webhook configuration")
        print("   2. Test with a real Twilio phone call")
        print("   3. Monitor the logs for ConversationRelay connections")
    else:
        print("❌ Some tests failed. Please check your configuration and server status.")
    
    return success_count == total_tests

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        sys.exit(1)
