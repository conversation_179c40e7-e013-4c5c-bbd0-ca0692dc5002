#!/usr/bin/env python3
"""
测试新的 AgentInterface
验证核心AI对话功能
"""

import asyncio
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agent.agent_interface import AgentInterface

async def test_agent_interface():
    """测试 AgentInterface 基本功能"""
    print("🚀 开始测试 AgentInterface...")
    
    # 创建 AgentInterface 实例
    agent = AgentInterface()
    
    try:
        # 测试初始化
        print("\n1. 测试初始化...")
        success = await agent.initialize()
        print(f"   初始化结果: {'✅ 成功' if success else '❌ 失败'}")
        
        # 测试健康检查
        print("\n2. 测试健康检查...")
        health = await agent.health_check()
        print(f"   健康状态: {health.get('health', {}).get('status', 'unknown')}")
        print(f"   组件状态: {health.get('health', {}).get('components', {})}")
        
        # 测试开始对话
        print("\n3. 测试开始对话...")
        test_user_id = "test_user_123"
        test_phone = "+**********"
        
        start_result = await agent.start_conversation(test_user_id, test_phone)
        print(f"   开始对话结果: {'✅ 成功' if start_result.get('success') else '❌ 失败'}")
        if start_result.get('success'):
            print(f"   欢迎消息: {start_result.get('welcome_message', '')[:100]}...")
            print(f"   Agent类型: {start_result.get('agent_type')}")
            print(f"   是否新用户: {start_result.get('is_new_user')}")
        
        # 测试处理用户消息
        print("\n4. 测试处理用户消息...")
        test_message = "你好，我叫张三"
        
        message_result = await agent.process_user_message(test_user_id, test_message)
        print(f"   消息处理结果: {'✅ 成功' if message_result.get('success') else '❌ 失败'}")
        if message_result.get('success'):
            print(f"   AI回复: {message_result.get('response', '')[:100]}...")
            print(f"   Agent类型: {message_result.get('agent_type')}")
            print(f"   应该继续: {message_result.get('should_continue')}")
        else:
            print(f"   错误: {message_result.get('error')}")
        
        # 测试获取用户状态
        print("\n5. 测试获取用户状态...")
        status_result = await agent.get_user_status(test_user_id)
        print(f"   状态查询结果: {'✅ 成功' if status_result.get('success') else '❌ 失败'}")
        if status_result.get('success'):
            print(f"   注册状态: {status_result.get('registration_status')}")
            print(f"   档案完成度: {status_result.get('profile_completion', 0):.2f}")
            print(f"   对话次数: {status_result.get('conversation_count', 0)}")
        
        # 测试对话历史
        print("\n6. 测试对话历史...")
        history_result = await agent.get_conversation_history(test_user_id, limit=5)
        print(f"   历史查询结果: {'✅ 成功' if history_result.get('success') else '❌ 失败'}")
        if history_result.get('success'):
            print(f"   消息总数: {history_result.get('total_messages', 0)}")
        
        # 测试结束对话
        print("\n7. 测试结束对话...")
        end_result = await agent.end_conversation(test_user_id, "test_completed")
        print(f"   结束对话结果: {'✅ 成功' if end_result.get('success') else '❌ 失败'}")
        if end_result.get('success'):
            print(f"   告别消息: {end_result.get('farewell_message')}")
            print(f"   档案完成度: {end_result.get('profile_completion', 0):.2f}")
        
        print("\n✅ AgentInterface 测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理资源
        print("\n🧹 清理资源...")
        await agent.cleanup()

async def test_multiple_messages():
    """测试多轮对话"""
    print("\n🔄 测试多轮对话...")
    
    agent = AgentInterface()
    await agent.initialize()
    
    test_user_id = "test_user_456"
    test_phone = "+9876543210"
    
    # 开始对话
    await agent.start_conversation(test_user_id, test_phone)
    
    # 模拟多轮对话
    messages = [
        "你好",
        "我叫李四",
        "我今年25岁",
        "我住在北京",
        "我是一名程序员"
    ]
    
    for i, message in enumerate(messages, 1):
        print(f"\n   第{i}轮对话:")
        print(f"   用户: {message}")
        
        result = await agent.process_user_message(test_user_id, message)
        if result.get('success'):
            print(f"   AI: {result.get('response', '')[:80]}...")
            print(f"   更新信息: {result.get('updated_info', {})}")
        else:
            print(f"   错误: {result.get('error')}")
    
    # 结束对话
    await agent.end_conversation(test_user_id)
    await agent.cleanup()

if __name__ == "__main__":
    print("=" * 60)
    print("🤖 AgentInterface 测试程序")
    print("=" * 60)
    
    # 运行基本测试
    asyncio.run(test_agent_interface())
    
    # 运行多轮对话测试
    asyncio.run(test_multiple_messages())
    
    print("\n" + "=" * 60)
    print("🎉 所有测试完成！")
    print("=" * 60)
