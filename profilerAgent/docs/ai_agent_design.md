# AI Agent 设计文档

## 概述

本文档描述了约会应用AI Agent的核心设计原则、对话策略和技术实现方案。AI Agent负责通过自然对话收集用户信息，构建完整的用户档案，并为后续的匹配服务提供基础数据。

## 设计原则

### 1. 用户分类
- **registration_incomplete** - 注册未完成（首次通话未完成）
- **registered_user** - 已注册用户（首次通话已完成）

### 2. 信息收集策略
- **基本信息（硬性要求）**：姓名、年龄、城市、职业 - 必须100%收集
- **扩展信息（软性收集）**：兴趣、性格、关系目标等 - 需达到70-80%完成度
- **收集失败处理**：明确告知用户无法完成注册流程

### 3. 对话连续性
- **断点续传**：支持通话中断后无缝继续
- **状态持久化**：记录对话进度和收集状态
- **无缝衔接**：不解释中断原因，直接继续上次话题

## 核心功能设计

### 新用户注册流程

#### 注册完成标准
```json
{
  "required_info": {
    "name": true,      // 必须100%
    "age": true,       // 必须100%  
    "city": true,      // 必须100%
    "occupation": true // 必须100%
  },
  "optional_info_completion": 0.75,  // 必须达到75%
  "optional_info": {
    "interests": true,           // 兴趣爱好
    "personality_traits": true,  // 性格特征
    "relationship_goals": false, // 关系目标
    "lifestyle": true,           // 生活方式
    "values": false              // 价值观
  }
}
```

#### 信息收集优先级
1. **P1 (前5分钟)**：基本信息收集
   - 姓名、年龄、城市、职业
   - 硬性要求，必须完成

2. **P2 (中5分钟)**：核心扩展信息
   - 主要兴趣爱好
   - 基本性格特征
   - 生活方式

3. **P3 (后5分钟)**：深度信息
   - 关系期望和偏好
   - 价值观和原则
   - 未来规划

#### 强制收集策略
```
基本信息收集失败处理：
1. 尝试多种问法（最多5次不同方式）
2. 如果仍然失败，明确告知：
   "很抱歉，为了帮你找到合适的匹配，我需要了解你的基本信息。
   如果你不愿意分享这些信息，我无法为你完成注册流程。"
3. 结束通话，状态保持 registration_incomplete
```

### 老用户对话策略

#### 记忆引用机制
- **直接引用**：明确说"我记得你上次说..."
- **自然衔接**：基于用户输入选择相关历史话题
- **信息更新**：发现冲突时主动确认变化

#### 对话跟随策略
- 以用户主题为主导
- 不强制收集新信息
- 自然更新已有信息
- 保持对话的连贯性和趣味性

## 技术架构

### 数据结构设计

#### 用户状态跟踪
```json
{
  "user_id": "user_123",
  "registration_status": "registration_incomplete",
  "call_progress": {
    "required_info": {
      "name": {"collected": true, "value": "张三", "attempts": 1},
      "age": {"collected": false, "attempts": 2, "last_question": "你今年多大？"},
      "city": {"collected": true, "value": "北京", "attempts": 1},
      "occupation": {"collected": false, "attempts": 3, "last_question": "你做什么工作？"}
    },
    "optional_info": {
      "interests": {"collected": true, "completion": 0.8},
      "personality": {"collected": false, "completion": 0.0}
    },
    "conversation_context": {
      "last_topic": "询问职业",
      "resistant_topics": ["工作"],
      "successful_topics": ["基本信息", "兴趣爱好"]
    }
  },
  "call_history": [
    {
      "date": "2024-01-15",
      "duration": 8,
      "reason": "user_hangup",
      "progress": "60%"
    }
  ]
}
```

#### 记忆管理系统
```json
{
  "short_term_memory": [
    {"role": "user", "content": "我是程序员"},
    {"role": "assistant", "content": "程序员很有挑战性！"}
  ],
  "user_profile": {
    "basic_info": {"name": "张三", "age": 28, "city": "北京", "occupation": "程序员"},
    "personality": {"traits": ["内向", "理性"], "mbti": "INTJ"},
    "interests": {"hobbies": ["编程", "阅读"], "sports": ["羽毛球"]},
    "relationship": {"looking_for": "长期关系", "preferences": ["有共同兴趣"]}
  },
  "important_topics": [
    {"topic": "career_change", "last_mentioned": "2024-01-15", "context": "考虑换工作"}
  ]
}
```

### 核心算法

#### 完成度计算
```python
def calculate_completion_rate(user_data):
    # 基本信息完成度
    required_fields = ['name', 'age', 'city', 'occupation']
    required_complete = all(user_data.get(field) for field in required_fields)
    
    # 可选信息完成度
    optional_fields = ['interests', 'personality', 'relationship_goals', 'lifestyle', 'values']
    optional_complete = sum(1 for field in optional_fields if user_data.get(field))
    optional_rate = optional_complete / len(optional_fields)
    
    return required_complete, optional_rate

def is_registration_complete(user_data):
    required_complete, optional_rate = calculate_completion_rate(user_data)
    return required_complete and optional_rate >= 0.75
```

#### 断点续传逻辑
```python
def resume_conversation(user_id):
    state = get_user_state(user_id)
    if state.status == "registration_incomplete":
        # 找到下一个需要收集的信息
        next_required = find_next_required_info(state)
        if next_required:
            return continue_required_collection(next_required)
        else:
            return continue_optional_collection(state)
    else:
        return start_regular_conversation(user_id)
```

## 对话示例

### 新用户注册对话
```
AI: "你好！我是你的约会档案助手。为了帮你找到合适的人，我需要了解你一些基本信息。你叫什么名字？"
用户: "张三"
AI: "你好张三！你今年多大了？"
用户: "28"
AI: "你现在在哪个城市生活？"
用户: "北京"
AI: "你在北京做什么工作？"
用户: "程序员"
AI: "程序员很有挑战性！你主要做什么方向的开发？"
```

### 断点续传示例
```
// 第一次通话中断
AI: "你在北京做什么工作？"
用户: [挂断]

// 第二次通话续传
AI: "你在北京做什么工作？"
用户: "程序员"
AI: "程序员很有挑战性！你主要做什么方向的开发？"
```

### 老用户对话示例
```
AI: "嗨张三！上次你提到在考虑换工作，最近有什么进展吗？"
用户: "还在看机会，不过发现互联网加班太严重了"
AI: "确实，工作生活平衡很重要。你现在还是倾向于留在现在的公司吗？"
```

### 信息冲突处理
```
// 用户档案：职业=程序员
用户: "我最近换工作了，现在做产品经理"
AI: "咦？你之前不是做程序员吗？什么时候转做产品经理的？"
用户: "上个月刚转的"
AI: "恭喜你！从程序员转产品经理是很好的发展。适应得怎么样？"
```

## 对话结束条件

### 新用户注册完成
- 基本信息100%收集完成
- 可选信息达到75%完成度
- 确认信息："好的，我对你已经有了比较全面的了解。"

### 其他结束条件
- 用户明确表示不想继续聊天
- 通话时间超过20分钟（严重超时）
- 基本信息收集失败，无法完成注册

### 注册失败处理
```
AI: "很抱歉，为了帮你找到合适的匹配，我需要了解你的基本信息。如果你不愿意分享这些信息，我无法为你完成注册流程。你可以考虑一下，准备好了再联系我。"
```

## 实现要点

### 状态管理
- 实时保存对话进度
- 支持多次中断和恢复
- 记录用户阻力话题
- 跟踪信息收集完成度

### 自然对话
- 避免机械式问答
- 根据用户回答灵活调整
- 适时切换话题
- 保持对话的趣味性

### 记忆系统
- 短期记忆：当前对话上下文
- 长期记忆：用户档案信息
- 话题记忆：重要对话片段
- 智能引用：自然提及历史信息

### 错误处理
- 网络中断恢复
- 用户挂断处理
- 信息冲突解决
- 收集失败应对

## 总结

本AI Agent设计以用户体验为核心，通过自然对话完成信息收集任务。设计强调了注册流程的完整性和对话的连续性，确保每个用户都能获得高质量的档案构建服务，为后续的匹配功能奠定坚实基础。
