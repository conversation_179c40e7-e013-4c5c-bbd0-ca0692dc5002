<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0.0000019073486328125 473.3849792480469 1934.038330078125" style="max-width: 473.3849792480469px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14"><style>#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14 .error-icon{fill:#a44141;}#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14 .error-text{fill:#ddd;stroke:#ddd;}#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14 .edge-thickness-normal{stroke-width:1px;}#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14 .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14 .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14 .marker.cross{stroke:lightgrey;}#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14 p{margin:0;}#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14 .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14 .cluster-label text{fill:#F9FFFE;}#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14 .cluster-label span{color:#F9FFFE;}#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14 .cluster-label span p{background-color:transparent;}#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14 .label text,#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14 span{fill:#ccc;color:#ccc;}#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14 .node rect,#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14 .node circle,#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14 .node ellipse,#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14 .node polygon,#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14 .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14 .rough-node .label text,#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14 .node .label text,#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14 .image-shape .label,#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14 .icon-shape .label{text-anchor:middle;}#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14 .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14 .rough-node .label,#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14 .node .label,#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14 .image-shape .label,#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14 .icon-shape .label{text-align:center;}#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14 .node.clickable{cursor:pointer;}#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14 .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14 .arrowheadPath{fill:lightgrey;}#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14 .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14 .flowchart-link{stroke:lightgrey;fill:none;}#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14 .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14 .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14 .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14 .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14 .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14 .cluster text{fill:#F9FFFE;}#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14 .cluster span{color:#F9FFFE;}#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14 .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14 rect.text{fill:none;stroke-width:0;}#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14 .icon-shape,#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14 .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14 .icon-shape p,#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14 .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14 .icon-shape rect,#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14 .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"></g><g class="edgePaths"><path marker-end="url(#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_B_0" d="M236.692,62.002L236.692,66.168C236.692,70.335,236.692,78.668,236.692,86.335C236.692,94.002,236.692,101.002,236.692,104.502L236.692,108.002"></path><path marker-end="url(#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_C_1" d="M236.692,190.005L236.692,194.172C236.692,198.339,236.692,206.672,236.692,214.339C236.692,222.005,236.692,229.005,236.692,232.505L236.692,236.005"></path><path marker-end="url(#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_D_2" d="M236.692,294.007L236.692,298.174C236.692,302.34,236.692,310.674,236.692,318.34C236.692,326.007,236.692,333.007,236.692,336.507L236.692,340.007"></path><path marker-end="url(#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D_E_3" d="M236.692,422.01L236.692,426.177C236.692,430.344,236.692,438.677,236.692,446.344C236.692,454.01,236.692,461.01,236.692,464.51L236.692,468.01"></path><path marker-end="url(#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E_F_4" d="M236.692,526.012L236.692,530.179C236.692,534.345,236.692,542.679,236.692,550.345C236.692,558.012,236.692,565.012,236.692,568.512L236.692,572.012"></path><path marker-end="url(#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F_G_5" d="M236.692,654.016L236.692,658.182C236.692,662.349,236.692,670.682,236.692,678.349C236.692,686.016,236.692,693.016,236.692,696.516L236.692,700.016"></path><path marker-end="url(#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G_H_6" d="M236.692,758.017L236.692,762.184C236.692,766.351,236.692,774.684,236.692,782.351C236.692,790.017,236.692,797.017,236.692,800.517L236.692,804.017"></path><path marker-end="url(#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_H_I_7" d="M236.692,862.019L236.692,866.186C236.692,870.352,236.692,878.686,236.692,886.352C236.692,894.019,236.692,901.019,236.692,904.519L236.692,908.019"></path><path marker-end="url(#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_I_J_8" d="M236.692,966.021L236.692,970.187C236.692,974.354,236.692,982.687,236.692,990.354C236.692,998.021,236.692,1005.021,236.692,1008.521L236.692,1012.021"></path><path marker-end="url(#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_J_K_9" d="M236.692,1094.024L236.692,1098.191C236.692,1102.358,236.692,1110.691,236.692,1118.358C236.692,1126.024,236.692,1133.024,236.692,1136.524L236.692,1140.024"></path><path marker-end="url(#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_K_L_10" d="M236.692,1198.026L236.692,1202.193C236.692,1206.359,236.692,1214.693,236.692,1222.359C236.692,1230.026,236.692,1237.026,236.692,1240.526L236.692,1244.026"></path><path marker-end="url(#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_L_M_11" d="M236.692,1302.028L236.692,1306.194C236.692,1310.361,236.692,1318.694,236.692,1326.361C236.692,1334.028,236.692,1341.028,236.692,1344.528L236.692,1348.028"></path><path marker-end="url(#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_M_N_12" d="M236.692,1406.03L236.692,1410.196C236.692,1414.363,236.692,1422.696,236.692,1430.363C236.692,1438.03,236.692,1445.03,236.692,1448.53L236.692,1452.03"></path><path marker-end="url(#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_N_O_13" d="M236.692,1510.031L236.692,1514.198C236.692,1518.365,236.692,1526.698,236.692,1534.365C236.692,1542.031,236.692,1549.031,236.692,1552.531L236.692,1556.031"></path><path marker-end="url(#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_O_P_14" d="M236.692,1614.033L236.692,1618.2C236.692,1622.366,236.692,1630.7,236.692,1638.366C236.692,1646.033,236.692,1653.033,236.692,1656.533L236.692,1660.033"></path><path marker-end="url(#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_P_Q_15" d="M236.692,1718.035L236.692,1722.201C236.692,1726.368,236.692,1734.701,236.692,1742.368C236.692,1750.035,236.692,1757.035,236.692,1760.535L236.692,1764.035"></path><path marker-end="url(#mermaid-0a7e8dcd-8cf1-4cd5-888e-b3ae51398c14_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Q_R_16" d="M236.692,1822.036L236.692,1826.203C236.692,1830.37,236.692,1838.703,236.692,1846.37C236.692,1854.036,236.692,1861.036,236.692,1864.536L236.692,1868.036"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(236.69248962402344, 35.00086784362793)" id="flowchart-A-111" class="node default"><rect height="54.00173568725586" width="124.00824737548828" y="-27.00086784362793" x="-62.00412368774414" style="" class="basic label-container"></rect><g transform="translate(-32.00412368774414, -12.00086784362793)" style="" class="label"><rect></rect><foreignObject height="24.00173568725586" width="64.00824737548828"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>用户拨打</p></span></div></foreignObject></g></g><g transform="translate(236.69248962402344, 151.00347137451172)" id="flowchart-B-112" class="node default"><rect height="78.00347137451172" width="260" y="-39.00173568725586" x="-130" style="" class="basic label-container"></rect><g transform="translate(-100, -24.00173568725586)" style="" class="label"><rect></rect><foreignObject height="48.00347137451172" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>API: /voice/initiate/user_id</p></span></div></foreignObject></g></g><g transform="translate(236.69248962402344, 267.0060749053955)" id="flowchart-C-114" class="node default"><rect height="54.00173568725586" width="242.13975524902344" y="-27.00086784362793" x="-121.06987762451172" style="" class="basic label-container"></rect><g transform="translate(-91.06987762451172, -12.00086784362793)" style="" class="label"><rect></rect><foreignObject height="24.00173568725586" width="182.13975524902344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>VoiceService.initiate_call</p></span></div></foreignObject></g></g><g transform="translate(236.69248962402344, 383.0086784362793)" id="flowchart-D-116" class="node default"><rect height="78.00347137451172" width="260" y="-39.00173568725586" x="-130" style="fill:#e8f5e8 !important" class="basic label-container"></rect><g transform="translate(-100, -24.00173568725586)" style="" class="label"><rect></rect><foreignObject height="48.00347137451172" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>创建CallSession + 数据库记录</p></span></div></foreignObject></g></g><g transform="translate(236.69248962402344, 499.0112819671631)" id="flowchart-E-118" class="node default"><rect height="54.00173568725586" width="212.74522399902344" y="-27.00086784362793" x="-106.37261199951172" style="" class="basic label-container"></rect><g transform="translate(-76.37261199951172, -12.00086784362793)" style="" class="label"><rect></rect><foreignObject height="24.00173568725586" width="152.74522399902344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Twilio发起通话: CA...</p></span></div></foreignObject></g></g><g transform="translate(236.69248962402344, 615.0138854980469)" id="flowchart-F-120" class="node default"><rect height="78.00347137451172" width="260" y="-39.00173568725586" x="-130" style="" class="basic label-container"></rect><g transform="translate(-100, -24.00173568725586)" style="" class="label"><rect></rect><foreignObject height="48.00347137451172" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ConversationRelay WebSocket</p></span></div></foreignObject></g></g><g transform="translate(236.69248962402344, 731.0164890289307)" id="flowchart-G-122" class="node default"><rect height="54.00173568725586" width="210.06509399414062" y="-27.00086784362793" x="-105.03254699707031" style="" class="basic label-container"></rect><g transform="translate(-75.03254699707031, -12.00086784362793)" style="" class="label"><rect></rect><foreignObject height="24.00173568725586" width="150.06509399414062"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Setup Message: VX...</p></span></div></foreignObject></g></g><g transform="translate(236.69248962402344, 835.0182247161865)" id="flowchart-H-124" class="node default"><rect height="54.00173568725586" width="214.38368225097656" y="-27.00086784362793" x="-107.19184112548828" style="" class="basic label-container"></rect><g transform="translate(-77.19184112548828, -12.00086784362793)" style="" class="label"><rect></rect><foreignObject height="24.00173568725586" width="154.38368225097656"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>find_user_by_call_sid</p></span></div></foreignObject></g></g><g transform="translate(236.69248962402344, 939.0199604034424)" id="flowchart-I-126" class="node default"><rect height="54.00173568725586" width="457.3849792480469" y="-27.00086784362793" x="-228.69248962402344" style="" class="basic label-container"></rect><g transform="translate(-198.69248962402344, -12.00086784362793)" style="" class="label"><rect></rect><foreignObject height="24.00173568725586" width="397.3849792480469"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>voice_service.conversation_agent.get_user_by_call_sid</p></span></div></foreignObject></g></g><g transform="translate(236.69248962402344, 1055.0225639343262)" id="flowchart-J-128" class="node default"><rect height="78.00347137451172" width="260" y="-39.00173568725586" x="-130" style="fill:#e8f5e8 !important" class="basic label-container"></rect><g transform="translate(-100, -24.00173568725586)" style="" class="label"><rect></rect><foreignObject height="48.00347137451172" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>数据库查询: call_sid -&gt; user_id</p></span></div></foreignObject></g></g><g transform="translate(236.69248962402344, 1171.02516746521)" id="flowchart-K-130" class="node default"><rect height="54.00173568725586" width="145.49261474609375" y="-27.00086784362793" x="-72.74630737304688" style="fill:#e8f5e8 !important" class="basic label-container"></rect><g transform="translate(-42.746307373046875, -12.00086784362793)" style="" class="label"><rect></rect><foreignObject height="24.00173568725586" width="85.49261474609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>✅ 找到用户</p></span></div></foreignObject></g></g><g transform="translate(236.69248962402344, 1275.0269031524658)" id="flowchart-L-132" class="node default"><rect height="54.00173568725586" width="241.13063049316406" y="-27.00086784362793" x="-120.56531524658203" style="" class="basic label-container"></rect><g transform="translate(-90.56531524658203, -12.00086784362793)" style="" class="label"><rect></rect><foreignObject height="24.00173568725586" width="181.13063049316406"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>创建ConversationContext</p></span></div></foreignObject></g></g><g transform="translate(236.69248962402344, 1379.0286388397217)" id="flowchart-M-134" class="node default"><rect height="54.00173568725586" width="124.00824737548828" y="-27.00086784362793" x="-62.00412368774414" style="" class="basic label-container"></rect><g transform="translate(-32.00412368774414, -12.00086784362793)" style="" class="label"><rect></rect><foreignObject height="24.00173568725586" width="64.00824737548828"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>用户输入</p></span></div></foreignObject></g></g><g transform="translate(236.69248962402344, 1483.0303745269775)" id="flowchart-N-136" class="node default"><rect height="54.00173568725586" width="258.5568542480469" y="-27.00086784362793" x="-129.27842712402344" style="" class="basic label-container"></rect><g transform="translate(-99.27842712402344, -12.00086784362793)" style="" class="label"><rect></rect><foreignObject height="24.00173568725586" width="198.55685424804688"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>process_conversation_input</p></span></div></foreignObject></g></g><g transform="translate(236.69248962402344, 1587.0321102142334)" id="flowchart-O-138" class="node default"><rect height="54.00173568725586" width="361.2586669921875" y="-27.00086784362793" x="-180.62933349609375" style="" class="basic label-container"></rect><g transform="translate(-150.62933349609375, -12.00086784362793)" style="" class="label"><rect></rect><foreignObject height="24.00173568725586" width="301.2586669921875"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>voice_service.process_voice_conversation</p></span></div></foreignObject></g></g><g transform="translate(236.69248962402344, 1691.0338459014893)" id="flowchart-P-140" class="node default"><rect height="54.00173568725586" width="304.6831512451172" y="-27.00086784362793" x="-152.3415756225586" style="fill:#e8f5e8 !important" class="basic label-container"></rect><g transform="translate(-122.3415756225586, -12.00086784362793)" style="" class="label"><rect></rect><foreignObject height="24.00173568725586" width="244.6831512451172"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>conversation_agent.process_input</p></span></div></foreignObject></g></g><g transform="translate(236.69248962402344, 1795.0355815887451)" id="flowchart-Q-142" class="node default"><rect height="54.00173568725586" width="137.89713287353516" y="-27.00086784362793" x="-68.94856643676758" style="fill:#e8f5e8 !important" class="basic label-container"></rect><g transform="translate(-38.94856643676758, -12.00086784362793)" style="" class="label"><rect></rect><foreignObject height="24.00173568725586" width="77.89713287353516"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>AI生成回复</p></span></div></foreignObject></g></g><g transform="translate(236.69248962402344, 1899.037317276001)" id="flowchart-R-144" class="node default"><rect height="54.00173568725586" width="140.00216674804688" y="-27.00086784362793" x="-70.00108337402344" style="" class="basic label-container"></rect><g transform="translate(-40.00108337402344, -12.00086784362793)" style="" class="label"><rect></rect><foreignObject height="24.00173568725586" width="80.00216674804688"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>返回给用户</p></span></div></foreignObject></g></g></g></g></g></svg>