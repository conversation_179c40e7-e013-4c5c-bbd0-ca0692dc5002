# pgAdmin 配置指南

## 访问pgAdmin

1. 打开浏览器访问：http://localhost:8080
2. 使用以下凭据登录：
   - **Email**: <EMAIL>
   - **Password**: admin123

## 添加AI Agent数据库服务器

### 步骤1：添加新服务器
1. 在pgAdmin界面中，右键点击左侧的"Servers"
2. 选择 "Register" > "Server..."

### 步骤2：配置服务器连接

#### General标签页
- **Name**: AI Agent Database
- **Server group**: Servers (默认)
- **Comments**: AI Agent PostgreSQL Database

#### Connection标签页
- **Host name/address**: `postgres` (Docker容器名称，不是localhost!)
- **Port**: `5432`
- **Maintenance database**: `ai_agent_db`
- **Username**: `ai_agent_user`
- **Password**: `ai_agent_password`
- **Save password**: ✅ (勾选)

#### Advanced标签页 (可选)
- **DB restriction**: `ai_agent_db` (限制只显示我们的数据库)

### 步骤3：保存并连接
1. 点击"Save"按钮
2. 如果配置正确，左侧会显示"AI Agent Database"服务器
3. 展开服务器可以看到数据库和表

## 常见问题解决

### 问题1：连接被拒绝
**错误**: connection to server at "localhost", port 5432 failed
**解决**: 确保Host name/address使用`postgres`而不是`localhost`

### 问题2：认证失败
**错误**: FATAL: password authentication failed
**解决**: 检查用户名和密码是否正确：
- Username: `ai_agent_user`
- Password: `ai_agent_password`

### 问题3：数据库不存在
**错误**: database "ai_agent_db" does not exist
**解决**: 确保数据库重建脚本已成功运行

## 验证连接

连接成功后，你应该能看到以下8个表：
1. `users` - 用户基础信息
2. `user_profiles` - 用户画像
3. `conversations` - 对话会话
4. `conversation_messages` - 对话消息
5. `user_context_cache` - 用户上下文缓存
6. `voice_sessions` - 语音会话
7. `user_matches` - 用户匹配
8. `system_config` - 系统配置

## 快速测试查询

连接成功后，可以在Query Tool中运行以下查询测试：

```sql
-- 查看所有表
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
ORDER BY table_name;

-- 查看系统配置
SELECT key, description FROM system_config;

-- 查看用户表结构
\d users;

-- 查看用户画像表结构
\d user_profiles;
```

## Docker网络说明

pgAdmin和PostgreSQL都运行在同一个Docker网络(`ai_agent_network`)中，所以：
- ✅ 使用容器名称: `postgres`
- ❌ 不要使用: `localhost` 或 `127.0.0.1`

这是Docker容器间通信的标准方式。
