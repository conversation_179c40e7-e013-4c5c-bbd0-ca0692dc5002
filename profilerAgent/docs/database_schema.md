# AI Agent Database Schema Documentation

## Overview
This document describes the new simplified database schema for the AI Agent application. The new architecture focuses on efficient user profiling, conversation management, and intelligent matching while removing over-engineered analytics tables.

## Design Principles
1. **Simplicity First** - Removed complex analytics tables, focus on core functionality
2. **AI Agent Friendly** - Support for incremental user profile updates and conversation context
3. **Performance Optimized** - Proper indexing and JSONB usage for flexible data structures
4. **Scalable** - Flexible JSON structures support future feature expansion

## Database Configuration
- **Database Type**: PostgreSQL 15+
- **Database Name**: ai_agent_db
- **Username**: ai_agent_user
- **Character Encoding**: UTF-8
- **Timezone**: UTC
- **Port**: 5432

## Core Tables Summary
- **users** - Basic user information and verification status
- **user_profiles** - AI-managed user profiles with confidence scores
- **conversations** - Conversation sessions (voice, chat, SMS)
- **conversation_messages** - Individual messages within conversations
- **user_context_cache** - Short-term memory cache for active conversations
- **voice_sessions** - Voice call session tracking
- **user_matches** - Simplified matching system
- **system_config** - Application configuration
---

## Table Definitions

### 1. users
**Purpose**: Basic user information and verification status

```sql
CREATE TABLE users (
    user_id VARCHAR(50) PRIMARY KEY,
    phone_number VARCHAR(20) UNIQUE,
    email VARCHAR(255) UNIQUE,
    name VARCHAR(100),
    status VARCHAR(20) DEFAULT 'active',
    sms_verified BOOLEAN DEFAULT FALSE,
    voice_call_completed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**Key Features:**
- String-based user_id for flexibility
- Unique constraints on phone_number and email
- Verification tracking for SMS and voice calls
- Automatic timestamp management with triggers

**Status Values:**
- `active` - Normal active user
- `inactive` - Temporarily disabled
- `banned` - Permanently banned
- `pending` - Awaiting verification

### 2. user_profiles
**Purpose**: AI-managed user profiles with structured data and confidence scores

```sql
CREATE TABLE user_profiles (
    user_id VARCHAR(50) PRIMARY KEY REFERENCES users(user_id) ON DELETE CASCADE,
    profile_data JSONB NOT NULL DEFAULT '{}',
    confidence_scores JSONB DEFAULT '{}',
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    conversation_count INTEGER DEFAULT 0,
    total_interactions INTEGER DEFAULT 0
);
```

**Key Features:**
- JSONB fields for flexible, structured user data
- Confidence scores for each profile field
- Automatic interaction tracking
- Optimized for AI Agent incremental updates

**Profile Data Structure Example:**
```json
{
  "basic_info": {
    "name": "张三",
    "age": 28,
    "city": "北京",
    "occupation": "软件工程师"
  },
  "personality": {
    "mbti": "INTJ",
    "traits": ["内向", "理性", "计划性强"],
    "social_energy": "introvert"
  },
  "interests": {
    "hobbies": ["编程", "阅读", "爬山"],
    "music": ["古典", "爵士"],
    "sports": ["羽毛球", "游泳"]
  },
  "relationship": {
    "looking_for": "长期关系",
    "preferences": ["有共同兴趣", "性格互补"],
    "deal_breakers": ["吸烟", "不爱运动"]
  }
}
```

**Confidence Scores Example:**
```json
{
  "basic_info.occupation": 0.95,
  "personality.mbti": 0.8,
  "interests.hobbies": 0.9,
  "relationship.looking_for": 0.85
}
```

### 3. conversations
**Purpose**: Conversation sessions across different channels (voice, chat, SMS)

```sql
CREATE TABLE conversations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id VARCHAR(50) NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    type VARCHAR(20) NOT NULL,
    status VARCHAR(20) DEFAULT 'active',
    summary TEXT,
    context_data JSONB DEFAULT '{}',
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP
);
```

**Key Features:**
- UUID primary keys for global uniqueness
- Support for multiple conversation types
- AI-generated conversation summaries
- Flexible context data storage

**Type Values:**
- `voice` - Voice call conversations
- `chat` - Text chat conversations
- `sms` - SMS conversations

**Status Values:**
- `active` - Ongoing conversation
- `completed` - Successfully completed
- `abandoned` - User left without completion

### 4. conversation_messages
**Purpose**: Individual messages within conversations

```sql
CREATE TABLE conversation_messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    conversation_id UUID NOT NULL REFERENCES conversations(id) ON DELETE CASCADE,
    role VARCHAR(20) NOT NULL,
    content TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**Key Features:**
- Links to parent conversation
- Role-based message tracking
- Metadata for additional information (confidence, tool calls, etc.)
- Optimized for AI Agent context building

**Role Values:**
- `user` - User messages
- `assistant` - AI Agent responses
- `system` - System messages and notifications

### 5. user_context_cache
**Purpose**: Short-term memory cache for active conversations (Redis backup)

```sql
CREATE TABLE user_context_cache (
    user_id VARCHAR(50) PRIMARY KEY REFERENCES users(user_id) ON DELETE CASCADE,
    short_term_memory JSONB DEFAULT '[]',
    conversation_summary TEXT,
    active_conversation_id UUID REFERENCES conversations(id),
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**Key Features:**
- Database backup for Redis cache
- Recent conversation context storage
- Automatic cleanup of old data (7 days)
- Optimized for AI Agent context retrieval

### 6. voice_sessions
**Purpose**: Voice call session tracking and Twilio integration

```sql
CREATE TABLE voice_sessions (
    twilio_call_sid VARCHAR(100) PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL REFERENCES users(user_id),
    conversation_id UUID REFERENCES conversations(id),
    session_status VARCHAR(20) DEFAULT 'initiated',
    phone_number VARCHAR(20),
    call_duration INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP
);
```

**Key Features:**
- Twilio Call SID as primary key
- Links to conversation system
- Call duration tracking
- Session status management

**Session Status Values:**
- `initiated` - Call started
- `in_progress` - Active conversation
- `completed` - Successfully completed
- `failed` - Call failed or dropped

### 7. user_matches
**Purpose**: Simplified user matching system

```sql
CREATE TABLE user_matches (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id VARCHAR(50) NOT NULL REFERENCES users(user_id),
    matched_user_id VARCHAR(50) NOT NULL REFERENCES users(user_id),
    match_score DECIMAL(3,2),
    match_reasons JSONB DEFAULT '[]',
    status VARCHAR(20) DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    UNIQUE(user_id, matched_user_id),
    CHECK (user_id != matched_user_id)
);
```

**Key Features:**
- Simplified matching without complex analytics
- Match score and reasoning storage
- Prevents duplicate and self-matches
- Status tracking for match progression

**Status Values:**
- `pending` - Match suggested, awaiting response
- `accepted` - Both users accepted
- `rejected` - One or both users rejected
- `expired` - Match suggestion expired

**Match Reasons Example:**
```json
[
  "Similar MBTI personality (INTJ)",
  "Shared interest in programming",
  "Compatible age range",
  "Same city location"
]
```

### 8. system_config
**Purpose**: Application configuration and AI Agent parameters

```sql
CREATE TABLE system_config (
    key VARCHAR(100) PRIMARY KEY,
    value JSONB NOT NULL,
    description TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**Key Features:**
- Flexible JSON configuration values
- Runtime configuration updates
- AI Agent parameter tuning
- Feature flag management

**Default Configuration Examples:**
```json
{
  "ai_agent_config": {
    "max_short_term_memory": 10,
    "summary_threshold": 20,
    "confidence_threshold": 0.7
  },
  "matching_config": {
    "min_score": 0.6,
    "max_matches_per_day": 10
  },
  "conversation_config": {
    "max_message_length": 2000,
    "auto_summary_enabled": true
  }
}
```

## Indexes and Performance

### Key Indexes
```sql
-- User lookups
CREATE INDEX idx_users_phone_number ON users(phone_number);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_status ON users(status);

-- Conversation queries
CREATE INDEX idx_conversations_user_id ON conversations(user_id);
CREATE INDEX idx_conversations_type ON conversations(type);
CREATE INDEX idx_conversations_status ON conversations(status);
CREATE INDEX idx_conversations_started_at ON conversations(started_at);

-- Message queries
CREATE INDEX idx_messages_conversation_id ON conversation_messages(conversation_id);
CREATE INDEX idx_messages_timestamp ON conversation_messages(timestamp);
CREATE INDEX idx_messages_role ON conversation_messages(role);

-- Voice session lookups
CREATE INDEX idx_voice_sessions_user_id ON voice_sessions(user_id);
CREATE INDEX idx_voice_sessions_status ON voice_sessions(session_status);

-- Matching queries
CREATE INDEX idx_matches_user_id ON user_matches(user_id);
CREATE INDEX idx_matches_status ON user_matches(status);
CREATE INDEX idx_matches_score ON user_matches(match_score DESC);

-- JSONB indexes for profile data
CREATE INDEX idx_user_profiles_basic_info ON user_profiles USING GIN ((profile_data->'basic_info'));
CREATE INDEX idx_user_profiles_personality ON user_profiles USING GIN ((profile_data->'personality'));
```

## Redis Cache Configuration

### Database Partitioning
- **DB 0**: User short-term memory cache (`user_context:user_id`)
- **DB 1**: Conversation session state (`conversation:conversation_id`)
- **DB 2**: AI model response cache (`ai_cache:hash`)
- **DB 3**: User profile cache (`profile:user_id`)
- **DB 4**: Matching algorithm cache (`matches:user_id`)

### Cache Expiration Strategy
- **Short-term memory**: 1 hour
- **Conversation state**: 24 hours
- **AI response cache**: 7 days
- **User profile cache**: 1 hour
- **Match cache**: 30 minutes

## Migration from Old Schema

### Removed Tables
The following complex analytics tables have been removed:
- `agent_decisions` - Over-engineered decision tracking
- `agent_goals` - Complex goal management
- `agent_memory` - Redundant memory system
- `agent_metrics` - Detailed performance analytics
- `agent_strategies` - Strategy management system
- `agent_conversations` - Duplicate conversation tracking
- `agent_learning` - Machine learning analytics
- `profile_cards` - Individual personality cards
- `linkedin_profiles` - LinkedIn integration
- `user_preferences` - Complex preference system
- `match_recommendations` - Batch recommendation tracking
- `user_chat_messages` - Separate chat system

### Data Migration Strategy
1. **User Data**: Migrate basic user information to new `users` table
2. **Profile Data**: Consolidate profile information into JSONB `user_profiles`
3. **Conversations**: Merge chat and voice data into unified `conversations` system
4. **Matches**: Simplify matching data, remove complex analytics
5. **Configuration**: Move settings to `system_config` table

### Benefits of New Schema
1. **Simplified Maintenance** - 8 core tables vs 15+ complex tables
2. **Better Performance** - Optimized indexes and JSONB usage
3. **AI Agent Friendly** - Designed for incremental updates and context management
4. **Flexible Structure** - JSON fields support future feature expansion
5. **Reduced Complexity** - Focus on core functionality, remove over-engineering
## Usage Examples

### Creating a New User
```sql
INSERT INTO users (user_id, phone_number, name)
VALUES ('user_123', '+1234567890', 'John Doe');
```

### Updating User Profile
```sql
UPDATE user_profiles
SET profile_data = profile_data || '{"basic_info": {"occupation": "Software Engineer"}}',
    confidence_scores = confidence_scores || '{"basic_info.occupation": 0.95}',
    conversation_count = conversation_count + 1
WHERE user_id = 'user_123';
```

### Starting a New Conversation
```sql
INSERT INTO conversations (user_id, type, status)
VALUES ('user_123', 'voice', 'active')
RETURNING id;
```

### Adding Messages to Conversation
```sql
INSERT INTO conversation_messages (conversation_id, role, content)
VALUES
  ('conv_uuid', 'user', 'Hello, I am a software engineer'),
  ('conv_uuid', 'assistant', 'That is interesting! What type of software do you work on?');
```

### Caching User Context (Redis equivalent in DB)
```sql
INSERT INTO user_context_cache (user_id, short_term_memory, conversation_summary)
VALUES ('user_123', '[{"role": "user", "content": "Hello"}]', 'User introduced themselves')
ON CONFLICT (user_id) DO UPDATE SET
  short_term_memory = EXCLUDED.short_term_memory,
  conversation_summary = EXCLUDED.conversation_summary,
  updated_at = CURRENT_TIMESTAMP;
```

## Summary

This new database schema represents a significant simplification and optimization for AI Agent applications:

- **8 core tables** instead of 18+ complex tables
- **JSONB-based flexibility** for user profiles and conversation data
- **AI Agent optimized** for incremental updates and context management
- **Performance focused** with proper indexing and caching strategies
- **Maintainable** with clear separation of concerns

The schema supports the core AI Agent workflow: collect user information through conversations, build comprehensive user profiles, and enable intelligent matching - all while maintaining high performance and flexibility for future enhancements.
