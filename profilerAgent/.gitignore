# Environment variables
.env
.env.local
.env.production
.env.staging
.env.backup

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
.venv/
.ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/
log/

# Docker
.dockerignore

# Database
*.db
*.sqlite
*.sqlite3

# Node.js (for frontend)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity

# Frontend build
frontend/dist/
frontend/build/
frontend/.next/
frontend/.nuxt/

# Testing
.coverage
.pytest_cache/
.tox/
htmlcov/
.nyc_output

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# Temporary files
*.tmp
*.temp
temp/
tmp/

# API keys and secrets (backup)
*secret*
*key*
!requirements.txt
!package.json

# Data files
data/
*.csv
*.json
*.xlsx
*.parquet

# Model files
*.pkl
*.joblib
*.h5
*.pt
*.pth
