#!/usr/bin/env python3
"""
测试 Twilio 配置
验证 ConversationRelay 端点是否正确配置
"""

import requests
import os
import json
from datetime import datetime

def test_twiml_endpoint():
    """测试 TwiML 端点"""
    base_url = os.getenv("TWILIO_WEBHOOK_URL", "https://e36ed358d667.ngrok-free.app")
    
    print("🧪 测试 Twilio 配置")
    print("=" * 50)
    
    # 测试 ConversationRelay TwiML 端点
    relay_url = f"{base_url}/conversation-relay/twiml/start"
    print(f"📞 测试 ConversationRelay 端点: {relay_url}")
    
    try:
        response = requests.post(relay_url, timeout=10)
        print(f"✅ 状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ ConversationRelay TwiML 端点正常工作")
            print("📄 TwiML 内容:")
            print(response.text[:300] + "..." if len(response.text) > 300 else response.text)
            
            # 检查是否包含正确的 WebSocket URL
            if "wss://" in response.text and "conversation-relay/voice" in response.text:
                print("✅ WebSocket URL 配置正确")
            else:
                print("❌ WebSocket URL 配置可能有问题")
        else:
            print(f"❌ ConversationRelay 端点返回错误: {response.status_code}")
            print(f"错误内容: {response.text}")
            
    except Exception as e:
        print(f"❌ ConversationRelay 端点测试失败: {e}")
    
    print()
    
    # 测试旧的端点（看是否还存在）
    old_url = f"{base_url}/voice/webhook/incoming"
    print(f"📞 测试旧端点: {old_url}")
    
    try:
        response = requests.post(old_url, timeout=10)
        print(f"⚠️  旧端点状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("⚠️  旧端点仍然工作 - 可能 Twilio 还在调用旧端点")
            print("📄 旧端点 TwiML:")
            print(response.text[:200] + "..." if len(response.text) > 200 else response.text)
        else:
            print("✅ 旧端点已不可用")
            
    except Exception as e:
        print(f"✅ 旧端点不可用: {e}")

def test_websocket_url():
    """测试 WebSocket URL 配置"""
    print("\n🔗 测试 WebSocket 配置")
    print("=" * 50)
    
    base_url = os.getenv("TWILIO_WEBHOOK_URL", "https://e36ed358d667.ngrok-free.app")
    ws_url = base_url.replace("https://", "wss://") + "/conversation-relay/voice"
    
    print(f"WebSocket URL: {ws_url}")
    
    # 这里我们不能直接测试 WebSocket，但可以检查 URL 格式
    if ws_url.startswith("wss://") and "conversation-relay/voice" in ws_url:
        print("✅ WebSocket URL 格式正确")
    else:
        print("❌ WebSocket URL 格式错误")

def check_twilio_webhook_logs():
    """检查 Twilio webhook 调用建议"""
    print("\n📋 Twilio 配置检查清单")
    print("=" * 50)
    
    print("请确认以下配置：")
    print("1. 在 Twilio Console 中:")
    print("   - 进入 Phone Numbers -> Manage -> Active numbers")
    print("   - 选择你的电话号码")
    print("   - 在 Voice Configuration 部分:")
    print(f"     ✅ Request URL: https://e36ed358d667.ngrok-free.app/conversation-relay/twiml/start")
    print("     ✅ Request Method: HTTP POST")
    print("     ✅ 点击 Save 保存配置")
    print()
    print("2. 测试步骤:")
    print("   - 拨打你的 Twilio 号码")
    print("   - 检查服务器日志是否有 '🚀 ConversationRelay TwiML endpoint called!'")
    print("   - 检查 ngrok 日志是否有 POST /conversation-relay/twiml/start")
    print()
    print("3. 如果仍然调用旧端点:")
    print("   - 确认 Twilio Console 中的配置已保存")
    print("   - 等待几分钟让配置生效")
    print("   - 尝试重新拨打")

def simulate_twilio_call():
    """模拟 Twilio 调用"""
    print("\n🎭 模拟 Twilio 调用")
    print("=" * 50)
    
    base_url = os.getenv("TWILIO_WEBHOOK_URL", "https://e36ed358d667.ngrok-free.app")
    relay_url = f"{base_url}/conversation-relay/twiml/start"
    
    # 模拟 Twilio 发送的参数
    twilio_params = {
        "CallSid": "CA1234567890abcdef1234567890abcdef",
        "AccountSid": "AC1234567890abcdef1234567890abcdef", 
        "From": "+***********",
        "To": "+***********",
        "CallStatus": "ringing",
        "ApiVersion": "2010-04-01",
        "Direction": "inbound"
    }
    
    print(f"📞 模拟调用: {relay_url}")
    print(f"📋 参数: {json.dumps(twilio_params, indent=2)}")
    
    try:
        response = requests.post(relay_url, data=twilio_params, timeout=10)
        print(f"✅ 响应状态: {response.status_code}")
        print("📄 TwiML 响应:")
        print(response.text)
        
        if "ConversationRelay" in response.text:
            print("✅ ConversationRelay 配置正确")
        else:
            print("❌ 响应中没有 ConversationRelay 配置")
            
    except Exception as e:
        print(f"❌ 模拟调用失败: {e}")

def main():
    """主函数"""
    print("🔧 Twilio ConversationRelay 配置测试")
    print("=" * 60)
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🌐 TWILIO_WEBHOOK_URL: {os.getenv('TWILIO_WEBHOOK_URL', 'Not set')}")
    print()
    
    # 运行所有测试
    test_twiml_endpoint()
    test_websocket_url()
    simulate_twilio_call()
    check_twilio_webhook_logs()
    
    print("\n" + "=" * 60)
    print("🎯 测试完成！")
    print("如果所有测试都通过，但真实通话仍然不工作，")
    print("请检查 Twilio Console 中的配置是否已正确保存。")

if __name__ == "__main__":
    main()
