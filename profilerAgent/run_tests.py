#!/usr/bin/env python3
"""
Test runner for AI Agent system
"""

import sys
import os
import subprocess
import argparse
from pathlib import Path

def run_tests(test_path=None, verbose=False, coverage=False):
    """运行测试"""
    
    # 确保在正确的目录
    project_root = Path(__file__).parent
    os.chdir(project_root)
    
    # 构建pytest命令
    cmd = ["python", "-m", "pytest"]
    
    if test_path:
        cmd.append(test_path)
    else:
        cmd.append("tests/")
    
    if verbose:
        cmd.append("-v")
    
    if coverage:
        cmd.extend(["--cov=agent", "--cov-report=html", "--cov-report=term"])
    
    # 添加其他有用的选项
    cmd.extend([
        "--tb=short",  # 简短的traceback
        "--strict-markers",  # 严格的marker检查
        "-x"  # 遇到第一个失败就停止
    ])
    
    print(f"Running command: {' '.join(cmd)}")
    print("-" * 50)
    
    try:
        result = subprocess.run(cmd, check=False)
        return result.returncode
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        return 1
    except Exception as e:
        print(f"运行测试时出错: {e}")
        return 1

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="运行AI Agent测试")
    
    parser.add_argument(
        "test_path", 
        nargs="?", 
        help="要运行的测试路径（可选，默认运行所有测试）"
    )
    
    parser.add_argument(
        "-v", "--verbose", 
        action="store_true", 
        help="详细输出"
    )
    
    parser.add_argument(
        "-c", "--coverage", 
        action="store_true", 
        help="生成覆盖率报告"
    )
    
    parser.add_argument(
        "--core", 
        action="store_true", 
        help="只运行core组件测试"
    )
    
    parser.add_argument(
        "--services", 
        action="store_true", 
        help="只运行services组件测试"
    )
    
    args = parser.parse_args()
    
    # 确定测试路径
    test_path = args.test_path
    
    if args.core:
        test_path = "tests/test_core/"
    elif args.services:
        test_path = "tests/test_services/"
    
    # 运行测试
    return_code = run_tests(
        test_path=test_path,
        verbose=args.verbose,
        coverage=args.coverage
    )
    
    if return_code == 0:
        print("\n✅ 所有测试通过！")
    else:
        print(f"\n❌ 测试失败，退出码: {return_code}")
    
    return return_code

if __name__ == "__main__":
    sys.exit(main())
