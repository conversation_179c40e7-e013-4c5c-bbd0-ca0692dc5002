#!/usr/bin/env python3
"""
重置用户语音面试状态脚本
"""

import asyncio
import sys
import os

# 添加项目路径
project_root = os.path.dirname(__file__)
sys.path.insert(0, project_root)

from agent import initialize_agent_system, get_agent_interface, cleanup_agent_system

async def reset_user_voice_status(phone_number: str):
    """重置用户语音状态"""
    try:
        print(f"🔧 Initializing system...")
        await initialize_agent_system()
        agent = get_agent_interface()
        
        print(f"🔍 Looking for user: {phone_number}")
        user = await agent.user_manager.get_user_by_phone(phone_number)
        
        if not user:
            print(f"❌ User not found: {phone_number}")
            return
            
        print(f"✓ Found user: {user.first_name} {user.last_name} (ID: {user.user_id})")
        print(f"   Current voice status: {user.voice_call_completed}")
        
        if not user.voice_call_completed:
            print("✓ Voice status is already false, no need to reset")
            return
            
        # 重置状态
        user.voice_call_completed = False
        user.verification_status = user.verification_status  # 保持当前验证状态
        
        await agent.user_manager.update_user(user)
        print(f"✅ Successfully reset voice status for user: {phone_number}")
        
    except Exception as e:
        print(f"❌ Failed to reset user status: {e}")
    finally:
        await cleanup_agent_system()

async def main():
    if len(sys.argv) != 2:
        print("Usage: python reset_user_voice_status.py <phone_number>")
        print("Example: python reset_user_voice_status.py +15103650664")
        return 1
        
    phone_number = sys.argv[1]
    await reset_user_voice_status(phone_number)
    return 0

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code) 