version: '3.8'

services:
  # PostgreSQL 数据库 - 新的AI Agent架构
  postgres:
    image: postgres:15-alpine
    container_name: ai_agent_postgres
    environment:
      POSTGRES_DB: ai_agent_db
      POSTGRES_USER: ai_agent_user
      POSTGRES_PASSWORD: ai_agent_password
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/database/new_schema.sql:/docker-entrypoint-initdb.d/01-new-schema.sql:ro
    networks:
      - ai_agent_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ai_agent_user -d ai_agent_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis 缓存 - AI Agent专用
  redis:
    image: redis:7-alpine
    container_name: ai_agent_redis
    ports:
      - "6380:6379"
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - ai_agent_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # pgAdmin (可选，用于数据库管理)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: ai_agent_pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "8080:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - ai_agent_network
    restart: unless-stopped
    depends_on:
      - postgres

  # Redis Commander (可选，用于Redis管理)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: ai_agent_redis_commander
    environment:
      REDIS_HOSTS: local:redis:6379
    ports:
      - "8081:8081"
    networks:
      - ai_agent_network
    restart: unless-stopped
    depends_on:
      - redis

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  pgadmin_data:
    driver: local

networks:
  ai_agent_network:
    driver: bridge
