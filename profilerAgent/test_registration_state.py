#!/usr/bin/env python3
"""
测试 RegistrationStateManager
验证注册状态管理功能
"""

import asyncio
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agent.core.registration_state import RegistrationStateManager, RegistrationStatus
from agent.core.database_manager import DatabaseManager

async def test_registration_state_manager():
    """测试 RegistrationStateManager 功能"""
    print("🚀 开始测试 RegistrationStateManager...")
    
    # 创建组件
    db_manager = DatabaseManager()
    await db_manager.connect()
    
    registration_manager = RegistrationStateManager(
        redis_client=None,  # 暂时不使用Redis
        db_manager=db_manager
    )
    
    try:
        test_user_id = "test_reg_user_123"
        test_phone = "+1234567890"
        
        # 测试1: 创建注册
        print("\n1. 测试创建注册...")
        progress = await registration_manager.create_registration(test_user_id, test_phone)
        print(f"   创建结果: {'✅ 成功' if progress else '❌ 失败'}")
        if progress:
            print(f"   用户ID: {progress.user_id}")
            print(f"   状态: {progress.status.value}")
            print(f"   必须信息完成: {progress.calculate_completion_rates()[0]}")
            print(f"   可选信息完成率: {progress.calculate_completion_rates()[1]:.2f}")
        
        # 测试2: 获取注册状态
        print("\n2. 测试获取注册状态...")
        retrieved_progress = await registration_manager.get_registration_state(test_user_id)
        print(f"   获取结果: {'✅ 成功' if retrieved_progress else '❌ 失败'}")
        if retrieved_progress:
            print(f"   状态: {retrieved_progress.status.value}")
            print(f"   下一个必须字段: {retrieved_progress.get_next_required_info()}")
            print(f"   缺失可选字段: {retrieved_progress.get_missing_optional_info()}")
        
        # 测试3: 更新必须信息
        print("\n3. 测试更新必须信息...")
        if retrieved_progress:
            retrieved_progress.update_required_info("name", "张三", "你叫什么名字？")
            retrieved_progress.update_required_info("age", "25", "你多大了？")
            
            await registration_manager._save_state(retrieved_progress)
            print(f"   更新完成")
            print(f"   必须信息完成: {retrieved_progress.calculate_completion_rates()[0]}")
            print(f"   下一个必须字段: {retrieved_progress.get_next_required_info()}")
        
        # 测试4: 更新可选信息
        print("\n4. 测试更新可选信息...")
        if retrieved_progress:
            retrieved_progress.update_optional_info("interests", {"hobbies": ["reading", "coding"]}, 0.8)
            retrieved_progress.update_optional_info("personality", {"traits": ["introverted", "analytical"]}, 0.9)
            
            await registration_manager._save_state(retrieved_progress)
            print(f"   更新完成")
            print(f"   可选信息完成率: {retrieved_progress.calculate_completion_rates()[1]:.2f}")
        
        # 测试5: 检查注册完成状态
        print("\n5. 测试注册完成检查...")
        if retrieved_progress:
            # 完成所有必须信息
            retrieved_progress.update_required_info("city", "北京", "你住在哪里？")
            retrieved_progress.update_required_info("occupation", "程序员", "你的职业是什么？")
            
            # 完成更多可选信息以达到75%
            retrieved_progress.update_optional_info("lifestyle", {"exercise": "regular"}, 1.0)
            retrieved_progress.update_optional_info("values", {"family": "important"}, 1.0)
            
            await registration_manager._save_state(retrieved_progress)
            
            is_complete = retrieved_progress.is_registration_complete()
            print(f"   注册完成: {'✅ 是' if is_complete else '❌ 否'}")
            print(f"   必须信息完成: {retrieved_progress.calculate_completion_rates()[0]}")
            print(f"   可选信息完成率: {retrieved_progress.calculate_completion_rates()[1]:.2f}")
        
        # 测试6: 更新状态
        print("\n6. 测试状态更新...")
        success = await registration_manager.update_status(test_user_id, RegistrationStatus.REGISTERED)
        print(f"   状态更新: {'✅ 成功' if success else '❌ 失败'}")
        
        # 测试7: 获取注册进度
        print("\n7. 测试获取注册进度...")
        progress_info = await registration_manager.get_registration_progress(test_user_id)
        print(f"   进度获取: {'✅ 成功' if progress_info else '❌ 失败'}")
        if progress_info:
            print(f"   状态: {progress_info['status']}")
            print(f"   整体完成: {progress_info['overall_complete']}")
            print(f"   必须信息完成: {progress_info['required_complete']}")
            print(f"   可选信息完成率: {progress_info['optional_completion_rate']:.2f}")
        
        # 测试8: 检查最终注册状态
        print("\n8. 测试最终注册状态检查...")
        is_registered = await registration_manager.is_registration_complete(test_user_id)
        print(f"   最终注册状态: {'✅ 已完成' if is_registered else '❌ 未完成'}")
        
        print("\n✅ RegistrationStateManager 测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理资源
        print("\n🧹 清理资源...")
        await db_manager.disconnect()

async def test_cleanup():
    """测试清理功能"""
    print("\n🧹 测试清理功能...")
    
    db_manager = DatabaseManager()
    await db_manager.connect()
    
    registration_manager = RegistrationStateManager(
        redis_client=None,
        db_manager=db_manager
    )
    
    try:
        # 测试清理过期注册
        cleanup_count = await registration_manager.cleanup_old_registrations()
        print(f"   清理了 {cleanup_count} 个过期注册")
        
    except Exception as e:
        print(f"   清理测试失败: {e}")
    
    finally:
        await db_manager.disconnect()

if __name__ == "__main__":
    print("=" * 60)
    print("🤖 RegistrationStateManager 测试程序")
    print("=" * 60)
    
    # 运行主要测试
    asyncio.run(test_registration_state_manager())
    
    # 运行清理测试
    asyncio.run(test_cleanup())
    
    print("\n" + "=" * 60)
    print("🎉 所有测试完成！")
    print("=" * 60)
