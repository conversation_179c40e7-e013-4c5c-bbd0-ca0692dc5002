2025-08-01 00:01:54,760 - agent.services.conversation_agent - INFO - ⏱️  extract_information_detailed completed in 3.179s
2025-08-01 00:01:54,760 - agent.services.conversation_agent - INFO - ⏱️  extract_information completed in 3.179s
2025-08-01 00:01:54,760 - agent.services.conversation_agent - INFO - Parallel AI processing completed in 3.18s
2025-08-01 00:01:54,760 - agent.services.ai_service - INFO - Generating AI question for domain: relationships
2025-08-01 00:01:56,228 - agent.services.ai_service - INFO - AI question generated: What do you think makes <PERSON><PERSON> such an inspiring character, and how does that influence your art or relationships?
2025-08-01 00:01:56,228 - agent.services.conversation_agent - INFO - ⏱️  ai_decision completed in 1.468s
2025-08-01 00:01:56,228 - agent.services.conversation_agent - INFO - ⏱️  make_smart_decision completed in 1.468s
2025-08-01 00:01:56,228 - agent.services.conversation_agent - INFO - Decision generated in 1.47s, total processing: 4.65s
2025-08-01 00:01:56,228 - agent.services.conversation_agent - INFO - ⏱️  async_optimization completed in 4.648s
2025-08-01 00:01:56,229 - agent.services.conversation_agent - INFO - Agent transitioning stage: relationships -> closing
2025-08-01 00:01:56,229 - agent.services.conversation_agent - INFO - ⏱️  process_input completed in 4.648s
2025-08-01 00:01:56,229 - agent.services.voice_service - INFO - Agent decision: ask_question - What do you think makes Goku such an inspiring character, and how does that influence your art or relationships? (confidence: 0.8)
2025-08-01 00:01:56,229 - agent.services.voice_service - INFO - Generated next question: 'What do you think makes Goku such an inspiring character, and how does that influence your art or relationships?'
2025-08-01 00:01:56,236 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-01 00:01:56,236 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 00:01:56,237 INFO sqlalchemy.engine.Engine 
                        INSERT INTO agent_decisions 
                        (call_sid, decision_type, decision_data, reasoning, confidence)
                        VALUES ($1, $2, $3, $4, $5)
                    
2025-08-01 00:01:56,237 - sqlalchemy.engine.Engine - INFO - 
                        INSERT INTO agent_decisions 
                        (call_sid, decision_type, decision_data, reasoning, confidence)
                        VALUES ($1, $2, $3, $4, $5)
                    
2025-08-01 00:01:56,237 INFO sqlalchemy.engine.Engine [cached since 219.7s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', 'question_generate', '{"action_type": "ask_question", "content": "What do you think makes Goku such an inspiring character, and how does that influence your art or relatio ... (162 characters truncated) ... : 0.7309151599999999, "comfort": 0.77890002, "enthusiasm": 0.6324112299999999}, "collected_info_count": 3, "timestamp": "2025-08-01T00:01:56.232631"}', 'AI generated question based on conversation context', 0.8)
2025-08-01 00:01:56,237 - sqlalchemy.engine.Engine - INFO - [cached since 219.7s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', 'question_generate', '{"action_type": "ask_question", "content": "What do you think makes Goku such an inspiring character, and how does that influence your art or relatio ... (162 characters truncated) ... : 0.7309151599999999, "comfort": 0.77890002, "enthusiasm": 0.6324112299999999}, "collected_info_count": 3, "timestamp": "2025-08-01T00:01:56.232631"}', 'AI generated question based on conversation context', 0.8)
2025-08-01 00:01:56,237 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-01 00:01:56,237 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 00:01:56,238 INFO sqlalchemy.engine.Engine 
                        UPDATE voice_sessions SET
                            session_data = $1,
                            last_activity = NOW(),
                            current_stage = $2,
                            questions_asked = $3,
                            responses_received = $4
                        WHERE twilio_call_sid = $5
                        AND session_status = 'active'
                    
2025-08-01 00:01:56,238 - sqlalchemy.engine.Engine - INFO - 
                        UPDATE voice_sessions SET
                            session_data = $1,
                            last_activity = NOW(),
                            current_stage = $2,
                            questions_asked = $3,
                            responses_received = $4
                        WHERE twilio_call_sid = $5
                        AND session_status = 'active'
                    
2025-08-01 00:01:56,238 INFO sqlalchemy.engine.Engine [cached since 248.7s ago] ('{"call_sid": "CAb546c7046b7ac7ba2397a5fe4e5b68e5", "user_id": "8beb42d4-23b6-4dbf-a3ea-869044eb8881", "from_number": "+***********", "start_time": "2 ... (3607 characters truncated) ... ow does that influence your art or relationships?", "recording_url": null, "recording_duration": 0, "call_status": "in-progress", "timeout_count": 0}', 'closing', 8, 14, 'CAb546c7046b7ac7ba2397a5fe4e5b68e5')
2025-08-01 00:01:56,238 - sqlalchemy.engine.Engine - INFO - [cached since 248.7s ago] ('{"call_sid": "CAb546c7046b7ac7ba2397a5fe4e5b68e5", "user_id": "8beb42d4-23b6-4dbf-a3ea-869044eb8881", "from_number": "+***********", "start_time": "2 ... (3607 characters truncated) ... ow does that influence your art or relationships?", "recording_url": null, "recording_duration": 0, "call_status": "in-progress", "timeout_count": 0}', 'closing', 8, 14, 'CAb546c7046b7ac7ba2397a5fe4e5b68e5')
2025-08-01 00:01:56,250 INFO sqlalchemy.engine.Engine COMMIT
2025-08-01 00:01:56,250 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-01 00:01:56,252 INFO sqlalchemy.engine.Engine COMMIT
2025-08-01 00:01:56,252 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-01 00:01:56,254 - agent.services.voice_service - INFO - Returning TwiML for next question
INFO:     **************:0 - "POST /voice/webhook/speech HTTP/1.1" 200 OK
2025-08-01 00:01:56,257 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-01 00:01:56,257 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 00:01:56,257 INFO sqlalchemy.engine.Engine 
                    INSERT INTO agent_conversations 
                    (call_sid, user_id, conversation_stage, topic_transitions, emotional_state,
                     engagement_score, conversation_quality, total_exchanges, successful_responses, timeout_count)
                    VALUES ($1, $2, $3, $4, $5,
                     $6, $7, $8, $9, $10)
                    ON CONFLICT (call_sid) DO UPDATE SET
                        conversation_stage = EXCLUDED.conversation_stage,
                        topic_transitions = EXCLUDED.topic_transitions,
                        emotional_state = EXCLUDED.emotional_state,
                        engagement_score = EXCLUDED.engagement_score,
                        conversation_quality = EXCLUDED.conversation_quality,
                        total_exchanges = EXCLUDED.total_exchanges,
                        successful_responses = EXCLUDED.successful_responses,
                        timeout_count = EXCLUDED.timeout_count,
                        updated_at = NOW()
                
2025-08-01 00:01:56,257 - sqlalchemy.engine.Engine - INFO - 
                    INSERT INTO agent_conversations 
                    (call_sid, user_id, conversation_stage, topic_transitions, emotional_state,
                     engagement_score, conversation_quality, total_exchanges, successful_responses, timeout_count)
                    VALUES ($1, $2, $3, $4, $5,
                     $6, $7, $8, $9, $10)
                    ON CONFLICT (call_sid) DO UPDATE SET
                        conversation_stage = EXCLUDED.conversation_stage,
                        topic_transitions = EXCLUDED.topic_transitions,
                        emotional_state = EXCLUDED.emotional_state,
                        engagement_score = EXCLUDED.engagement_score,
                        conversation_quality = EXCLUDED.conversation_quality,
                        total_exchanges = EXCLUDED.total_exchanges,
                        successful_responses = EXCLUDED.successful_responses,
                        timeout_count = EXCLUDED.timeout_count,
                        updated_at = NOW()
                
2025-08-01 00:01:56,257 INFO sqlalchemy.engine.Engine [cached since 219.7s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', '8beb42d4-23b6-4dbf-a3ea-869044eb8881', 'closing', '["greeting", "personality", "relationships"]', '{"engagement": 0.7309151599999999, "comfort": 0.77890002, "enthusiasm": 0.6324112299999999}', 0.7309151599999999, 0.7140754699999999, 16, 14, 0)
2025-08-01 00:01:56,257 - sqlalchemy.engine.Engine - INFO - [cached since 219.7s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', '8beb42d4-23b6-4dbf-a3ea-869044eb8881', 'closing', '["greeting", "personality", "relationships"]', '{"engagement": 0.7309151599999999, "comfort": 0.77890002, "enthusiasm": 0.6324112299999999}', 0.7309151599999999, 0.7140754699999999, 16, 14, 0)
2025-08-01 00:01:56,261 INFO sqlalchemy.engine.Engine COMMIT
2025-08-01 00:01:56,261 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-01 00:01:56,264 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-01 00:01:56,264 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 00:01:56,265 INFO sqlalchemy.engine.Engine 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-08-01 00:01:56,265 - sqlalchemy.engine.Engine - INFO - 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-08-01 00:01:56,265 INFO sqlalchemy.engine.Engine [cached since 219.7s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', 'collect_basic_info', 'active', 0.0, 5, '{"required_fields": ["name", "city"], "collected": 0}')
2025-08-01 00:01:56,265 - sqlalchemy.engine.Engine - INFO - [cached since 219.7s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', 'collect_basic_info', 'active', 0.0, 5, '{"required_fields": ["name", "city"], "collected": 0}')
2025-08-01 00:01:56,267 INFO sqlalchemy.engine.Engine 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-08-01 00:01:56,267 - sqlalchemy.engine.Engine - INFO - 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-08-01 00:01:56,267 INFO sqlalchemy.engine.Engine [cached since 219.7s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', 'collect_profession', 'completed', 1.0, 4, '{"required_fields": ["profession"], "collected": 1}')
2025-08-01 00:01:56,267 - sqlalchemy.engine.Engine - INFO - [cached since 219.7s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', 'collect_profession', 'completed', 1.0, 4, '{"required_fields": ["profession"], "collected": 1}')
2025-08-01 00:01:56,268 INFO sqlalchemy.engine.Engine 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-08-01 00:01:56,268 - sqlalchemy.engine.Engine - INFO - 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-08-01 00:01:56,268 INFO sqlalchemy.engine.Engine [cached since 219.7s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', 'collect_personality', 'completed', 1.0, 3, '{"required_fields": ["personality_traits"], "collected": 1}')
2025-08-01 00:01:56,268 - sqlalchemy.engine.Engine - INFO - [cached since 219.7s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', 'collect_personality', 'completed', 1.0, 3, '{"required_fields": ["personality_traits"], "collected": 1}')
2025-08-01 00:01:56,269 INFO sqlalchemy.engine.Engine 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-08-01 00:01:56,269 - sqlalchemy.engine.Engine - INFO - 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-08-01 00:01:56,269 INFO sqlalchemy.engine.Engine [cached since 219.7s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', 'collect_interests', 'completed', 1.0, 2, '{"required_fields": ["interests"], "collected": 1}')
2025-08-01 00:01:56,269 - sqlalchemy.engine.Engine - INFO - [cached since 219.7s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', 'collect_interests', 'completed', 1.0, 2, '{"required_fields": ["interests"], "collected": 1}')
2025-08-01 00:01:56,270 INFO sqlalchemy.engine.Engine 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-08-01 00:01:56,270 - sqlalchemy.engine.Engine - INFO - 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-08-01 00:01:56,270 INFO sqlalchemy.engine.Engine [cached since 219.7s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', 'collect_relationships', 'active', 0.0, 1, '{"required_fields": ["relationship_preferences"], "collected": 0}')
2025-08-01 00:01:56,270 - sqlalchemy.engine.Engine - INFO - [cached since 219.7s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', 'collect_relationships', 'active', 0.0, 1, '{"required_fields": ["relationship_preferences"], "collected": 0}')
2025-08-01 00:01:56,270 INFO sqlalchemy.engine.Engine COMMIT
2025-08-01 00:01:56,270 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-01 00:01:56,273 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-01 00:01:56,273 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 00:01:56,273 INFO sqlalchemy.engine.Engine 
                    INSERT INTO agent_memory 
                    (call_sid, memory_type, memory_data)
                    VALUES ($1, $2, $3)
                    ON CONFLICT (call_sid, memory_type) DO UPDATE SET
                        memory_data = EXCLUDED.memory_data,
                        updated_at = NOW()
                
2025-08-01 00:01:56,273 - sqlalchemy.engine.Engine - INFO - 
                    INSERT INTO agent_memory 
                    (call_sid, memory_type, memory_data)
                    VALUES ($1, $2, $3)
                    ON CONFLICT (call_sid, memory_type) DO UPDATE SET
                        memory_data = EXCLUDED.memory_data,
                        updated_at = NOW()
                
2025-08-01 00:01:56,274 INFO sqlalchemy.engine.Engine [cached since 219.7s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', 'short_term', '{"recent_exchanges": [{"timestamp": "2025-08-01T00:01:22.356360", "question": "What\\u2019s your favorite anime character to draw, and what do you lo ... (483 characters truncated) ... ll.  Yeah, Dragon Ball.  Goku, Goku", "emotional_state": {"engagement": 0.7309151599999999, "comfort": 0.77890002, "enthusiasm": 0.6324112299999999}}')
2025-08-01 00:01:56,274 - sqlalchemy.engine.Engine - INFO - [cached since 219.7s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', 'short_term', '{"recent_exchanges": [{"timestamp": "2025-08-01T00:01:22.356360", "question": "What\\u2019s your favorite anime character to draw, and what do you lo ... (483 characters truncated) ... ll.  Yeah, Dragon Ball.  Goku, Goku", "emotional_state": {"engagement": 0.7309151599999999, "comfort": 0.77890002, "enthusiasm": 0.6324112299999999}}')
2025-08-01 00:01:56,276 INFO sqlalchemy.engine.Engine 
                    INSERT INTO agent_memory 
                    (call_sid, memory_type, memory_data)
                    VALUES ($1, $2, $3)
                    ON CONFLICT (call_sid, memory_type) DO UPDATE SET
                        memory_data = EXCLUDED.memory_data,
                        updated_at = NOW()
                
2025-08-01 00:01:56,276 - sqlalchemy.engine.Engine - INFO - 
                    INSERT INTO agent_memory 
                    (call_sid, memory_type, memory_data)
                    VALUES ($1, $2, $3)
                    ON CONFLICT (call_sid, memory_type) DO UPDATE SET
                        memory_data = EXCLUDED.memory_data,
                        updated_at = NOW()
                
2025-08-01 00:01:56,276 INFO sqlalchemy.engine.Engine [cached since 219.7s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', 'user_profile', '{"collected_info": {"profession": "art student", "personality_traits": ["unique", "confident"], "interests": ["design", "AI", "collaborate with AI",  ... (18 characters truncated) ...  "drawing", "watching anime", "Dragon Ball", "Goku"]}, "conversation_quality": 0.7140754699999999, "total_exchanges": 16, "successful_responses": 14}')
2025-08-01 00:01:56,276 - sqlalchemy.engine.Engine - INFO - [cached since 219.7s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', 'user_profile', '{"collected_info": {"profession": "art student", "personality_traits": ["unique", "confident"], "interests": ["design", "AI", "collaborate with AI",  ... (18 characters truncated) ...  "drawing", "watching anime", "Dragon Ball", "Goku"]}, "conversation_quality": 0.7140754699999999, "total_exchanges": 16, "successful_responses": 14}')
2025-08-01 00:01:56,277 INFO sqlalchemy.engine.Engine COMMIT
2025-08-01 00:01:56,277 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-01 00:01:56,280 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-01 00:01:56,280 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 00:01:56,280 INFO sqlalchemy.engine.Engine 
                    INSERT INTO agent_metrics (call_sid, metric_name, metric_value, metric_unit)
                    VALUES ($1, $2, $3, $4)
                
2025-08-01 00:01:56,280 - sqlalchemy.engine.Engine - INFO - 
                    INSERT INTO agent_metrics (call_sid, metric_name, metric_value, metric_unit)
                    VALUES ($1, $2, $3, $4)
                
2025-08-01 00:01:56,280 INFO sqlalchemy.engine.Engine [cached since 219.7s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', 'decision_confidence', 0.8, 'score')
2025-08-01 00:01:56,280 - sqlalchemy.engine.Engine - INFO - [cached since 219.7s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', 'decision_confidence', 0.8, 'score')
2025-08-01 00:01:56,281 INFO sqlalchemy.engine.Engine 
                    INSERT INTO agent_metrics (call_sid, metric_name, metric_value, metric_unit)
                    VALUES ($1, $2, $3, $4)
                
2025-08-01 00:01:56,281 - sqlalchemy.engine.Engine - INFO - 
                    INSERT INTO agent_metrics (call_sid, metric_name, metric_value, metric_unit)
                    VALUES ($1, $2, $3, $4)
                
2025-08-01 00:01:56,281 INFO sqlalchemy.engine.Engine [cached since 219.7s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', 'user_engagement', 0.7309151599999999, 'score')
2025-08-01 00:01:56,281 - sqlalchemy.engine.Engine - INFO - [cached since 219.7s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', 'user_engagement', 0.7309151599999999, 'score')
2025-08-01 00:01:56,282 INFO sqlalchemy.engine.Engine 
                    INSERT INTO agent_metrics (call_sid, metric_name, metric_value, metric_unit)
                    VALUES ($1, $2, $3, $4)
                
2025-08-01 00:01:56,282 - sqlalchemy.engine.Engine - INFO - 
                    INSERT INTO agent_metrics (call_sid, metric_name, metric_value, metric_unit)
                    VALUES ($1, $2, $3, $4)
                
2025-08-01 00:01:56,282 INFO sqlalchemy.engine.Engine [cached since 219.7s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', 'info_collection_rate', 0.1875, 'rate')
2025-08-01 00:01:56,282 - sqlalchemy.engine.Engine - INFO - [cached since 219.7s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', 'info_collection_rate', 0.1875, 'rate')
2025-08-01 00:01:56,282 INFO sqlalchemy.engine.Engine COMMIT
2025-08-01 00:01:56,282 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-01 00:01:56,285 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-01 00:01:56,285 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 00:01:56,285 INFO sqlalchemy.engine.Engine 
                    INSERT INTO agent_strategies 
                    (call_sid, strategy_name, strategy_config, is_active)
                    VALUES ($1, $2, $3, $4)
                    ON CONFLICT (call_sid, strategy_name) DO UPDATE SET
                        strategy_config = EXCLUDED.strategy_config,
                        is_active = EXCLUDED.is_active,
                        updated_at = NOW()
                
2025-08-01 00:01:56,285 - sqlalchemy.engine.Engine - INFO - 
                    INSERT INTO agent_strategies 
                    (call_sid, strategy_name, strategy_config, is_active)
                    VALUES ($1, $2, $3, $4)
                    ON CONFLICT (call_sid, strategy_name) DO UPDATE SET
                        strategy_config = EXCLUDED.strategy_config,
                        is_active = EXCLUDED.is_active,
                        updated_at = NOW()
                
2025-08-01 00:01:56,285 INFO sqlalchemy.engine.Engine [cached since 219.7s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', 'info_collection', '{"missing_fields": ["basic_info", "relationships"], "collection_order": ["basic_info", "profession", "personality", "interests", "relationships"], "current_stage": "closing"}', True)
2025-08-01 00:01:56,285 - sqlalchemy.engine.Engine - INFO - [cached since 219.7s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', 'info_collection', '{"missing_fields": ["basic_info", "relationships"], "collection_order": ["basic_info", "profession", "personality", "interests", "relationships"], "current_stage": "closing"}', True)
2025-08-01 00:01:56,286 INFO sqlalchemy.engine.Engine COMMIT
2025-08-01 00:01:56,286 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-01 00:01:56,289 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-01 00:01:56,289 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 00:01:56,289 INFO sqlalchemy.engine.Engine 
                    INSERT INTO agent_learning 
                    (call_sid, user_id, learning_type, trigger_event, input_data, learning_outcome, 
                     effectiveness_score, applied_immediately)
                    VALUES ($1, $2, $3, $4, $5, $6, 
                     $7, $8)
                
2025-08-01 00:01:56,289 - sqlalchemy.engine.Engine - INFO - 
                    INSERT INTO agent_learning 
                    (call_sid, user_id, learning_type, trigger_event, input_data, learning_outcome, 
                     effectiveness_score, applied_immediately)
                    VALUES ($1, $2, $3, $4, $5, $6, 
                     $7, $8)
                
2025-08-01 00:01:56,289 INFO sqlalchemy.engine.Engine [cached since 190.1s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', '8beb42d4-23b6-4dbf-a3ea-869044eb8881', 'emotional_response', 'Question type: ask_question', '{"question": "What do you think makes Goku such an inspiring character, and how does that influence your art or relationships?", "user_response": "Dragon Ball.  Yeah, Dragon Ball.  Goku, Goku"}', '{"engagement_increase": true, "new_engagement": 0.7309151599999999}', 0.7309151599999999, True)
2025-08-01 00:01:56,289 - sqlalchemy.engine.Engine - INFO - [cached since 190.1s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', '8beb42d4-23b6-4dbf-a3ea-869044eb8881', 'emotional_response', 'Question type: ask_question', '{"question": "What do you think makes Goku such an inspiring character, and how does that influence your art or relationships?", "user_response": "Dragon Ball.  Yeah, Dragon Ball.  Goku, Goku"}', '{"engagement_increase": true, "new_engagement": 0.7309151599999999}', 0.7309151599999999, True)
2025-08-01 00:01:56,292 INFO sqlalchemy.engine.Engine 
                    INSERT INTO agent_learning 
                    (call_sid, user_id, learning_type, trigger_event, input_data, learning_outcome, 
                     effectiveness_score, applied_immediately)
                    VALUES ($1, $2, $3, $4, $5, $6, 
                     $7, $8)
                
2025-08-01 00:01:56,292 - sqlalchemy.engine.Engine - INFO - 
                    INSERT INTO agent_learning 
                    (call_sid, user_id, learning_type, trigger_event, input_data, learning_outcome, 
                     effectiveness_score, applied_immediately)
                    VALUES ($1, $2, $3, $4, $5, $6, 
                     $7, $8)
                
2025-08-01 00:01:56,292 INFO sqlalchemy.engine.Engine [cached since 190.1s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', '8beb42d4-23b6-4dbf-a3ea-869044eb8881', 'question_success', 'Information extraction from: ask_question', '{"question": "What do you think makes Goku such an inspiring character, and how does that influence your art or relationships?", "user_response": "Dragon Ball.  Yeah, Dragon Ball.  Goku, Goku"}', '{"info_extracted": 3, "fields": ["profession", "personality_traits", "interests"]}', 1.0, True)
2025-08-01 00:01:56,292 - sqlalchemy.engine.Engine - INFO - [cached since 190.1s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', '8beb42d4-23b6-4dbf-a3ea-869044eb8881', 'question_success', 'Information extraction from: ask_question', '{"question": "What do you think makes Goku such an inspiring character, and how does that influence your art or relationships?", "user_response": "Dragon Ball.  Yeah, Dragon Ball.  Goku, Goku"}', '{"info_extracted": 3, "fields": ["profession", "personality_traits", "interests"]}', 1.0, True)
2025-08-01 00:01:56,295 INFO sqlalchemy.engine.Engine COMMIT
2025-08-01 00:01:56,295 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-01 00:01:56,296 - agent.services.conversation_agent - INFO - ⏱️  save_agent_data completed in 0.064s
2025-08-01 00:02:23,775 - api.voice - INFO - Speech webhook: {'Called': '+***********', 'ToState': 'CA', 'CallerCountry': 'US', 'Direction': 'outbound-api', 'SpeechResult': "I don't think that this even matter right now. Can you ask something that's related to  Partner, finding the loved ones.  Or dating relating to dating or anything like that.", 'CallerState': 'CA', 'Language': 'en-US', 'ToZip': '', 'Confidence': '0.98296', 'CallSid': 'CAb546c7046b7ac7ba2397a5fe4e5b68e5', 'To': '+***********', 'CallerZip': '', 'ToCountry': 'US', 'CalledZip': '', 'ApiVersion': '2010-04-01', 'CalledCity': '', 'CallStatus': 'in-progress', 'From': '+***********', 'AccountSid': 'AC98fddc0d9bf796ef17233cf22bbe31f5', 'CalledCountry': 'US', 'CallerCity': 'GUADALUPE', 'ToCity': '', 'FromCountry': 'US', 'Caller': '+***********', 'FromCity': 'GUADALUPE', 'CalledState': 'CA', 'FromZip': '', 'FromState': 'CA'}
2025-08-01 00:02:23,775 - agent.services.voice_service - INFO - Processing speech input for call: CAb546c7046b7ac7ba2397a5fe4e5b68e5
2025-08-01 00:02:23,775 - agent.services.voice_service - INFO - User speech: 'I don't think that this even matter right now. Can you ask something that's related to  Partner, finding the loved ones.  Or dating relating to dating or anything like that.' (confidence: 0.98296)
2025-08-01 00:02:23,775 - agent.services.voice_service - INFO - Session updated - Questions: 8, Responses: 15
2025-08-01 00:02:23,775 - agent.services.voice_service - INFO - Using ConversationAgent for call: CAb546c7046b7ac7ba2397a5fe4e5b68e5
2025-08-01 00:02:25,599 - agent.services.ai_service - INFO - AI emotion analysis: {'engagement': 0.4, 'comfort': 0.6, 'enthusiasm': 0.3}
2025-08-01 00:02:25,599 - agent.services.conversation_agent - INFO - AI emotion analysis updated: {'engagement': 0.49927454799999993, 'comfort': 0.653670006, 'enthusiasm': 0.39972336899999994}
2025-08-01 00:02:25,599 - agent.services.conversation_agent - INFO - ⏱️  update_emotional_state_detailed completed in 1.824s
2025-08-01 00:02:25,599 - agent.services.conversation_agent - INFO - ⏱️  update_emotional_state completed in 1.824s
2025-08-01 00:02:26,525 - agent.services.ai_service - INFO - AI extracted info: {'name': None, 'city': None, 'profession': None, 'personality_traits': None, 'interests': None, 'relationship_preferences': ['finding the loved ones', 'dating']}
2025-08-01 00:02:26,525 - agent.services.conversation_agent - INFO - AI extracted and merged info: {'name': None, 'city': None, 'profession': None, 'personality_traits': None, 'interests': None, 'relationship_preferences': ['finding the loved ones', 'dating']}
2025-08-01 00:02:26,525 - agent.services.conversation_agent - INFO - ⏱️  extract_information_detailed completed in 2.750s
2025-08-01 00:02:26,525 - agent.services.conversation_agent - INFO - ⏱️  extract_information completed in 2.750s
2025-08-01 00:02:26,525 - agent.services.conversation_agent - INFO - Parallel AI processing completed in 2.75s
2025-08-01 00:02:26,525 - agent.services.ai_service - INFO - Generating AI question for domain: closing
2025-08-01 00:02:27,748 - agent.services.ai_service - INFO - AI question generated: What qualities do you think are most important in a partner when it comes to building a loving relationship?
2025-08-01 00:02:27,748 - agent.services.conversation_agent - INFO - ⏱️  ai_decision completed in 1.223s
2025-08-01 00:02:27,749 - agent.services.conversation_agent - INFO - ⏱️  make_smart_decision completed in 1.224s
2025-08-01 00:02:27,749 - agent.services.conversation_agent - INFO - Decision generated in 1.22s, total processing: 3.97s
2025-08-01 00:02:27,749 - agent.services.conversation_agent - INFO - ⏱️  async_optimization completed in 3.974s
2025-08-01 00:02:27,749 - agent.services.conversation_agent - INFO - Agent transitioning stage: closing -> completed
2025-08-01 00:02:27,749 - agent.services.conversation_agent - INFO - ⏱️  process_input completed in 3.975s
2025-08-01 00:02:27,749 - agent.services.voice_service - INFO - Agent decision: ask_question - What qualities do you think are most important in a partner when it comes to building a loving relationship? (confidence: 0.8)
2025-08-01 00:02:27,750 - agent.services.voice_service - INFO - Agent decided to end call
2025-08-01 00:02:27,750 - agent.services.voice_service - INFO - Generated next question: 'None'
2025-08-01 00:02:27,754 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-01 00:02:27,754 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 00:02:27,755 INFO sqlalchemy.engine.Engine 
                        INSERT INTO agent_decisions 
                        (call_sid, decision_type, decision_data, reasoning, confidence)
                        VALUES ($1, $2, $3, $4, $5)
                    
2025-08-01 00:02:27,755 - sqlalchemy.engine.Engine - INFO - 
                        INSERT INTO agent_decisions 
                        (call_sid, decision_type, decision_data, reasoning, confidence)
                        VALUES ($1, $2, $3, $4, $5)
                    
2025-08-01 00:02:27,755 INFO sqlalchemy.engine.Engine [cached since 251.2s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', 'question_generate', '{"action_type": "ask_question", "content": "What qualities do you think are most important in a partner when it comes to building a loving relationsh ... (296 characters truncated) ... .49927454799999993, "comfort": 0.653670006, "enthusiasm": 0.39972336899999994}, "collected_info_count": 4, "timestamp": "2025-08-01T00:02:27.751372"}', 'AI generated question based on conversation context', 0.8)
2025-08-01 00:02:27,755 - sqlalchemy.engine.Engine - INFO - [cached since 251.2s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', 'question_generate', '{"action_type": "ask_question", "content": "What qualities do you think are most important in a partner when it comes to building a loving relationsh ... (296 characters truncated) ... .49927454799999993, "comfort": 0.653670006, "enthusiasm": 0.39972336899999994}, "collected_info_count": 4, "timestamp": "2025-08-01T00:02:27.751372"}', 'AI generated question based on conversation context', 0.8)
2025-08-01 00:02:27,756 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-01 00:02:27,756 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 00:02:27,756 INFO sqlalchemy.engine.Engine 
                        UPDATE voice_sessions SET
                            session_data = $1,
                            last_activity = NOW(),
                            current_stage = $2,
                            questions_asked = $3,
                            responses_received = $4
                        WHERE twilio_call_sid = $5
                        AND session_status = 'active'
                    
2025-08-01 00:02:27,756 - sqlalchemy.engine.Engine - INFO - 
                        UPDATE voice_sessions SET
                            session_data = $1,
                            last_activity = NOW(),
                            current_stage = $2,
                            questions_asked = $3,
                            responses_received = $4
                        WHERE twilio_call_sid = $5
                        AND session_status = 'active'
                    
2025-08-01 00:02:27,756 INFO sqlalchemy.engine.Engine [cached since 280.2s ago] ('{"call_sid": "CAb546c7046b7ac7ba2397a5fe4e5b68e5", "user_id": "8beb42d4-23b6-4dbf-a3ea-869044eb8881", "from_number": "+***********", "start_time": "2 ... (4152 characters truncated) ... ow does that influence your art or relationships?", "recording_url": null, "recording_duration": 0, "call_status": "in-progress", "timeout_count": 0}', 'completed', 9, 16, 'CAb546c7046b7ac7ba2397a5fe4e5b68e5')
2025-08-01 00:02:27,756 - sqlalchemy.engine.Engine - INFO - [cached since 280.2s ago] ('{"call_sid": "CAb546c7046b7ac7ba2397a5fe4e5b68e5", "user_id": "8beb42d4-23b6-4dbf-a3ea-869044eb8881", "from_number": "+***********", "start_time": "2 ... (4152 characters truncated) ... ow does that influence your art or relationships?", "recording_url": null, "recording_duration": 0, "call_status": "in-progress", "timeout_count": 0}', 'completed', 9, 16, 'CAb546c7046b7ac7ba2397a5fe4e5b68e5')
2025-08-01 00:02:27,759 INFO sqlalchemy.engine.Engine COMMIT
2025-08-01 00:02:27,759 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-01 00:02:27,760 INFO sqlalchemy.engine.Engine COMMIT
2025-08-01 00:02:27,760 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-01 00:02:27,762 - agent.services.voice_service - INFO - Processing interview completion for session: CAb546c7046b7ac7ba2397a5fe4e5b68e5
2025-08-01 00:02:27,763 - agent.core.analysis_engine - INFO - Starting voice analysis for user: 8beb42d4-23b6-4dbf-a3ea-869044eb8881
2025-08-01 00:02:27,763 - agent.core.analysis_engine - ERROR - DeepSeek analysis failed: asyncio.run() cannot be called from a running event loop
/Users/<USER>/Desktop/profiler_agent/profilerAgent/agent/core/analysis_engine.py:78: RuntimeWarning: coroutine 'DeepSeekClient.analyze_async' was never awaited
  return {"error": str(e)}
RuntimeWarning: Enable tracemalloc to get the object allocation traceback
2025-08-01 00:02:27,769 - agent.core.analysis_engine - ERROR - Voice analysis failed for user 8beb42d4-23b6-4dbf-a3ea-869044eb8881: DeepSeek analysis failed: asyncio.run() cannot be called from a running event loop
/Users/<USER>/Desktop/profiler_agent/profilerAgent/agent/core/user_manager.py:760: RuntimeWarning: coroutine 'UserManager._save_user_to_db' was never awaited
  self._save_user_to_db(user)
RuntimeWarning: Enable tracemalloc to get the object allocation traceback
2025-08-01 00:02:27,771 - agent.core.user_manager - INFO - Publishing event user.voice_completed to 0 handlers
2025-08-01 00:02:27,771 - agent.core.user_manager - INFO - Voice call completed for user: 8beb42d4-23b6-4dbf-a3ea-869044eb8881
2025-08-01 00:02:27,772 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-01 00:02:27,772 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 00:02:27,772 INFO sqlalchemy.engine.Engine 
                    INSERT INTO agent_conversations 
                    (call_sid, user_id, conversation_stage, topic_transitions, emotional_state,
                     engagement_score, conversation_quality, total_exchanges, successful_responses, timeout_count)
                    VALUES ($1, $2, $3, $4, $5,
                     $6, $7, $8, $9, $10)
                    ON CONFLICT (call_sid) DO UPDATE SET
                        conversation_stage = EXCLUDED.conversation_stage,
                        topic_transitions = EXCLUDED.topic_transitions,
                        emotional_state = EXCLUDED.emotional_state,
                        engagement_score = EXCLUDED.engagement_score,
                        conversation_quality = EXCLUDED.conversation_quality,
                        total_exchanges = EXCLUDED.total_exchanges,
                        successful_responses = EXCLUDED.successful_responses,
                        timeout_count = EXCLUDED.timeout_count,
                        updated_at = NOW()
                
2025-08-01 00:02:27,772 - sqlalchemy.engine.Engine - INFO - 
                    INSERT INTO agent_conversations 
                    (call_sid, user_id, conversation_stage, topic_transitions, emotional_state,
                     engagement_score, conversation_quality, total_exchanges, successful_responses, timeout_count)
                    VALUES ($1, $2, $3, $4, $5,
                     $6, $7, $8, $9, $10)
                    ON CONFLICT (call_sid) DO UPDATE SET
                        conversation_stage = EXCLUDED.conversation_stage,
                        topic_transitions = EXCLUDED.topic_transitions,
                        emotional_state = EXCLUDED.emotional_state,
                        engagement_score = EXCLUDED.engagement_score,
                        conversation_quality = EXCLUDED.conversation_quality,
                        total_exchanges = EXCLUDED.total_exchanges,
                        successful_responses = EXCLUDED.successful_responses,
                        timeout_count = EXCLUDED.timeout_count,
                        updated_at = NOW()
                
2025-08-01 00:02:27,772 INFO sqlalchemy.engine.Engine [cached since 251.3s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', '8beb42d4-23b6-4dbf-a3ea-869044eb8881', 'completed', '["greeting", "personality", "relationships", "closing"]', '{"engagement": 0.49927454799999993, "comfort": 0.653670006, "enthusiasm": 0.39972336899999994}', 0.49927454799999993, 0.5175559743333332, 18, 16, 0)
2025-08-01 00:02:27,772 - sqlalchemy.engine.Engine - INFO - [cached since 251.3s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', '8beb42d4-23b6-4dbf-a3ea-869044eb8881', 'completed', '["greeting", "personality", "relationships", "closing"]', '{"engagement": 0.49927454799999993, "comfort": 0.653670006, "enthusiasm": 0.39972336899999994}', 0.49927454799999993, 0.5175559743333332, 18, 16, 0)
2025-08-01 00:02:27,773 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-01 00:02:27,773 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 00:02:27,774 INFO sqlalchemy.engine.Engine 
                        UPDATE voice_sessions SET
                            session_status = 'completed',
                            call_duration = $1,
                            analysis_data = $2,
                            completed_at = NOW()
                        WHERE twilio_call_sid = $3
                    
2025-08-01 00:02:27,774 - sqlalchemy.engine.Engine - INFO - 
                        UPDATE voice_sessions SET
                            session_status = 'completed',
                            call_duration = $1,
                            analysis_data = $2,
                            completed_at = NOW()
                        WHERE twilio_call_sid = $3
                    
2025-08-01 00:02:27,774 INFO sqlalchemy.engine.Engine [generated in 0.00020s] (280, '{"transcript": "Assistant: Hello! Thank you for answering. This is your scheduled voice interview for your dating profile. Are you ready to begin? Ju ... (6129 characters truncated) ... response": "", "stage": "closing"}], "quality_score": 1.1577777777777778, "questions_asked": 9, "responses_received": 16, "call_status": "completed"}', 'CAb546c7046b7ac7ba2397a5fe4e5b68e5')
2025-08-01 00:02:27,774 - sqlalchemy.engine.Engine - INFO - [generated in 0.00020s] (280, '{"transcript": "Assistant: Hello! Thank you for answering. This is your scheduled voice interview for your dating profile. Are you ready to begin? Ju ... (6129 characters truncated) ... response": "", "stage": "closing"}], "quality_score": 1.1577777777777778, "questions_asked": 9, "responses_received": 16, "call_status": "completed"}', 'CAb546c7046b7ac7ba2397a5fe4e5b68e5')
2025-08-01 00:02:27,774 INFO sqlalchemy.engine.Engine COMMIT
2025-08-01 00:02:27,774 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-01 00:02:27,776 INFO sqlalchemy.engine.Engine COMMIT
2025-08-01 00:02:27,776 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-01 00:02:27,776 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-01 00:02:27,776 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 00:02:27,776 INFO sqlalchemy.engine.Engine 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-08-01 00:02:27,776 - sqlalchemy.engine.Engine - INFO - 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-08-01 00:02:27,777 INFO sqlalchemy.engine.Engine [cached since 251.3s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', 'collect_basic_info', 'active', 0.0, 5, '{"required_fields": ["name", "city"], "collected": 0}')
2025-08-01 00:02:27,777 - sqlalchemy.engine.Engine - INFO - [cached since 251.3s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', 'collect_basic_info', 'active', 0.0, 5, '{"required_fields": ["name", "city"], "collected": 0}')
2025-08-01 00:02:27,778 - agent.services.voice_service - INFO - Session marked as completed in database: CAb546c7046b7ac7ba2397a5fe4e5b68e5
2025-08-01 00:02:27,779 INFO sqlalchemy.engine.Engine 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-08-01 00:02:27,779 - sqlalchemy.engine.Engine - INFO - 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-08-01 00:02:27,779 INFO sqlalchemy.engine.Engine [cached since 251.3s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', 'collect_profession', 'completed', 1.0, 4, '{"required_fields": ["profession"], "collected": 1}')
2025-08-01 00:02:27,779 - sqlalchemy.engine.Engine - INFO - [cached since 251.3s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', 'collect_profession', 'completed', 1.0, 4, '{"required_fields": ["profession"], "collected": 1}')
2025-08-01 00:02:27,780 INFO sqlalchemy.engine.Engine 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-08-01 00:02:27,780 - sqlalchemy.engine.Engine - INFO - 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-08-01 00:02:27,781 INFO sqlalchemy.engine.Engine [cached since 251.3s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', 'collect_personality', 'completed', 1.0, 3, '{"required_fields": ["personality_traits"], "collected": 1}')
2025-08-01 00:02:27,781 - sqlalchemy.engine.Engine - INFO - [cached since 251.3s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', 'collect_personality', 'completed', 1.0, 3, '{"required_fields": ["personality_traits"], "collected": 1}')
2025-08-01 00:02:27,781 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-01 00:02:27,781 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 00:02:27,781 INFO sqlalchemy.engine.Engine 
                        INSERT INTO users (
                            id, phone_number, email, first_name, last_name, age, city, profession,
                            sms_verified, voice_call_completed, linkedin_verified,
                            verification_status, verification_level, status, created_at, updated_at, last_login
                        ) VALUES (
                            $1, $2, $3, $4, $5, $6, $7, $8,
                            $9, $10, $11,
                            $12, $13, $14, $15, $16, $17
                        )
                        ON CONFLICT (id) DO UPDATE SET
                            phone_number = EXCLUDED.phone_number,
                            email = EXCLUDED.email,
                            first_name = EXCLUDED.first_name,
                            last_name = EXCLUDED.last_name,
                            age = EXCLUDED.age,
                            city = EXCLUDED.city,
                            profession = EXCLUDED.profession,
                            sms_verified = EXCLUDED.sms_verified,
                            voice_call_completed = EXCLUDED.voice_call_completed,
                            linkedin_verified = EXCLUDED.linkedin_verified,
                            verification_status = EXCLUDED.verification_status,
                            verification_level = EXCLUDED.verification_level,
                            status = EXCLUDED.status,
                            updated_at = EXCLUDED.updated_at,
                            last_login = EXCLUDED.last_login
                    
2025-08-01 00:02:27,781 - sqlalchemy.engine.Engine - INFO - 
                        INSERT INTO users (
                            id, phone_number, email, first_name, last_name, age, city, profession,
                            sms_verified, voice_call_completed, linkedin_verified,
                            verification_status, verification_level, status, created_at, updated_at, last_login
                        ) VALUES (
                            $1, $2, $3, $4, $5, $6, $7, $8,
                            $9, $10, $11,
                            $12, $13, $14, $15, $16, $17
                        )
                        ON CONFLICT (id) DO UPDATE SET
                            phone_number = EXCLUDED.phone_number,
                            email = EXCLUDED.email,
                            first_name = EXCLUDED.first_name,
                            last_name = EXCLUDED.last_name,
                            age = EXCLUDED.age,
                            city = EXCLUDED.city,
                            profession = EXCLUDED.profession,
                            sms_verified = EXCLUDED.sms_verified,
                            voice_call_completed = EXCLUDED.voice_call_completed,
                            linkedin_verified = EXCLUDED.linkedin_verified,
                            verification_status = EXCLUDED.verification_status,
                            verification_level = EXCLUDED.verification_level,
                            status = EXCLUDED.status,
                            updated_at = EXCLUDED.updated_at,
                            last_login = EXCLUDED.last_login
                    
2025-08-01 00:02:27,782 INFO sqlalchemy.engine.Engine [generated in 0.00020s] ('8beb42d4-23b6-4dbf-a3ea-869044eb8881', '+***********', None, 'a', 'a', None, None, None, True, True, False, 'voice_completed', 'partial', 'active', datetime.datetime(2025, 7, 31, 4, 23, 40, 211471, tzinfo=datetime.timezone.utc), datetime.datetime(2025, 8, 1, 0, 2, 27, 778213), datetime.datetime(2025, 7, 31, 4, 23, 40, 211484, tzinfo=datetime.timezone.utc))
2025-08-01 00:02:27,782 - sqlalchemy.engine.Engine - INFO - [generated in 0.00020s] ('8beb42d4-23b6-4dbf-a3ea-869044eb8881', '+***********', None, 'a', 'a', None, None, None, True, True, False, 'voice_completed', 'partial', 'active', datetime.datetime(2025, 7, 31, 4, 23, 40, 211471, tzinfo=datetime.timezone.utc), datetime.datetime(2025, 8, 1, 0, 2, 27, 778213), datetime.datetime(2025, 7, 31, 4, 23, 40, 211484, tzinfo=datetime.timezone.utc))
2025-08-01 00:02:27,782 INFO sqlalchemy.engine.Engine 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-08-01 00:02:27,782 - sqlalchemy.engine.Engine - INFO - 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-08-01 00:02:27,782 INFO sqlalchemy.engine.Engine [cached since 251.3s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', 'collect_interests', 'completed', 1.0, 2, '{"required_fields": ["interests"], "collected": 1}')
2025-08-01 00:02:27,782 - sqlalchemy.engine.Engine - INFO - [cached since 251.3s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', 'collect_interests', 'completed', 1.0, 2, '{"required_fields": ["interests"], "collected": 1}')
2025-08-01 00:02:27,783 INFO sqlalchemy.engine.Engine 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-08-01 00:02:27,783 - sqlalchemy.engine.Engine - INFO - 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-08-01 00:02:27,783 INFO sqlalchemy.engine.Engine [cached since 251.3s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', 'collect_relationships', 'completed', 1.0, 1, '{"required_fields": ["relationship_preferences"], "collected": 1}')
2025-08-01 00:02:27,783 - sqlalchemy.engine.Engine - INFO - [cached since 251.3s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', 'collect_relationships', 'completed', 1.0, 1, '{"required_fields": ["relationship_preferences"], "collected": 1}')
2025-08-01 00:02:27,784 INFO sqlalchemy.engine.Engine COMMIT
2025-08-01 00:02:27,784 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-01 00:02:27,785 INFO sqlalchemy.engine.Engine COMMIT
2025-08-01 00:02:27,785 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-01 00:02:27,786 - agent.services.voice_service - INFO - Updated user status to voice_call_completed for user: 8beb42d4-23b6-4dbf-a3ea-869044eb8881
2025-08-01 00:02:27,786 - agent.services.voice_service - INFO - Voice interview completed and analyzed for user: 8beb42d4-23b6-4dbf-a3ea-869044eb8881
INFO:     ***********:0 - "POST /voice/webhook/speech HTTP/1.1" 200 OK
2025-08-01 00:02:27,786 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-01 00:02:27,786 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 00:02:27,787 INFO sqlalchemy.engine.Engine 
                    INSERT INTO agent_memory 
                    (call_sid, memory_type, memory_data)
                    VALUES ($1, $2, $3)
                    ON CONFLICT (call_sid, memory_type) DO UPDATE SET
                        memory_data = EXCLUDED.memory_data,
                        updated_at = NOW()
                
2025-08-01 00:02:27,787 - sqlalchemy.engine.Engine - INFO - 
                    INSERT INTO agent_memory 
                    (call_sid, memory_type, memory_data)
                    VALUES ($1, $2, $3)
                    ON CONFLICT (call_sid, memory_type) DO UPDATE SET
                        memory_data = EXCLUDED.memory_data,
                        updated_at = NOW()
                
2025-08-01 00:02:27,787 INFO sqlalchemy.engine.Engine [cached since 251.3s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', 'short_term', '{"recent_exchanges": [{"timestamp": "2025-08-01T00:01:56.229010", "question": "What do you think makes Goku such an inspiring character, and how does ... (759 characters truncated) ... to dating or anything like that.", "emotional_state": {"engagement": 0.49927454799999993, "comfort": 0.653670006, "enthusiasm": 0.39972336899999994}}')
2025-08-01 00:02:27,787 - sqlalchemy.engine.Engine - INFO - [cached since 251.3s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', 'short_term', '{"recent_exchanges": [{"timestamp": "2025-08-01T00:01:56.229010", "question": "What do you think makes Goku such an inspiring character, and how does ... (759 characters truncated) ... to dating or anything like that.", "emotional_state": {"engagement": 0.49927454799999993, "comfort": 0.653670006, "enthusiasm": 0.39972336899999994}}')
2025-08-01 00:02:27,788 INFO sqlalchemy.engine.Engine 
                    INSERT INTO agent_memory 
                    (call_sid, memory_type, memory_data)
                    VALUES ($1, $2, $3)
                    ON CONFLICT (call_sid, memory_type) DO UPDATE SET
                        memory_data = EXCLUDED.memory_data,
                        updated_at = NOW()
                
2025-08-01 00:02:27,788 - sqlalchemy.engine.Engine - INFO - 
                    INSERT INTO agent_memory 
                    (call_sid, memory_type, memory_data)
                    VALUES ($1, $2, $3)
                    ON CONFLICT (call_sid, memory_type) DO UPDATE SET
                        memory_data = EXCLUDED.memory_data,
                        updated_at = NOW()
                
2025-08-01 00:02:27,788 INFO sqlalchemy.engine.Engine [cached since 251.3s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', 'user_profile', '{"collected_info": {"profession": "art student", "personality_traits": ["unique", "confident"], "interests": ["design", "AI", "collaborate with AI",  ... (84 characters truncated) ... p_preferences": ["finding the loved ones", "dating"]}, "conversation_quality": 0.5175559743333332, "total_exchanges": 18, "successful_responses": 16}')
2025-08-01 00:02:27,788 - sqlalchemy.engine.Engine - INFO - [cached since 251.3s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', 'user_profile', '{"collected_info": {"profession": "art student", "personality_traits": ["unique", "confident"], "interests": ["design", "AI", "collaborate with AI",  ... (84 characters truncated) ... p_preferences": ["finding the loved ones", "dating"]}, "conversation_quality": 0.5175559743333332, "total_exchanges": 18, "successful_responses": 16}')
2025-08-01 00:02:27,788 INFO sqlalchemy.engine.Engine COMMIT
2025-08-01 00:02:27,788 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-01 00:02:27,790 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-01 00:02:27,790 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 00:02:27,790 INFO sqlalchemy.engine.Engine 
                    INSERT INTO agent_metrics (call_sid, metric_name, metric_value, metric_unit)
                    VALUES ($1, $2, $3, $4)
                
2025-08-01 00:02:27,790 - sqlalchemy.engine.Engine - INFO - 
                    INSERT INTO agent_metrics (call_sid, metric_name, metric_value, metric_unit)
                    VALUES ($1, $2, $3, $4)
                
2025-08-01 00:02:27,790 INFO sqlalchemy.engine.Engine [cached since 251.3s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', 'decision_confidence', 0.8, 'score')
2025-08-01 00:02:27,790 - sqlalchemy.engine.Engine - INFO - [cached since 251.3s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', 'decision_confidence', 0.8, 'score')
2025-08-01 00:02:27,791 INFO sqlalchemy.engine.Engine 
                    INSERT INTO agent_metrics (call_sid, metric_name, metric_value, metric_unit)
                    VALUES ($1, $2, $3, $4)
                
2025-08-01 00:02:27,791 - sqlalchemy.engine.Engine - INFO - 
                    INSERT INTO agent_metrics (call_sid, metric_name, metric_value, metric_unit)
                    VALUES ($1, $2, $3, $4)
                
2025-08-01 00:02:27,791 INFO sqlalchemy.engine.Engine [cached since 251.3s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', 'user_engagement', 0.49927454799999993, 'score')
2025-08-01 00:02:27,791 - sqlalchemy.engine.Engine - INFO - [cached since 251.3s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', 'user_engagement', 0.49927454799999993, 'score')
2025-08-01 00:02:27,792 INFO sqlalchemy.engine.Engine 
                    INSERT INTO agent_metrics (call_sid, metric_name, metric_value, metric_unit)
                    VALUES ($1, $2, $3, $4)
                
2025-08-01 00:02:27,792 - sqlalchemy.engine.Engine - INFO - 
                    INSERT INTO agent_metrics (call_sid, metric_name, metric_value, metric_unit)
                    VALUES ($1, $2, $3, $4)
                
2025-08-01 00:02:27,792 INFO sqlalchemy.engine.Engine [cached since 251.3s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', 'info_collection_rate', 0.2222222222222222, 'rate')
2025-08-01 00:02:27,792 - sqlalchemy.engine.Engine - INFO - [cached since 251.3s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', 'info_collection_rate', 0.2222222222222222, 'rate')
2025-08-01 00:02:27,792 INFO sqlalchemy.engine.Engine COMMIT
2025-08-01 00:02:27,792 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-01 00:02:27,793 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-01 00:02:27,793 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 00:02:27,793 INFO sqlalchemy.engine.Engine 
                    INSERT INTO agent_strategies 
                    (call_sid, strategy_name, strategy_config, is_active)
                    VALUES ($1, $2, $3, $4)
                    ON CONFLICT (call_sid, strategy_name) DO UPDATE SET
                        strategy_config = EXCLUDED.strategy_config,
                        is_active = EXCLUDED.is_active,
                        updated_at = NOW()
                
2025-08-01 00:02:27,793 - sqlalchemy.engine.Engine - INFO - 
                    INSERT INTO agent_strategies 
                    (call_sid, strategy_name, strategy_config, is_active)
                    VALUES ($1, $2, $3, $4)
                    ON CONFLICT (call_sid, strategy_name) DO UPDATE SET
                        strategy_config = EXCLUDED.strategy_config,
                        is_active = EXCLUDED.is_active,
                        updated_at = NOW()
                
2025-08-01 00:02:27,793 INFO sqlalchemy.engine.Engine [cached since 251.3s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', 'info_collection', '{"missing_fields": ["basic_info"], "collection_order": ["basic_info", "profession", "personality", "interests", "relationships"], "current_stage": "completed"}', True)
2025-08-01 00:02:27,793 - sqlalchemy.engine.Engine - INFO - [cached since 251.3s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', 'info_collection', '{"missing_fields": ["basic_info"], "collection_order": ["basic_info", "profession", "personality", "interests", "relationships"], "current_stage": "completed"}', True)
2025-08-01 00:02:27,794 INFO sqlalchemy.engine.Engine COMMIT
2025-08-01 00:02:27,794 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-01 00:02:27,795 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-01 00:02:27,795 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 00:02:27,795 INFO sqlalchemy.engine.Engine 
                    INSERT INTO agent_learning 
                    (call_sid, user_id, learning_type, trigger_event, input_data, learning_outcome, 
                     effectiveness_score, applied_immediately)
                    VALUES ($1, $2, $3, $4, $5, $6, 
                     $7, $8)
                
2025-08-01 00:02:27,795 - sqlalchemy.engine.Engine - INFO - 
                    INSERT INTO agent_learning 
                    (call_sid, user_id, learning_type, trigger_event, input_data, learning_outcome, 
                     effectiveness_score, applied_immediately)
                    VALUES ($1, $2, $3, $4, $5, $6, 
                     $7, $8)
                
2025-08-01 00:02:27,795 INFO sqlalchemy.engine.Engine [cached since 221.6s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', '8beb42d4-23b6-4dbf-a3ea-869044eb8881', 'question_success', 'Information extraction from: ask_question', '{"question": "What qualities do you think are most important in a partner when it comes to building a loving relationship?", "user_response": "I don\ ... (22 characters truncated) ... n matter right now. Can you ask something that\'s related to  Partner, finding the loved ones.  Or dating relating to dating or anything like that."}', '{"info_extracted": 4, "fields": ["profession", "personality_traits", "interests", "relationship_preferences"]}', 1.0, True)
2025-08-01 00:02:27,795 - sqlalchemy.engine.Engine - INFO - [cached since 221.6s ago] ('CAb546c7046b7ac7ba2397a5fe4e5b68e5', '8beb42d4-23b6-4dbf-a3ea-869044eb8881', 'question_success', 'Information extraction from: ask_question', '{"question": "What qualities do you think are most important in a partner when it comes to building a loving relationship?", "user_response": "I don\ ... (22 characters truncated) ... n matter right now. Can you ask something that\'s related to  Partner, finding the loved ones.  Or dating relating to dating or anything like that."}', '{"info_extracted": 4, "fields": ["profession", "personality_traits", "interests", "relationship_preferences"]}', 1.0, True)
2025-08-01 00:02:27,796 INFO sqlalchemy.engine.Engine COMMIT
2025-08-01 00:02:27,796 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-01 00:02:27,797 - agent.services.conversation_agent - INFO - ⏱️  save_agent_data completed in 0.046s
2025-08-01 00:02:39,082 - api.voice - INFO - Call status webhook: {'Called': '+***********', 'ToState': 'CA', 'CallerCountry': 'US', 'Direction': 'outbound-api', 'Timestamp': 'Thu, 31 Jul 2025 16:02:39 +0000', 'CallbackSource': 'call-progress-events', 'SipResponseCode': '200', 'CallerState': 'CA', 'ToZip': '', 'SequenceNumber': '3', 'CallSid': 'CAb546c7046b7ac7ba2397a5fe4e5b68e5', 'To': '+***********', 'CallerZip': '', 'ToCountry': 'US', 'CalledZip': '', 'ApiVersion': '2010-04-01', 'CalledCity': '', 'CallStatus': 'completed', 'Duration': '5', 'From': '+***********', 'CallDuration': '291', 'AccountSid': 'AC98fddc0d9bf796ef17233cf22bbe31f5', 'CalledCountry': 'US', 'CallerCity': 'GUADALUPE', 'ToCity': '', 'FromCountry': 'US', 'Caller': '+***********', 'FromCity': 'GUADALUPE', 'CalledState': 'CA', 'FromZip': '', 'FromState': 'CA'}
2025-08-01 00:02:39,082 - agent.services.voice_service - INFO - Call status update: CAb546c7046b7ac7ba2397a5fe4e5b68e5 -> completed (duration: 291)
2025-08-01 00:02:39,082 - agent.services.voice_service - INFO - Processing call completion via status webhook: CAb546c7046b7ac7ba2397a5fe4e5b68e5 -> completed
2025-08-01 00:02:39,082 - agent.services.voice_service - INFO - Handling call completion: CAb546c7046b7ac7ba2397a5fe4e5b68e5, status: completed, duration: 291
2025-08-01 00:02:39,082 - agent.core.analysis_engine - INFO - Starting voice analysis for user: 8beb42d4-23b6-4dbf-a3ea-869044eb8881
2025-08-01 00:02:39,082 - agent.core.analysis_engine - ERROR - DeepSeek analysis failed: asyncio.run() cannot be called from a running event loop
2025-08-01 00:02:39,082 - agent.core.analysis_engine - ERROR - Voice analysis failed for user 8beb42d4-23b6-4dbf-a3ea-869044eb8881: DeepSeek analysis failed: asyncio.run() cannot be called from a running event loop
2025-08-01 00:02:39,084 - agent.core.user_manager - INFO - Publishing event user.voice_completed to 0 handlers
2025-08-01 00:02:39,084 - agent.core.user_manager - INFO - Voice call completed for user: 8beb42d4-23b6-4dbf-a3ea-869044eb8881
2025-08-01 00:02:39,087 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-01 00:02:39,087 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 00:02:39,088 INFO sqlalchemy.engine.Engine 
                        UPDATE voice_sessions SET
                            session_status = 'completed',
                            call_duration = $1,
                            analysis_data = $2,
                            completed_at = NOW()
                        WHERE twilio_call_sid = $3
                    
2025-08-01 00:02:39,088 - sqlalchemy.engine.Engine - INFO - 
                        UPDATE voice_sessions SET
                            session_status = 'completed',
                            call_duration = $1,
                            analysis_data = $2,
                            completed_at = NOW()
                        WHERE twilio_call_sid = $3
                    
2025-08-01 00:02:39,088 INFO sqlalchemy.engine.Engine [cached since 11.31s ago] (291, '{"transcript": "Assistant: Hello! Thank you for answering. This is your scheduled voice interview for your dating profile. Are you ready to begin? Ju ... (6129 characters truncated) ... response": "", "stage": "closing"}], "quality_score": 1.1687777777777777, "questions_asked": 9, "responses_received": 16, "call_status": "completed"}', 'CAb546c7046b7ac7ba2397a5fe4e5b68e5')
2025-08-01 00:02:39,088 - sqlalchemy.engine.Engine - INFO - [cached since 11.31s ago] (291, '{"transcript": "Assistant: Hello! Thank you for answering. This is your scheduled voice interview for your dating profile. Are you ready to begin? Ju ... (6129 characters truncated) ... response": "", "stage": "closing"}], "quality_score": 1.1687777777777777, "questions_asked": 9, "responses_received": 16, "call_status": "completed"}', 'CAb546c7046b7ac7ba2397a5fe4e5b68e5')
2025-08-01 00:02:39,092 INFO sqlalchemy.engine.Engine COMMIT
2025-08-01 00:02:39,092 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-01 00:02:39,093 - agent.services.voice_service - INFO - Session marked as completed in database: CAb546c7046b7ac7ba2397a5fe4e5b68e5
2025-08-01 00:02:39,093 - agent.services.voice_service - INFO - Voice session completed in database: CAb546c7046b7ac7ba2397a5fe4e5b68e5
2025-08-01 00:02:39,097 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-01 00:02:39,097 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 00:02:39,097 INFO sqlalchemy.engine.Engine 
                        INSERT INTO users (
                            id, phone_number, email, first_name, last_name, age, city, profession,
                            sms_verified, voice_call_completed, linkedin_verified,
                            verification_status, verification_level, status, created_at, updated_at, last_login
                        ) VALUES (
                            $1, $2, $3, $4, $5, $6, $7, $8,
                            $9, $10, $11,
                            $12, $13, $14, $15, $16, $17
                        )
                        ON CONFLICT (id) DO UPDATE SET
                            phone_number = EXCLUDED.phone_number,
                            email = EXCLUDED.email,
                            first_name = EXCLUDED.first_name,
                            last_name = EXCLUDED.last_name,
                            age = EXCLUDED.age,
                            city = EXCLUDED.city,
                            profession = EXCLUDED.profession,
                            sms_verified = EXCLUDED.sms_verified,
                            voice_call_completed = EXCLUDED.voice_call_completed,
                            linkedin_verified = EXCLUDED.linkedin_verified,
                            verification_status = EXCLUDED.verification_status,
                            verification_level = EXCLUDED.verification_level,
                            status = EXCLUDED.status,
                            updated_at = EXCLUDED.updated_at,
                            last_login = EXCLUDED.last_login
                    
2025-08-01 00:02:39,097 - sqlalchemy.engine.Engine - INFO - 
                        INSERT INTO users (
                            id, phone_number, email, first_name, last_name, age, city, profession,
                            sms_verified, voice_call_completed, linkedin_verified,
                            verification_status, verification_level, status, created_at, updated_at, last_login
                        ) VALUES (
                            $1, $2, $3, $4, $5, $6, $7, $8,
                            $9, $10, $11,
                            $12, $13, $14, $15, $16, $17
                        )
                        ON CONFLICT (id) DO UPDATE SET
                            phone_number = EXCLUDED.phone_number,
                            email = EXCLUDED.email,
                            first_name = EXCLUDED.first_name,
                            last_name = EXCLUDED.last_name,
                            age = EXCLUDED.age,
                            city = EXCLUDED.city,
                            profession = EXCLUDED.profession,
                            sms_verified = EXCLUDED.sms_verified,
                            voice_call_completed = EXCLUDED.voice_call_completed,
                            linkedin_verified = EXCLUDED.linkedin_verified,
                            verification_status = EXCLUDED.verification_status,
                            verification_level = EXCLUDED.verification_level,
                            status = EXCLUDED.status,
                            updated_at = EXCLUDED.updated_at,
                            last_login = EXCLUDED.last_login
                    
2025-08-01 00:02:39,098 INFO sqlalchemy.engine.Engine [cached since 11.32s ago] ('8beb42d4-23b6-4dbf-a3ea-869044eb8881', '+***********', None, 'a', 'a', None, None, None, True, True, False, 'voice_completed', 'partial', 'active', datetime.datetime(2025, 7, 31, 4, 23, 40, 211471, tzinfo=datetime.timezone.utc), datetime.datetime(2025, 8, 1, 0, 2, 39, 93504), datetime.datetime(2025, 7, 31, 4, 23, 40, 211484, tzinfo=datetime.timezone.utc))
2025-08-01 00:02:39,098 - sqlalchemy.engine.Engine - INFO - [cached since 11.32s ago] ('8beb42d4-23b6-4dbf-a3ea-869044eb8881', '+***********', None, 'a', 'a', None, None, None, True, True, False, 'voice_completed', 'partial', 'active', datetime.datetime(2025, 7, 31, 4, 23, 40, 211471, tzinfo=datetime.timezone.utc), datetime.datetime(2025, 8, 1, 0, 2, 39, 93504), datetime.datetime(2025, 7, 31, 4, 23, 40, 211484, tzinfo=datetime.timezone.utc))
2025-08-01 00:02:39,101 INFO sqlalchemy.engine.Engine COMMIT
2025-08-01 00:02:39,101 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-01 00:02:39,102 - agent.services.voice_service - INFO - Updated user status to REGISTERED for user: 8beb42d4-23b6-4dbf-a3ea-869044eb8881
2025-08-01 00:02:39,102 - agent.services.voice_service - INFO - Voice interview completed and analyzed for user: 8beb42d4-23b6-4dbf-a3ea-869044eb8881
INFO:     13.221.183.254:0 - "POST /voice/webhook/status HTTP/1.1" 200 OK