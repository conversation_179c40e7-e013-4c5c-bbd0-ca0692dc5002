INFO:     127.0.0.1:50010 - "OPTIONS /voice/initiate/8beb42d4-23b6-4dbf-a3ea-869044eb8881 HTTP/1.1" 200 OK
2025-07-31 12:24:22,603 - twilio.http_client - INFO - -- BEGIN Twilio API Request --
2025-07-31 12:24:22,604 - twilio.http_client - INFO - POST Request: https://api.twilio.com/2010-04-01/Accounts/**********************************/Calls.json
2025-07-31 12:24:22,604 - twilio.http_client - INFO - Headers:
2025-07-31 12:24:22,604 - twilio.http_client - INFO - Content-Type : application/x-www-form-urlencoded
2025-07-31 12:24:22,604 - twilio.http_client - INFO - Accept : application/json
2025-07-31 12:24:22,604 - twilio.http_client - INFO - User-Agent : twilio-python/9.6.5 (<PERSON> arm64) Python/3.12.2
2025-07-31 12:24:22,604 - twilio.http_client - INFO - X-Twilio-Client : python-9.6.5
2025-07-31 12:24:22,604 - twilio.http_client - INFO - Accept-Charset : utf-8
2025-07-31 12:24:22,604 - twilio.http_client - INFO - -- END Twilio API Request --
2025-07-31 12:24:24,245 - twilio.http_client - INFO - Response Status Code: 201
2025-07-31 12:24:24,245 - twilio.http_client - INFO - Response Headers: {'Content-Type': 'application/json;charset=utf-8', 'Content-Length': '2011', 'Connection': 'keep-alive', 'Date': 'Thu, 31 Jul 2025 04:24:24 GMT', 'Location': 'https://call-service.us1.svc.twilio.com/v2/Accounts/CA4d27074ea0ac0d7f4fa1c036a9814693', 'Twilio-Concurrent-Requests': '1', 'Twilio-Request-Id': 'RQ09d6bce710e955053453da8187b05d2e', 'Twilio-Request-Duration': '0.091', 'X-Home-Region': 'us1', 'X-API-Domain': 'api.twilio.com', 'Strict-Transport-Security': 'max-age=********', 'X-Cache': 'Miss from cloudfront', 'Via': '1.1 a87319e8c7e956cbba29cd5f6931dbee.cloudfront.net (CloudFront)', 'X-Amz-Cf-Pop': 'SFO53-C1', 'X-Amz-Cf-Id': 'QUnC8KQNTbB75q4goUNA-NVxN7Y501d8NChRzuOmejp7vtFa2h-V3w==', 'X-Powered-By': 'AT-5000', 'X-Shenanigans': 'none', 'Vary': 'Origin'}
2025-07-31 12:24:24,245 - agent.services.voice_service - INFO - Outbound call initiated: CA4d27074ea0ac0d7f4fa1c036a9814693 to +***********
2025-07-31 12:24:24,245 - agent.services.voice_service - INFO - Status callback configured: https://e36ed358d667.ngrok-free.app/voice/webhook/status
2025-07-31 12:24:24,245 - agent.services.voice_service - INFO - Status events monitored: ['initiated', 'ringing', 'answered', 'completed', 'busy', 'no-answer', 'failed', 'canceled']
INFO:     127.0.0.1:50010 - "POST /voice/initiate/8beb42d4-23b6-4dbf-a3ea-869044eb8881 HTTP/1.1" 200 OK
2025-07-31 12:24:24,519 - api.voice - INFO - Call status webhook: {'Called': '+***********', 'ToState': 'CA', 'CallerCountry': 'US', 'Direction': 'outbound-api', 'Timestamp': 'Thu, 31 Jul 2025 04:24:24 +0000', 'CallbackSource': 'call-progress-events', 'CallerState': 'CA', 'ToZip': '', 'SequenceNumber': '0', 'CallSid': 'CA4d27074ea0ac0d7f4fa1c036a9814693', 'To': '+***********', 'CallerZip': '', 'ToCountry': 'US', 'CalledZip': '', 'ApiVersion': '2010-04-01', 'CalledCity': '', 'CallStatus': 'initiated', 'From': '+***********', 'AccountSid': '**********************************', 'CalledCountry': 'US', 'CallerCity': 'GUADALUPE', 'ToCity': '', 'FromCountry': 'US', 'Caller': '+***********', 'FromCity': 'GUADALUPE', 'CalledState': 'CA', 'FromZip': '', 'FromState': 'CA'}
2025-07-31 12:24:24,520 - agent.services.voice_service - INFO - Call status update: CA4d27074ea0ac0d7f4fa1c036a9814693 -> initiated (duration: None)
2025-07-31 12:24:24,520 - agent.services.voice_service - INFO - Session not in memory, loading from database: CA4d27074ea0ac0d7f4fa1c036a9814693
2025-07-31 12:24:24,530 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 12:24:24,530 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 12:24:24,531 INFO sqlalchemy.engine.Engine 
                        SELECT session_data, user_id, from_number, current_stage,
                               questions_asked, responses_received, last_activity
                        FROM voice_sessions
                        WHERE twilio_call_sid = $1
                        AND session_status = 'active'
                        AND last_activity > NOW() - INTERVAL '2 hours'
                    
2025-07-31 12:24:24,531 - sqlalchemy.engine.Engine - INFO - 
                        SELECT session_data, user_id, from_number, current_stage,
                               questions_asked, responses_received, last_activity
                        FROM voice_sessions
                        WHERE twilio_call_sid = $1
                        AND session_status = 'active'
                        AND last_activity > NOW() - INTERVAL '2 hours'
                    
2025-07-31 12:24:24,531 INFO sqlalchemy.engine.Engine [generated in 0.00024s] ('CA4d27074ea0ac0d7f4fa1c036a9814693',)
2025-07-31 12:24:24,531 - sqlalchemy.engine.Engine - INFO - [generated in 0.00024s] ('CA4d27074ea0ac0d7f4fa1c036a9814693',)
2025-07-31 12:24:24,535 INFO sqlalchemy.engine.Engine ROLLBACK
2025-07-31 12:24:24,535 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-31 12:24:24,537 - agent.services.voice_service - WARNING - Session not found in memory or database: CA4d27074ea0ac0d7f4fa1c036a9814693
2025-07-31 12:24:24,537 - agent.services.voice_service - WARNING - No session found for status update: CA4d27074ea0ac0d7f4fa1c036a9814693
INFO:     *************:0 - "POST /voice/webhook/status HTTP/1.1" 200 OK
2025-07-31 12:24:25,791 - api.voice - INFO - Call status webhook: {'Called': '+***********', 'ToState': 'CA', 'CallerCountry': 'US', 'Direction': 'outbound-api', 'Timestamp': 'Thu, 31 Jul 2025 04:24:25 +0000', 'CallbackSource': 'call-progress-events', 'CallerState': 'CA', 'ToZip': '', 'SequenceNumber': '1', 'StirStatus': 'B', 'CallSid': 'CA4d27074ea0ac0d7f4fa1c036a9814693', 'To': '+***********', 'CallerZip': '', 'ToCountry': 'US', 'CalledZip': '', 'ApiVersion': '2010-04-01', 'CalledCity': '', 'CallStatus': 'ringing', 'From': '+***********', 'AccountSid': '**********************************', 'CalledCountry': 'US', 'CallerCity': 'GUADALUPE', 'ToCity': '', 'FromCountry': 'US', 'Caller': '+***********', 'FromCity': 'GUADALUPE', 'CalledState': 'CA', 'FromZip': '', 'FromState': 'CA'}
2025-07-31 12:24:25,791 - agent.services.voice_service - INFO - Call status update: CA4d27074ea0ac0d7f4fa1c036a9814693 -> ringing (duration: None)
2025-07-31 12:24:25,791 - agent.services.voice_service - INFO - Session not in memory, loading from database: CA4d27074ea0ac0d7f4fa1c036a9814693
2025-07-31 12:24:25,793 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 12:24:25,793 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 12:24:25,793 INFO sqlalchemy.engine.Engine 
                        SELECT session_data, user_id, from_number, current_stage,
                               questions_asked, responses_received, last_activity
                        FROM voice_sessions
                        WHERE twilio_call_sid = $1
                        AND session_status = 'active'
                        AND last_activity > NOW() - INTERVAL '2 hours'
                    
2025-07-31 12:24:25,793 - sqlalchemy.engine.Engine - INFO - 
                        SELECT session_data, user_id, from_number, current_stage,
                               questions_asked, responses_received, last_activity
                        FROM voice_sessions
                        WHERE twilio_call_sid = $1
                        AND session_status = 'active'
                        AND last_activity > NOW() - INTERVAL '2 hours'
                    
2025-07-31 12:24:25,793 INFO sqlalchemy.engine.Engine [cached since 1.263s ago] ('CA4d27074ea0ac0d7f4fa1c036a9814693',)
2025-07-31 12:24:25,793 - sqlalchemy.engine.Engine - INFO - [cached since 1.263s ago] ('CA4d27074ea0ac0d7f4fa1c036a9814693',)
2025-07-31 12:24:25,794 INFO sqlalchemy.engine.Engine ROLLBACK
2025-07-31 12:24:25,794 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-31 12:24:25,794 - agent.services.voice_service - WARNING - Session not found in memory or database: CA4d27074ea0ac0d7f4fa1c036a9814693
2025-07-31 12:24:25,794 - agent.services.voice_service - WARNING - No session found for status update: CA4d27074ea0ac0d7f4fa1c036a9814693
INFO:     *************:0 - "POST /voice/webhook/status HTTP/1.1" 200 OK
2025-07-31 12:24:28,844 - api.voice - INFO - Call status webhook: {'Called': '+***********', 'ToState': 'CA', 'CallerCountry': 'US', 'Direction': 'outbound-api', 'Timestamp': 'Thu, 31 Jul 2025 04:24:28 +0000', 'CallbackSource': 'call-progress-events', 'CallerState': 'CA', 'ToZip': '', 'SequenceNumber': '2', 'StirStatus': 'B', 'CallSid': 'CA4d27074ea0ac0d7f4fa1c036a9814693', 'To': '+***********', 'CallerZip': '', 'ToCountry': 'US', 'CalledZip': '', 'ApiVersion': '2010-04-01', 'CalledCity': '', 'CallStatus': 'in-progress', 'From': '+***********', 'AccountSid': '**********************************', 'CalledCountry': 'US', 'CallerCity': 'GUADALUPE', 'ToCity': '', 'FromCountry': 'US', 'Caller': '+***********', 'FromCity': 'GUADALUPE', 'CalledState': 'CA', 'FromZip': '', 'FromState': 'CA'}
2025-07-31 12:24:28,844 - agent.services.voice_service - INFO - Call status update: CA4d27074ea0ac0d7f4fa1c036a9814693 -> in-progress (duration: None)
2025-07-31 12:24:28,844 - agent.services.voice_service - INFO - Session not in memory, loading from database: CA4d27074ea0ac0d7f4fa1c036a9814693
2025-07-31 12:24:28,845 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 12:24:28,845 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 12:24:28,846 INFO sqlalchemy.engine.Engine 
                        SELECT session_data, user_id, from_number, current_stage,
                               questions_asked, responses_received, last_activity
                        FROM voice_sessions
                        WHERE twilio_call_sid = $1
                        AND session_status = 'active'
                        AND last_activity > NOW() - INTERVAL '2 hours'
                    
2025-07-31 12:24:28,846 - sqlalchemy.engine.Engine - INFO - 
                        SELECT session_data, user_id, from_number, current_stage,
                               questions_asked, responses_received, last_activity
                        FROM voice_sessions
                        WHERE twilio_call_sid = $1
                        AND session_status = 'active'
                        AND last_activity > NOW() - INTERVAL '2 hours'
                    
2025-07-31 12:24:28,846 INFO sqlalchemy.engine.Engine [cached since 4.315s ago] ('CA4d27074ea0ac0d7f4fa1c036a9814693',)
2025-07-31 12:24:28,846 - sqlalchemy.engine.Engine - INFO - [cached since 4.315s ago] ('CA4d27074ea0ac0d7f4fa1c036a9814693',)
2025-07-31 12:24:28,847 INFO sqlalchemy.engine.Engine ROLLBACK
2025-07-31 12:24:28,847 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-31 12:24:28,848 - agent.services.voice_service - WARNING - Session not found in memory or database: CA4d27074ea0ac0d7f4fa1c036a9814693
2025-07-31 12:24:28,848 - agent.services.voice_service - WARNING - No session found for status update: CA4d27074ea0ac0d7f4fa1c036a9814693
INFO:     *************:0 - "POST /voice/webhook/status HTTP/1.1" 200 OK
2025-07-31 12:24:28,982 - api.voice - INFO - Outgoing call webhook: {'Called': '+***********', 'ToState': 'CA', 'CallerCountry': 'US', 'Direction': 'outbound-api', 'CallerState': 'CA', 'ToZip': '', 'StirStatus': 'B', 'CallSid': 'CA4d27074ea0ac0d7f4fa1c036a9814693', 'To': '+***********', 'CallerZip': '', 'ToCountry': 'US', 'StirVerstat': 'TN-Validation-Passed-B', 'CalledZip': '', 'ApiVersion': '2010-04-01', 'CalledCity': '', 'CallStatus': 'in-progress', 'From': '+***********', 'AccountSid': '**********************************', 'CalledCountry': 'US', 'CallerCity': 'GUADALUPE', 'ToCity': '', 'FromCountry': 'US', 'Caller': '+***********', 'FromCity': 'GUADALUPE', 'CalledState': 'CA', 'FromZip': '', 'FromState': 'CA'}
2025-07-31 12:24:28,984 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 12:24:28,984 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 12:24:28,985 INFO sqlalchemy.engine.Engine SELECT * FROM users WHERE phone_number = $1
2025-07-31 12:24:28,985 - sqlalchemy.engine.Engine - INFO - SELECT * FROM users WHERE phone_number = $1
2025-07-31 12:24:28,985 INFO sqlalchemy.engine.Engine [cached since 48.8s ago] ('+***********',)
2025-07-31 12:24:28,985 - sqlalchemy.engine.Engine - INFO - [cached since 48.8s ago] ('+***********',)
2025-07-31 12:24:28,989 INFO sqlalchemy.engine.Engine ROLLBACK
2025-07-31 12:24:28,989 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-31 12:24:28,995 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 12:24:28,995 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 12:24:28,996 INFO sqlalchemy.engine.Engine 
                        INSERT INTO voice_sessions (
                            id, user_id, twilio_call_sid, session_data, session_status,
                            last_activity, from_number, current_stage, questions_asked,
                            responses_received, created_at
                        ) VALUES (
                            uuid_generate_v4(), $1, $2, $3,
                            'active', NOW(), $4, $5, $6,
                            $7, NOW()
                        )
                        ON CONFLICT (twilio_call_sid) DO UPDATE SET
                            session_data = EXCLUDED.session_data,
                            last_activity = NOW(),
                            current_stage = EXCLUDED.current_stage,
                            questions_asked = EXCLUDED.questions_asked,
                            responses_received = EXCLUDED.responses_received
                    
2025-07-31 12:24:28,996 - sqlalchemy.engine.Engine - INFO - 
                        INSERT INTO voice_sessions (
                            id, user_id, twilio_call_sid, session_data, session_status,
                            last_activity, from_number, current_stage, questions_asked,
                            responses_received, created_at
                        ) VALUES (
                            uuid_generate_v4(), $1, $2, $3,
                            'active', NOW(), $4, $5, $6,
                            $7, NOW()
                        )
                        ON CONFLICT (twilio_call_sid) DO UPDATE SET
                            session_data = EXCLUDED.session_data,
                            last_activity = NOW(),
                            current_stage = EXCLUDED.current_stage,
                            questions_asked = EXCLUDED.questions_asked,
                            responses_received = EXCLUDED.responses_received
                    
2025-07-31 12:24:28,996 INFO sqlalchemy.engine.Engine [generated in 0.00028s] ('8beb42d4-23b6-4dbf-a3ea-869044eb8881', 'CA4d27074ea0ac0d7f4fa1c036a9814693', '{"call_sid": "CA4d27074ea0ac0d7f4fa1c036a9814693", "user_id": "8beb42d4-23b6-4dbf-a3ea-869044eb8881", "from_number": "+***********", "start_time": "2 ... (124 characters truncated) ... ript": [], "collected_info": {}, "last_question": "", "recording_url": null, "recording_duration": 0, "call_status": "initiated", "timeout_count": 0}', '+***********', 'greeting', 0, 0)
2025-07-31 12:24:28,996 - sqlalchemy.engine.Engine - INFO - [generated in 0.00028s] ('8beb42d4-23b6-4dbf-a3ea-869044eb8881', 'CA4d27074ea0ac0d7f4fa1c036a9814693', '{"call_sid": "CA4d27074ea0ac0d7f4fa1c036a9814693", "user_id": "8beb42d4-23b6-4dbf-a3ea-869044eb8881", "from_number": "+***********", "start_time": "2 ... (124 characters truncated) ... ript": [], "collected_info": {}, "last_question": "", "recording_url": null, "recording_duration": 0, "call_status": "initiated", "timeout_count": 0}', '+***********', 'greeting', 0, 0)
2025-07-31 12:24:29,001 INFO sqlalchemy.engine.Engine COMMIT
2025-07-31 12:24:29,001 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-31 12:24:29,004 - agent.services.voice_service - INFO - Session saved to database: CA4d27074ea0ac0d7f4fa1c036a9814693
2025-07-31 12:24:29,006 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 12:24:29,006 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 12:24:29,006 INFO sqlalchemy.engine.Engine 
                        UPDATE voice_sessions SET
                            session_data = $1,
                            last_activity = NOW(),
                            current_stage = $2,
                            questions_asked = $3,
                            responses_received = $4
                        WHERE twilio_call_sid = $5
                        AND session_status = 'active'
                    
2025-07-31 12:24:29,006 - sqlalchemy.engine.Engine - INFO - 
                        UPDATE voice_sessions SET
                            session_data = $1,
                            last_activity = NOW(),
                            current_stage = $2,
                            questions_asked = $3,
                            responses_received = $4
                        WHERE twilio_call_sid = $5
                        AND session_status = 'active'
                    
2025-07-31 12:24:29,007 INFO sqlalchemy.engine.Engine [generated in 0.00019s] ('{"call_sid": "CA4d27074ea0ac0d7f4fa1c036a9814693", "user_id": "8beb42d4-23b6-4dbf-a3ea-869044eb8881", "from_number": "+***********", "start_time": "2 ... (602 characters truncated) ... , and I\'ll ask you a few questions about yourself.", "recording_url": null, "recording_duration": 0, "call_status": "initiated", "timeout_count": 0}', 'greeting', 1, 0, 'CA4d27074ea0ac0d7f4fa1c036a9814693')
2025-07-31 12:24:29,007 - sqlalchemy.engine.Engine - INFO - [generated in 0.00019s] ('{"call_sid": "CA4d27074ea0ac0d7f4fa1c036a9814693", "user_id": "8beb42d4-23b6-4dbf-a3ea-869044eb8881", "from_number": "+***********", "start_time": "2 ... (602 characters truncated) ... , and I\'ll ask you a few questions about yourself.", "recording_url": null, "recording_duration": 0, "call_status": "initiated", "timeout_count": 0}', 'greeting', 1, 0, 'CA4d27074ea0ac0d7f4fa1c036a9814693')
2025-07-31 12:24:29,009 INFO sqlalchemy.engine.Engine COMMIT
2025-07-31 12:24:29,009 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-31 12:24:29,013 - agent.services.voice_service - INFO - Started outgoing voice interview for user 8beb42d4-23b6-4dbf-a3ea-869044eb8881, call CA4d27074ea0ac0d7f4fa1c036a9814693
INFO:     ************:0 - "POST /voice/webhook/outgoing HTTP/1.1" 200 OK
2025-07-31 12:24:50,725 - api.voice - INFO - Speech webhook: {'Called': '+***********', 'ToState': 'CA', 'CallerCountry': 'US', 'Direction': 'outbound-api', 'SpeechResult': "Yes, I'm ready.", 'CallerState': 'CA', 'Language': 'en-US', 'ToZip': '', 'Confidence': '0.9899282', 'CallSid': 'CA4d27074ea0ac0d7f4fa1c036a9814693', 'To': '+***********', 'CallerZip': '', 'ToCountry': 'US', 'CalledZip': '', 'ApiVersion': '2010-04-01', 'CalledCity': '', 'CallStatus': 'in-progress', 'From': '+***********', 'AccountSid': '**********************************', 'CalledCountry': 'US', 'CallerCity': 'GUADALUPE', 'ToCity': '', 'FromCountry': 'US', 'Caller': '+***********', 'FromCity': 'GUADALUPE', 'CalledState': 'CA', 'FromZip': '', 'FromState': 'CA'}
2025-07-31 12:24:50,725 - agent.services.voice_service - INFO - Processing speech input for call: CA4d27074ea0ac0d7f4fa1c036a9814693
2025-07-31 12:24:50,725 - agent.services.voice_service - INFO - User speech: 'Yes, I'm ready.' (confidence: 0.9899282)
2025-07-31 12:24:50,725 - agent.services.voice_service - INFO - Session updated - Questions: 1, Responses: 1
2025-07-31 12:24:50,725 - agent.services.voice_service - INFO - Using ConversationAgent for call: CA4d27074ea0ac0d7f4fa1c036a9814693
2025-07-31 12:24:55,388 - agent.services.ai_service - INFO - AI extracted info: {}
2025-07-31 12:24:55,389 - agent.services.conversation_agent - INFO - AI extracted and merged info: {}
2025-07-31 12:25:00,814 - agent.services.ai_service - INFO - AI emotion analysis: {'engagement': 0.3, 'comfort': 0.7, 'enthusiasm': 0.2}
2025-07-31 12:25:00,815 - agent.services.conversation_agent - INFO - AI emotion analysis updated: {'engagement': 0.36, 'comfort': 0.6399999999999999, 'enthusiasm': 0.29}
2025-07-31 12:25:00,815 - agent.services.ai_service - INFO - Generating AI question for domain: greeting
2025-07-31 12:25:05,910 - agent.services.ai_service - INFO - AI question generated: Great! Let’s start simple—what’s something fun or interesting you’ve been up to lately?
2025-07-31 12:25:05,913 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 12:25:05,913 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 12:25:05,913 INFO sqlalchemy.engine.Engine 
                        INSERT INTO agent_decisions 
                        (call_sid, decision_type, decision_data, reasoning, confidence)
                        VALUES ($1, $2, $3, $4, $5)
                    
2025-07-31 12:25:05,913 - sqlalchemy.engine.Engine - INFO - 
                        INSERT INTO agent_decisions 
                        (call_sid, decision_type, decision_data, reasoning, confidence)
                        VALUES ($1, $2, $3, $4, $5)
                    
2025-07-31 12:25:05,913 INFO sqlalchemy.engine.Engine [generated in 0.00022s] ('CA4d27074ea0ac0d7f4fa1c036a9814693', 'question_generate', '{"action_type": "ask_question", "content": "Great! Let\\u2019s start simple\\u2014what\\u2019s something fun or interesting you\\u2019ve been up to l ... (109 characters truncated) ... tate": {"engagement": 0.36, "comfort": 0.6399999999999999, "enthusiasm": 0.29}, "collected_info_count": 0, "timestamp": "2025-07-31T12:25:05.910534"}', 'AI generated question based on conversation context', 0.8)
2025-07-31 12:25:05,913 - sqlalchemy.engine.Engine - INFO - [generated in 0.00022s] ('CA4d27074ea0ac0d7f4fa1c036a9814693', 'question_generate', '{"action_type": "ask_question", "content": "Great! Let\\u2019s start simple\\u2014what\\u2019s something fun or interesting you\\u2019ve been up to l ... (109 characters truncated) ... tate": {"engagement": 0.36, "comfort": 0.6399999999999999, "enthusiasm": 0.29}, "collected_info_count": 0, "timestamp": "2025-07-31T12:25:05.910534"}', 'AI generated question based on conversation context', 0.8)
2025-07-31 12:25:05,919 INFO sqlalchemy.engine.Engine COMMIT
2025-07-31 12:25:05,919 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-31 12:25:05,926 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 12:25:05,926 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 12:25:05,926 INFO sqlalchemy.engine.Engine 
                    INSERT INTO agent_conversations 
                    (call_sid, user_id, conversation_stage, topic_transitions, emotional_state,
                     engagement_score, conversation_quality, total_exchanges, successful_responses, timeout_count)
                    VALUES ($1, $2, $3, $4, $5,
                     $6, $7, $8, $9, $10)
                    ON CONFLICT (call_sid) DO UPDATE SET
                        conversation_stage = EXCLUDED.conversation_stage,
                        topic_transitions = EXCLUDED.topic_transitions,
                        emotional_state = EXCLUDED.emotional_state,
                        engagement_score = EXCLUDED.engagement_score,
                        conversation_quality = EXCLUDED.conversation_quality,
                        total_exchanges = EXCLUDED.total_exchanges,
                        successful_responses = EXCLUDED.successful_responses,
                        timeout_count = EXCLUDED.timeout_count,
                        updated_at = NOW()
                
2025-07-31 12:25:05,926 - sqlalchemy.engine.Engine - INFO - 
                    INSERT INTO agent_conversations 
                    (call_sid, user_id, conversation_stage, topic_transitions, emotional_state,
                     engagement_score, conversation_quality, total_exchanges, successful_responses, timeout_count)
                    VALUES ($1, $2, $3, $4, $5,
                     $6, $7, $8, $9, $10)
                    ON CONFLICT (call_sid) DO UPDATE SET
                        conversation_stage = EXCLUDED.conversation_stage,
                        topic_transitions = EXCLUDED.topic_transitions,
                        emotional_state = EXCLUDED.emotional_state,
                        engagement_score = EXCLUDED.engagement_score,
                        conversation_quality = EXCLUDED.conversation_quality,
                        total_exchanges = EXCLUDED.total_exchanges,
                        successful_responses = EXCLUDED.successful_responses,
                        timeout_count = EXCLUDED.timeout_count,
                        updated_at = NOW()
                
2025-07-31 12:25:05,927 INFO sqlalchemy.engine.Engine [generated in 0.00029s] ('CA4d27074ea0ac0d7f4fa1c036a9814693', '8beb42d4-23b6-4dbf-a3ea-869044eb8881', 'greeting', '["greeting"]', '{"engagement": 0.36, "comfort": 0.6399999999999999, "enthusiasm": 0.29}', 0.36, 0.42999999999999994, 4, 2, 0)
2025-07-31 12:25:05,927 - sqlalchemy.engine.Engine - INFO - [generated in 0.00029s] ('CA4d27074ea0ac0d7f4fa1c036a9814693', '8beb42d4-23b6-4dbf-a3ea-869044eb8881', 'greeting', '["greeting"]', '{"engagement": 0.36, "comfort": 0.6399999999999999, "enthusiasm": 0.29}', 0.36, 0.42999999999999994, 4, 2, 0)
2025-07-31 12:25:05,931 INFO sqlalchemy.engine.Engine COMMIT
2025-07-31 12:25:05,931 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-31 12:25:05,935 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 12:25:05,935 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 12:25:05,935 INFO sqlalchemy.engine.Engine 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-07-31 12:25:05,935 - sqlalchemy.engine.Engine - INFO - 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-07-31 12:25:05,935 INFO sqlalchemy.engine.Engine [generated in 0.00036s] ('CA4d27074ea0ac0d7f4fa1c036a9814693', 'collect_basic_info', 'active', 0.0, 5, '{"required_fields": ["name", "city"], "collected": 0}')
2025-07-31 12:25:05,935 - sqlalchemy.engine.Engine - INFO - [generated in 0.00036s] ('CA4d27074ea0ac0d7f4fa1c036a9814693', 'collect_basic_info', 'active', 0.0, 5, '{"required_fields": ["name", "city"], "collected": 0}')
2025-07-31 12:25:05,939 INFO sqlalchemy.engine.Engine 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-07-31 12:25:05,939 - sqlalchemy.engine.Engine - INFO - 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-07-31 12:25:05,939 INFO sqlalchemy.engine.Engine [cached since 0.003584s ago] ('CA4d27074ea0ac0d7f4fa1c036a9814693', 'collect_profession', 'active', 0.0, 4, '{"required_fields": ["profession"], "collected": 0}')
2025-07-31 12:25:05,939 - sqlalchemy.engine.Engine - INFO - [cached since 0.003584s ago] ('CA4d27074ea0ac0d7f4fa1c036a9814693', 'collect_profession', 'active', 0.0, 4, '{"required_fields": ["profession"], "collected": 0}')
2025-07-31 12:25:05,940 INFO sqlalchemy.engine.Engine 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-07-31 12:25:05,940 - sqlalchemy.engine.Engine - INFO - 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-07-31 12:25:05,940 INFO sqlalchemy.engine.Engine [cached since 0.00484s ago] ('CA4d27074ea0ac0d7f4fa1c036a9814693', 'collect_personality', 'active', 0.0, 3, '{"required_fields": ["personality_traits"], "collected": 0}')
2025-07-31 12:25:05,940 - sqlalchemy.engine.Engine - INFO - [cached since 0.00484s ago] ('CA4d27074ea0ac0d7f4fa1c036a9814693', 'collect_personality', 'active', 0.0, 3, '{"required_fields": ["personality_traits"], "collected": 0}')
2025-07-31 12:25:05,943 INFO sqlalchemy.engine.Engine 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-07-31 12:25:05,943 - sqlalchemy.engine.Engine - INFO - 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-07-31 12:25:05,943 INFO sqlalchemy.engine.Engine [cached since 0.007964s ago] ('CA4d27074ea0ac0d7f4fa1c036a9814693', 'collect_interests', 'active', 0.0, 2, '{"required_fields": ["interests"], "collected": 0}')
2025-07-31 12:25:05,943 - sqlalchemy.engine.Engine - INFO - [cached since 0.007964s ago] ('CA4d27074ea0ac0d7f4fa1c036a9814693', 'collect_interests', 'active', 0.0, 2, '{"required_fields": ["interests"], "collected": 0}')
2025-07-31 12:25:05,944 INFO sqlalchemy.engine.Engine 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-07-31 12:25:05,944 - sqlalchemy.engine.Engine - INFO - 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-07-31 12:25:05,944 INFO sqlalchemy.engine.Engine [cached since 0.008995s ago] ('CA4d27074ea0ac0d7f4fa1c036a9814693', 'collect_relationships', 'active', 0.0, 1, '{"required_fields": ["relationship_preferences"], "collected": 0}')
2025-07-31 12:25:05,944 - sqlalchemy.engine.Engine - INFO - [cached since 0.008995s ago] ('CA4d27074ea0ac0d7f4fa1c036a9814693', 'collect_relationships', 'active', 0.0, 1, '{"required_fields": ["relationship_preferences"], "collected": 0}')
2025-07-31 12:25:05,945 INFO sqlalchemy.engine.Engine COMMIT
2025-07-31 12:25:05,945 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-31 12:25:05,947 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 12:25:05,947 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 12:25:05,947 INFO sqlalchemy.engine.Engine 
                    INSERT INTO agent_memory 
                    (call_sid, memory_type, memory_data)
                    VALUES ($1, $2, $3)
                    ON CONFLICT (call_sid, memory_type) DO UPDATE SET
                        memory_data = EXCLUDED.memory_data,
                        updated_at = NOW()
                
2025-07-31 12:25:05,947 - sqlalchemy.engine.Engine - INFO - 
                    INSERT INTO agent_memory 
                    (call_sid, memory_type, memory_data)
                    VALUES ($1, $2, $3)
                    ON CONFLICT (call_sid, memory_type) DO UPDATE SET
                        memory_data = EXCLUDED.memory_data,
                        updated_at = NOW()
                
2025-07-31 12:25:05,947 INFO sqlalchemy.engine.Engine [generated in 0.00007s] ('CA4d27074ea0ac0d7f4fa1c036a9814693', 'short_term', '{"recent_exchanges": [{"timestamp": "2025-07-31T12:24:50.725535", "question": "", "response": "Yes, I\'m ready.", "stage": "greeting"}, {"timestamp": ... (321 characters truncated) ... tage": "greeting", "last_user_input": "Yes, I\'m ready.", "emotional_state": {"engagement": 0.36, "comfort": 0.6399999999999999, "enthusiasm": 0.29}}')
2025-07-31 12:25:05,947 - sqlalchemy.engine.Engine - INFO - [generated in 0.00007s] ('CA4d27074ea0ac0d7f4fa1c036a9814693', 'short_term', '{"recent_exchanges": [{"timestamp": "2025-07-31T12:24:50.725535", "question": "", "response": "Yes, I\'m ready.", "stage": "greeting"}, {"timestamp": ... (321 characters truncated) ... tage": "greeting", "last_user_input": "Yes, I\'m ready.", "emotional_state": {"engagement": 0.36, "comfort": 0.6399999999999999, "enthusiasm": 0.29}}')
2025-07-31 12:25:05,950 INFO sqlalchemy.engine.Engine COMMIT
2025-07-31 12:25:05,950 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-31 12:25:05,954 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 12:25:05,954 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 12:25:05,954 INFO sqlalchemy.engine.Engine 
                    INSERT INTO agent_metrics (call_sid, metric_name, metric_value, metric_unit)
                    VALUES ($1, $2, $3, $4)
                
2025-07-31 12:25:05,954 - sqlalchemy.engine.Engine - INFO - 
                    INSERT INTO agent_metrics (call_sid, metric_name, metric_value, metric_unit)
                    VALUES ($1, $2, $3, $4)
                
2025-07-31 12:25:05,954 INFO sqlalchemy.engine.Engine [generated in 0.00021s] ('CA4d27074ea0ac0d7f4fa1c036a9814693', 'decision_confidence', 0.8, 'score')
2025-07-31 12:25:05,954 - sqlalchemy.engine.Engine - INFO - [generated in 0.00021s] ('CA4d27074ea0ac0d7f4fa1c036a9814693', 'decision_confidence', 0.8, 'score')
2025-07-31 12:25:05,958 INFO sqlalchemy.engine.Engine 
                    INSERT INTO agent_metrics (call_sid, metric_name, metric_value, metric_unit)
                    VALUES ($1, $2, $3, $4)
                
2025-07-31 12:25:05,958 - sqlalchemy.engine.Engine - INFO - 
                    INSERT INTO agent_metrics (call_sid, metric_name, metric_value, metric_unit)
                    VALUES ($1, $2, $3, $4)
                
2025-07-31 12:25:05,958 INFO sqlalchemy.engine.Engine [cached since 0.003902s ago] ('CA4d27074ea0ac0d7f4fa1c036a9814693', 'user_engagement', 0.36, 'score')
2025-07-31 12:25:05,958 - sqlalchemy.engine.Engine - INFO - [cached since 0.003902s ago] ('CA4d27074ea0ac0d7f4fa1c036a9814693', 'user_engagement', 0.36, 'score')
2025-07-31 12:25:05,959 INFO sqlalchemy.engine.Engine 
                    INSERT INTO agent_metrics (call_sid, metric_name, metric_value, metric_unit)
                    VALUES ($1, $2, $3, $4)
                
2025-07-31 12:25:05,959 - sqlalchemy.engine.Engine - INFO - 
                    INSERT INTO agent_metrics (call_sid, metric_name, metric_value, metric_unit)
                    VALUES ($1, $2, $3, $4)
                
2025-07-31 12:25:05,959 INFO sqlalchemy.engine.Engine [cached since 0.005035s ago] ('CA4d27074ea0ac0d7f4fa1c036a9814693', 'info_collection_rate', 0.0, 'rate')
2025-07-31 12:25:05,959 - sqlalchemy.engine.Engine - INFO - [cached since 0.005035s ago] ('CA4d27074ea0ac0d7f4fa1c036a9814693', 'info_collection_rate', 0.0, 'rate')
2025-07-31 12:25:05,962 INFO sqlalchemy.engine.Engine COMMIT
2025-07-31 12:25:05,962 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-31 12:25:05,965 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 12:25:05,965 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 12:25:05,966 INFO sqlalchemy.engine.Engine 
                    INSERT INTO agent_strategies 
                    (call_sid, strategy_name, strategy_config, is_active)
                    VALUES ($1, $2, $3, $4)
                    ON CONFLICT (call_sid, strategy_name) DO UPDATE SET
                        strategy_config = EXCLUDED.strategy_config,
                        is_active = EXCLUDED.is_active,
                        updated_at = NOW()
                
2025-07-31 12:25:05,966 - sqlalchemy.engine.Engine - INFO - 
                    INSERT INTO agent_strategies 
                    (call_sid, strategy_name, strategy_config, is_active)
                    VALUES ($1, $2, $3, $4)
                    ON CONFLICT (call_sid, strategy_name) DO UPDATE SET
                        strategy_config = EXCLUDED.strategy_config,
                        is_active = EXCLUDED.is_active,
                        updated_at = NOW()
                
2025-07-31 12:25:05,966 INFO sqlalchemy.engine.Engine [generated in 0.00020s] ('CA4d27074ea0ac0d7f4fa1c036a9814693', 're_engagement', '{"trigger_threshold": 0.4, "tactics": ["passion_questions", "lighter_topics", "enthusiasm_boost"], "current_engagement": 0.36}', True)
2025-07-31 12:25:05,966 - sqlalchemy.engine.Engine - INFO - [generated in 0.00020s] ('CA4d27074ea0ac0d7f4fa1c036a9814693', 're_engagement', '{"trigger_threshold": 0.4, "tactics": ["passion_questions", "lighter_topics", "enthusiasm_boost"], "current_engagement": 0.36}', True)
2025-07-31 12:25:05,969 INFO sqlalchemy.engine.Engine 
                    INSERT INTO agent_strategies 
                    (call_sid, strategy_name, strategy_config, is_active)
                    VALUES ($1, $2, $3, $4)
                    ON CONFLICT (call_sid, strategy_name) DO UPDATE SET
                        strategy_config = EXCLUDED.strategy_config,
                        is_active = EXCLUDED.is_active,
                        updated_at = NOW()
                
2025-07-31 12:25:05,969 - sqlalchemy.engine.Engine - INFO - 
                    INSERT INTO agent_strategies 
                    (call_sid, strategy_name, strategy_config, is_active)
                    VALUES ($1, $2, $3, $4)
                    ON CONFLICT (call_sid, strategy_name) DO UPDATE SET
                        strategy_config = EXCLUDED.strategy_config,
                        is_active = EXCLUDED.is_active,
                        updated_at = NOW()
                
2025-07-31 12:25:05,969 INFO sqlalchemy.engine.Engine [cached since 0.003391s ago] ('CA4d27074ea0ac0d7f4fa1c036a9814693', 'info_collection', '{"missing_fields": ["basic_info", "profession", "personality", "interests", "relationships"], "collection_order": ["basic_info", "profession", "personality", "interests", "relationships"], "current_stage": "greeting"}', True)
2025-07-31 12:25:05,969 - sqlalchemy.engine.Engine - INFO - [cached since 0.003391s ago] ('CA4d27074ea0ac0d7f4fa1c036a9814693', 'info_collection', '{"missing_fields": ["basic_info", "profession", "personality", "interests", "relationships"], "collection_order": ["basic_info", "profession", "personality", "interests", "relationships"], "current_stage": "greeting"}', True)
2025-07-31 12:25:05,970 INFO sqlalchemy.engine.Engine COMMIT
2025-07-31 12:25:05,970 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-31 12:25:05,973 - agent.services.voice_service - INFO - Agent decision: ask_question - Great! Let’s start simple—what’s something fun or interesting you’ve been up to lately? (confidence: 0.8)
2025-07-31 12:25:05,973 - agent.services.voice_service - INFO - Generated next question: 'Great! Let’s start simple—what’s something fun or interesting you’ve been up to lately?'
2025-07-31 12:25:05,975 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 12:25:05,975 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 12:25:05,975 INFO sqlalchemy.engine.Engine 
                        UPDATE voice_sessions SET
                            session_data = $1,
                            last_activity = NOW(),
                            current_stage = $2,
                            questions_asked = $3,
                            responses_received = $4
                        WHERE twilio_call_sid = $5
                        AND session_status = 'active'
                    
2025-07-31 12:25:05,975 - sqlalchemy.engine.Engine - INFO - 
                        UPDATE voice_sessions SET
                            session_data = $1,
                            last_activity = NOW(),
                            current_stage = $2,
                            questions_asked = $3,
                            responses_received = $4
                        WHERE twilio_call_sid = $5
                        AND session_status = 'active'
                    
2025-07-31 12:25:05,976 INFO sqlalchemy.engine.Engine [cached since 36.97s ago] ('{"call_sid": "CA4d27074ea0ac0d7f4fa1c036a9814693", "user_id": "8beb42d4-23b6-4dbf-a3ea-869044eb8881", "from_number": "+***********", "start_time": "2 ... (959 characters truncated) ...  fun or interesting you\\u2019ve been up to lately?", "recording_url": null, "recording_duration": 0, "call_status": "initiated", "timeout_count": 0}', 'greeting', 2, 2, 'CA4d27074ea0ac0d7f4fa1c036a9814693')
2025-07-31 12:25:05,976 - sqlalchemy.engine.Engine - INFO - [cached since 36.97s ago] ('{"call_sid": "CA4d27074ea0ac0d7f4fa1c036a9814693", "user_id": "8beb42d4-23b6-4dbf-a3ea-869044eb8881", "from_number": "+***********", "start_time": "2 ... (959 characters truncated) ...  fun or interesting you\\u2019ve been up to lately?", "recording_url": null, "recording_duration": 0, "call_status": "initiated", "timeout_count": 0}', 'greeting', 2, 2, 'CA4d27074ea0ac0d7f4fa1c036a9814693')
2025-07-31 12:25:05,977 INFO sqlalchemy.engine.Engine COMMIT
2025-07-31 12:25:05,977 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-31 12:25:05,978 - agent.services.voice_service - INFO - Returning TwiML for next question
INFO:     **********:0 - "POST /voice/webhook/speech HTTP/1.1" 200 OK
2025-07-31 12:25:10,030 - api.voice - INFO - Call status webhook: {'Called': '+***********', 'ToState': 'CA', 'CallerCountry': 'US', 'Direction': 'outbound-api', 'Timestamp': 'Thu, 31 Jul 2025 04:25:09 +0000', 'CallbackSource': 'call-progress-events', 'SipResponseCode': '200', 'CallerState': 'CA', 'ToZip': '', 'SequenceNumber': '3', 'CallSid': 'CA4d27074ea0ac0d7f4fa1c036a9814693', 'To': '+***********', 'CallerZip': '', 'ToCountry': 'US', 'CalledZip': '', 'ApiVersion': '2010-04-01', 'CalledCity': '', 'CallStatus': 'completed', 'Duration': '1', 'From': '+***********', 'CallDuration': '41', 'AccountSid': '**********************************', 'CalledCountry': 'US', 'CallerCity': 'GUADALUPE', 'ToCity': '', 'FromCountry': 'US', 'Caller': '+***********', 'FromCity': 'GUADALUPE', 'CalledState': 'CA', 'FromZip': '', 'FromState': 'CA'}
2025-07-31 12:25:10,030 - agent.services.voice_service - INFO - Call status update: CA4d27074ea0ac0d7f4fa1c036a9814693 -> completed (duration: 41)
2025-07-31 12:25:10,030 - agent.services.voice_service - INFO - Processing call completion via status webhook: CA4d27074ea0ac0d7f4fa1c036a9814693 -> completed
2025-07-31 12:25:10,030 - agent.services.voice_service - INFO - Handling call completion: CA4d27074ea0ac0d7f4fa1c036a9814693, status: completed, duration: 41
2025-07-31 12:25:10,030 - agent.services.voice_service - WARNING - Insufficient responses for analysis: 8beb42d4-23b6-4dbf-a3ea-869044eb8881
2025-07-31 12:25:10,032 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 12:25:10,032 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 12:25:10,033 INFO sqlalchemy.engine.Engine 
                        UPDATE voice_sessions SET
                            session_status = 'completed',
                            call_duration = $1,
                            analysis_data = $2,
                            completed_at = NOW()
                        WHERE twilio_call_sid = $3
                    
2025-07-31 12:25:10,033 - sqlalchemy.engine.Engine - INFO - 
                        UPDATE voice_sessions SET
                            session_status = 'completed',
                            call_duration = $1,
                            analysis_data = $2,
                            completed_at = NOW()
                        WHERE twilio_call_sid = $3
                    
2025-07-31 12:25:10,033 INFO sqlalchemy.engine.Engine [generated in 0.00018s] (41, '{"transcript": "Assistant: Hello! Thank you for answering. This is your scheduled voice interview for your dating profile. Are you ready to begin? Ju ... (1108 characters truncated) ... core": 0.45766666666666667, "questions_asked": 2, "responses_received": 2, "call_status": "completed", "completion_status": "insufficient_responses"}', 'CA4d27074ea0ac0d7f4fa1c036a9814693')
2025-07-31 12:25:10,033 - sqlalchemy.engine.Engine - INFO - [generated in 0.00018s] (41, '{"transcript": "Assistant: Hello! Thank you for answering. This is your scheduled voice interview for your dating profile. Are you ready to begin? Ju ... (1108 characters truncated) ... core": 0.45766666666666667, "questions_asked": 2, "responses_received": 2, "call_status": "completed", "completion_status": "insufficient_responses"}', 'CA4d27074ea0ac0d7f4fa1c036a9814693')
2025-07-31 12:25:10,036 INFO sqlalchemy.engine.Engine COMMIT
2025-07-31 12:25:10,036 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-31 12:25:10,037 - agent.services.voice_service - INFO - Partial voice session saved to database: CA4d27074ea0ac0d7f4fa1c036a9814693
INFO:     ***********:0 - "POST /voice/webhook/status HTTP/1.1" 200 OK
2025-07-31 12:29:24,755 - database.connection - INFO - Database engine created: postgresql://dating_app_user:dating_app_password@localhost:5432/dating_app_db
2025-07-31 12:29:24,859 INFO sqlalchemy.engine.Engine select pg_catalog.version()
2025-07-31 12:29:24,859 - sqlalchemy.engine.Engine - INFO - select pg_catalog.version()
2025-07-31 12:29:24,859 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-31 12:29:24,859 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-07-31 12:29:24,861 INFO sqlalchemy.engine.Engine select current_schema()
2025-07-31 12:29:24,861 - sqlalchemy.engine.Engine - INFO - select current_schema()
2025-07-31 12:29:24,861 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-31 12:29:24,861 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-07-31 12:29:24,866 INFO sqlalchemy.engine.Engine show standard_conforming_strings
2025-07-31 12:29:24,866 - sqlalchemy.engine.Engine - INFO - show standard_conforming_strings
2025-07-31 12:29:24,866 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-07-31 12:29:24,866 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-07-31 12:29:24,868 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 12:29:24,868 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 12:29:24,868 INFO sqlalchemy.engine.Engine SELECT 1 as test
2025-07-31 12:29:24,868 - sqlalchemy.engine.Engine - INFO - SELECT 1 as test
2025-07-31 12:29:24,868 INFO sqlalchemy.engine.Engine [generated in 0.00012s] {}
2025-07-31 12:29:24,868 - sqlalchemy.engine.Engine - INFO - [generated in 0.00012s] {}
2025-07-31 12:29:24,873 INFO sqlalchemy.engine.Engine ROLLBACK
2025-07-31 12:29:24,873 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-31 12:29:24,874 - database.connection - INFO - Async database engine created: postgresql+asyncpg://dating_app_user:dating_app_password@localhost:5432/dating_app_db
2025-07-31 12:29:24,898 INFO sqlalchemy.engine.Engine select pg_catalog.version()
2025-07-31 12:29:24,898 - sqlalchemy.engine.Engine - INFO - select pg_catalog.version()
2025-07-31 12:29:24,898 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-31 12:29:24,898 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-31 12:29:24,900 INFO sqlalchemy.engine.Engine select current_schema()
2025-07-31 12:29:24,900 - sqlalchemy.engine.Engine - INFO - select current_schema()
2025-07-31 12:29:24,900 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-31 12:29:24,900 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-31 12:29:24,902 INFO sqlalchemy.engine.Engine show standard_conforming_strings
2025-07-31 12:29:24,902 - sqlalchemy.engine.Engine - INFO - show standard_conforming_strings
2025-07-31 12:29:24,902 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-31 12:29:24,902 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-31 12:29:24,903 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-31 12:29:24,903 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 12:29:24,904 INFO sqlalchemy.engine.Engine SELECT 1 as test
2025-07-31 12:29:24,904 - sqlalchemy.engine.Engine - INFO - SELECT 1 as test
2025-07-31 12:29:24,904 INFO sqlalchemy.engine.Engine [generated in 0.00015s] ()
2025-07-31 12:29:24,904 - sqlalchemy.engine.Engine - INFO - [generated in 0.00015s] ()
2025-07-31 12:29:24,905 INFO sqlalchemy.engine.Engine COMMIT
2025-07-31 12:29:24,905 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-31 12:29:24,910 - twilio.http_client - INFO - -- BEGIN Twilio API Request --
2025-07-31 12:29:24,910 - twilio.http_client - INFO - GET Request: https://api.twilio.com/2010-04-01/Accounts/**********************************.json
2025-07-31 12:29:24,910 - twilio.http_client - INFO - Headers:
2025-07-31 12:29:24,910 - twilio.http_client - INFO - Accept : application/json
2025-07-31 12:29:24,910 - twilio.http_client - INFO - User-Agent : twilio-python/9.6.5 (Darwin arm64) Python/3.12.2
2025-07-31 12:29:24,910 - twilio.http_client - INFO - X-Twilio-Client : python-9.6.5
2025-07-31 12:29:24,910 - twilio.http_client - INFO - Accept-Charset : utf-8
2025-07-31 12:29:24,910 - twilio.http_client - INFO - -- END Twilio API Request --
2025-07-31 12:29:25,707 - twilio.http_client - INFO - Response Status Code: 200
2025-07-31 12:29:25,707 - twilio.http_client - INFO - Response Headers: {'Content-Type': 'application/json;charset=utf-8', 'Content-Length': '538', 'Connection': 'keep-alive', 'Date': 'Thu, 31 Jul 2025 04:29:25 GMT', 'Content-Encoding': 'gzip', 'ETag': 'ec9d41fc8af2c3874b9dfb62da11c612--gzip', 'Last-Modified': 'Thu, 24 Jul 2025 09:52:41 +0000', 'Twilio-Concurrent-Requests': '1', 'Twilio-Request-Id': 'RQ42be29a17525f40a04f3c856157cb608', 'Twilio-Request-Duration': '0.033', 'X-Home-Region': 'us1', 'X-API-Domain': 'api.twilio.com', 'Strict-Transport-Security': 'max-age=********', 'Vary': 'Accept-Encoding, Origin', 'X-Cache': 'Miss from cloudfront', 'Via': '1.1 cf0406bc67043378998de4d2ebe1b638.cloudfront.net (CloudFront)', 'X-Amz-Cf-Pop': 'LAX50-P3', 'X-Amz-Cf-Id': 'gSFPneloVF2ZNKoWayP8EdWtKe9_nCpxfTeEnZlB1fsKbW2DGmVWng==', 'X-Powered-By': 'AT-5000', 'X-Shenanigans': 'none'}
2025-07-31 12:29:25,707 - twilio.http_client - INFO - -- BEGIN Twilio API Request --
2025-07-31 12:29:25,707 - twilio.http_client - INFO - GET Request: https://api.twilio.com/2010-04-01/Accounts/**********************************.json
2025-07-31 12:29:25,707 - twilio.http_client - INFO - Headers:
2025-07-31 12:29:25,707 - twilio.http_client - INFO - Accept : application/json
2025-07-31 12:29:25,707 - twilio.http_client - INFO - User-Agent : twilio-python/9.6.5 (Darwin arm64) Python/3.12.2
2025-07-31 12:29:25,707 - twilio.http_client - INFO - X-Twilio-Client : python-9.6.5
2025-07-31 12:29:25,707 - twilio.http_client - INFO - Accept-Charset : utf-8
2025-07-31 12:29:25,707 - twilio.http_client - INFO - -- END Twilio API Request --
2025-07-31 12:29:26,091 - twilio.http_client - INFO - Response Status Code: 200
2025-07-31 12:29:26,091 - twilio.http_client - INFO - Response Headers: {'Content-Type': 'application/json;charset=utf-8', 'Content-Length': '538', 'Connection': 'keep-alive', 'Date': 'Thu, 31 Jul 2025 04:29:26 GMT', 'Content-Encoding': 'gzip', 'ETag': 'ec9d41fc8af2c3874b9dfb62da11c612--gzip', 'Last-Modified': 'Thu, 24 Jul 2025 09:52:41 +0000', 'Twilio-Concurrent-Requests': '1', 'Twilio-Request-Id': 'RQ1b58d478a770641885fa4fb0253f4c9a', 'Twilio-Request-Duration': '0.022', 'X-Home-Region': 'us1', 'X-API-Domain': 'api.twilio.com', 'Strict-Transport-Security': 'max-age=********', 'Vary': 'Accept-Encoding, Origin', 'X-Cache': 'Miss from cloudfront', 'Via': '1.1 cf0406bc67043378998de4d2ebe1b638.cloudfront.net (CloudFront)', 'X-Amz-Cf-Pop': 'LAX50-P3', 'X-Amz-Cf-Id': 'uAIQtuDu1K-rehM3-FvirOnyJyM5AMy2gVlQnZxTXtbOjgsHBV1lhQ==', 'X-Powered-By': 'AT-5000', 'X-Shenanigans': 'none'}
INFO:     *************:0 - "GET /health HTTP/1.1" 200 OK
