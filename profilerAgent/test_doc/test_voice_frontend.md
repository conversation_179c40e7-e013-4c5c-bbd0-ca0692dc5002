# Voice Frontend 测试指南

## 修复内容总结

### ✅ 已修复的问题
1. **移除了不存在的API调用**
   - 删除了 `testRetryInterview` (调用不存在的 `/voice/retry` API)
   - 替换为 `testHealthCheck` (调用 `/health` API)

2. **改进了用户体验**
   - 添加了预设测试用户ID按钮
   - 改进了响应显示格式
   - 添加了时间戳和API调用记录
   - 更好的错误显示

3. **增强了调试功能**
   - 显示HTTP状态码
   - 显示API调用路径
   - 显示调用时间戳
   - 清空响应按钮

## 测试步骤

### 1. 启动后端服务
```bash
cd profilerAgent/backend
python -m uvicorn main:app --reload --port 8000
```

### 2. 启动前端服务
```bash
cd profilerAgent/frontend
npm run dev
```

### 3. 访问Voice测试页面
```
http://localhost:3000/voice
```

### 4. 测试流程

#### 步骤1: 健康检查
- 点击"健康检查"按钮
- 确认后端服务正常运行
- 应该返回200状态码

#### 步骤2: 测试用户ID
- 点击"测试用户1"按钮设置用户ID为 `test_user_001`
- 或手动输入用户ID

#### 步骤3: 检查语音状态
- 点击"检查语音状态"按钮
- 查看用户的语音通话状态

#### 步骤4: 发起语音通话
- 点击"发起语音通话"按钮
- 这会调用Twilio API发起outgoing call
- 查看返回的通话信息

#### 步骤5: 获取语音分析
- 点击"获取语音分析"按钮
- 查看AI分析结果（如果已完成语音面试）

## 预期结果

### 健康检查 (GET /api/v1/health)
```json
{
  "status": "healthy",
  "version": "1.0.0",
  "timestamp": "2024-01-01T12:00:00Z",
  "services": {
    "agent": "running",
    "database": "connected",
    "redis": "connected"
  }
}
```

### 语音状态 (GET /api/v1/voice/status/{user_id})
```json
{
  "success": true,
  "data": {
    "user_id": "test_user_001",
    "voice_completed": false,
    "current_stage": "sms_verified",
    "call_status": {
      "status": "not_started",
      "message": "No voice call session found for this user"
    }
  }
}
```

### 发起通话 (POST /api/v1/voice/initiate/{user_id})
```json
{
  "success": true,
  "data": {
    "call_sid": "CA**********abcdef",
    "user_id": "test_user_001",
    "phone_number": "+**********",
    "twilio_number": "+**********",
    "call_type": "outgoing",
    "status": "initiated"
  }
}
```

## 可能的错误

### 用户不存在
```json
{
  "detail": "User not found"
}
```

### SMS未验证
```json
{
  "detail": "SMS verification must be completed before voice interview"
}
```

### 语音已完成
```json
{
  "detail": "Voice interview already completed"
}
```

## 调试提示

1. **检查控制台日志** - 查看浏览器开发者工具的Console
2. **检查网络请求** - 查看Network标签页的API调用
3. **检查后端日志** - 查看FastAPI服务器的输出
4. **使用清空响应** - 清除之前的测试结果

## 下一步测试

完成前端测试后，可以进行：
1. **实际语音通话测试** - 使用真实电话号码
2. **Webhook测试** - 模拟Twilio webhook调用
3. **端到端测试** - 完整的用户注册到语音分析流程
