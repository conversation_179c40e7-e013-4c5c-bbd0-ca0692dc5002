                    ON CONFLICT (call_sid, strategy_name) DO UPDATE SET
                        strategy_config = EXCLUDED.strategy_config,
                        is_active = EXCLUDED.is_active,
                        updated_at = NOW()
                
2025-08-02 00:47:03,145 - sqlalchemy.engine.Engine - INFO - 
                    INSERT INTO agent_strategies 
                    (call_sid, strategy_name, strategy_config, is_active)
                    VALUES ($1, $2, $3, $4)
                    ON CONFLICT (call_sid, strategy_name) DO UPDATE SET
                        strategy_config = EXCLUDED.strategy_config,
                        is_active = EXCLUDED.is_active,
                        updated_at = NOW()
                
2025-08-02 00:47:03,145 INFO sqlalchemy.engine.Engine [cached since 52.89s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066821', 'info_collection', '{"missing_fields": ["basic_info", "profession", "personality", "interests", "relationships"], "collection_order": ["basic_info", "profession", "personality", "interests", "relationships"], "current_stage": "greeting"}', True)
2025-08-02 00:47:03,145 - sqlalchemy.engine.Engine - INFO - [cached since 52.89s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066821', 'info_collection', '{"missing_fields": ["basic_info", "profession", "personality", "interests", "relationships"], "collection_order": ["basic_info", "profession", "personality", "interests", "relationships"], "current_stage": "greeting"}', True)
2025-08-02 00:47:03,146 INFO sqlalchemy.engine.Engine COMMIT
2025-08-02 00:47:03,146 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-02 00:47:03,147 - agent.services.conversation_agent - INFO - ⏱️  save_agent_data completed in 0.042s
2025-08-02 00:47:14,810 - api.conversation_relay - INFO - Received ConversationRelay message: prompt
2025-08-02 00:47:14,811 - api.conversation_relay - INFO - Processing user input:  (confidence: 1.0)
2025-08-02 00:47:14,812 - agent.services.voice_service - INFO - Processing ConversationRelay input for user 8beb42d4-23b6-4dbf-a3ea-869044eb8881: ''
2025-08-02 00:47:14,812 - agent.services.voice_service - INFO - Session not in memory, loading from database: relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066834
2025-08-02 00:47:14,817 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-02 00:47:14,817 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-02 00:47:14,818 INFO sqlalchemy.engine.Engine 
                        SELECT session_data, user_id, from_number, current_stage,
                               questions_asked, responses_received, last_activity
                        FROM voice_sessions
                        WHERE twilio_call_sid = $1
                        AND session_status = 'active'
                        AND last_activity > NOW() - INTERVAL '2 hours'
                    
2025-08-02 00:47:14,818 - sqlalchemy.engine.Engine - INFO - 
                        SELECT session_data, user_id, from_number, current_stage,
                               questions_asked, responses_received, last_activity
                        FROM voice_sessions
                        WHERE twilio_call_sid = $1
                        AND session_status = 'active'
                        AND last_activity > NOW() - INTERVAL '2 hours'
                    
2025-08-02 00:47:14,818 INFO sqlalchemy.engine.Engine [cached since 68.33s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066834',)
2025-08-02 00:47:14,818 - sqlalchemy.engine.Engine - INFO - [cached since 68.33s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066834',)
2025-08-02 00:47:14,820 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-02 00:47:14,820 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-02 00:47:14,821 - agent.services.voice_service - WARNING - Session not found in memory or database: relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066834
2025-08-02 00:47:14,822 - agent.core.database - ERROR - Failed to save active session: Object of type UUID is not JSON serializable
2025-08-02 00:47:14,822 - agent.services.voice_service - WARNING - Failed to save session to database: relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066834
2025-08-02 00:47:14,822 - agent.services.voice_service - INFO - Using ConversationAgent for call: relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066834
2025-08-02 00:47:14,822 - agent.services.ai_service - INFO - Generating AI question for domain: greeting
2025-08-02 00:47:17,119 - agent.services.ai_service - INFO - AI question generated: What’s something you enjoy doing in your free time that always brings you joy?
2025-08-02 00:47:17,120 - agent.services.conversation_agent - INFO - ⏱️  ai_decision completed in 2.298s
2025-08-02 00:47:17,120 - agent.services.conversation_agent - INFO - ⏱️  make_smart_decision completed in 2.298s
2025-08-02 00:47:17,120 - agent.services.conversation_agent - INFO - ⏱️  process_input completed in 2.298s
2025-08-02 00:47:17,120 - agent.services.voice_service - INFO - Agent decision: ask_question - What’s something you enjoy doing in your free time that always brings you joy? (confidence: 0.8)
2025-08-02 00:47:17,121 - agent.core.database - ERROR - Failed to update session activity: Object of type UUID is not JSON serializable
2025-08-02 00:47:17,121 - api.conversation_relay - INFO - Received ConversationRelay message: interrupt
2025-08-02 00:47:17,121 - api.conversation_relay - INFO - User interrupted in session: VXc04cb73dc5f4dc6acd4fcce5264c2b67
2025-08-02 00:47:17,124 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-02 00:47:17,124 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-02 00:47:17,124 INFO sqlalchemy.engine.Engine 
                        INSERT INTO agent_decisions 
                        (call_sid, decision_type, decision_data, reasoning, confidence)
                        VALUES ($1, $2, $3, $4, $5)
                    
2025-08-02 00:47:17,124 - sqlalchemy.engine.Engine - INFO - 
                        INSERT INTO agent_decisions 
                        (call_sid, decision_type, decision_data, reasoning, confidence)
                        VALUES ($1, $2, $3, $4, $5)
                    
2025-08-02 00:47:17,124 INFO sqlalchemy.engine.Engine [cached since 66.91s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066834', 'question_generate', '{"action_type": "ask_question", "content": "What\\u2019s something you enjoy doing in your free time that always brings you joy?", "user_input": "",  ... (49 characters truncated) ... ull, "emotional_state": {"engagement": 0.5, "comfort": 0.5, "enthusiasm": 0.5}, "collected_info_count": 0, "timestamp": "2025-08-02T00:47:17.120973"}', 'AI generated question based on conversation context', 0.8)
2025-08-02 00:47:17,124 - sqlalchemy.engine.Engine - INFO - [cached since 66.91s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066834', 'question_generate', '{"action_type": "ask_question", "content": "What\\u2019s something you enjoy doing in your free time that always brings you joy?", "user_input": "",  ... (49 characters truncated) ... ull, "emotional_state": {"engagement": 0.5, "comfort": 0.5, "enthusiasm": 0.5}, "collected_info_count": 0, "timestamp": "2025-08-02T00:47:17.120973"}', 'AI generated question based on conversation context', 0.8)
2025-08-02 00:47:17,128 INFO sqlalchemy.engine.Engine COMMIT
2025-08-02 00:47:17,128 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-02 00:47:17,135 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-02 00:47:17,135 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-02 00:47:17,136 INFO sqlalchemy.engine.Engine 
                    INSERT INTO agent_conversations 
                    (call_sid, user_id, conversation_stage, topic_transitions, emotional_state,
                     engagement_score, conversation_quality, total_exchanges, successful_responses, timeout_count)
                    VALUES ($1, $2, $3, $4, $5,
                     $6, $7, $8, $9, $10)
                    ON CONFLICT (call_sid) DO UPDATE SET
                        conversation_stage = EXCLUDED.conversation_stage,
                        topic_transitions = EXCLUDED.topic_transitions,
                        emotional_state = EXCLUDED.emotional_state,
                        engagement_score = EXCLUDED.engagement_score,
                        conversation_quality = EXCLUDED.conversation_quality,
                        total_exchanges = EXCLUDED.total_exchanges,
                        successful_responses = EXCLUDED.successful_responses,
                        timeout_count = EXCLUDED.timeout_count,
                        updated_at = NOW()
                
2025-08-02 00:47:17,136 - sqlalchemy.engine.Engine - INFO - 
                    INSERT INTO agent_conversations 
                    (call_sid, user_id, conversation_stage, topic_transitions, emotional_state,
                     engagement_score, conversation_quality, total_exchanges, successful_responses, timeout_count)
                    VALUES ($1, $2, $3, $4, $5,
                     $6, $7, $8, $9, $10)
                    ON CONFLICT (call_sid) DO UPDATE SET
                        conversation_stage = EXCLUDED.conversation_stage,
                        topic_transitions = EXCLUDED.topic_transitions,
                        emotional_state = EXCLUDED.emotional_state,
                        engagement_score = EXCLUDED.engagement_score,
                        conversation_quality = EXCLUDED.conversation_quality,
                        total_exchanges = EXCLUDED.total_exchanges,
                        successful_responses = EXCLUDED.successful_responses,
                        timeout_count = EXCLUDED.timeout_count,
                        updated_at = NOW()
                
2025-08-02 00:47:17,136 INFO sqlalchemy.engine.Engine [cached since 66.91s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066834', UUID('8beb42d4-23b6-4dbf-a3ea-869044eb8881'), 'greeting', '["greeting"]', '{"engagement": 0.5, "comfort": 0.5, "enthusiasm": 0.5}', 0.5, 0.5, 2, 0, 0)
2025-08-02 00:47:17,136 - sqlalchemy.engine.Engine - INFO - [cached since 66.91s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066834', UUID('8beb42d4-23b6-4dbf-a3ea-869044eb8881'), 'greeting', '["greeting"]', '{"engagement": 0.5, "comfort": 0.5, "enthusiasm": 0.5}', 0.5, 0.5, 2, 0, 0)
2025-08-02 00:47:17,140 INFO sqlalchemy.engine.Engine COMMIT
2025-08-02 00:47:17,140 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-02 00:47:17,145 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-02 00:47:17,145 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-02 00:47:17,145 INFO sqlalchemy.engine.Engine 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-08-02 00:47:17,145 - sqlalchemy.engine.Engine - INFO - 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-08-02 00:47:17,145 INFO sqlalchemy.engine.Engine [cached since 66.91s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066834', 'collect_basic_info', 'active', 0.0, 5, '{"required_fields": ["name", "city"], "collected": 0}')
2025-08-02 00:47:17,145 - sqlalchemy.engine.Engine - INFO - [cached since 66.91s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066834', 'collect_basic_info', 'active', 0.0, 5, '{"required_fields": ["name", "city"], "collected": 0}')
2025-08-02 00:47:17,148 INFO sqlalchemy.engine.Engine 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-08-02 00:47:17,148 - sqlalchemy.engine.Engine - INFO - 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-08-02 00:47:17,148 INFO sqlalchemy.engine.Engine [cached since 66.92s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066834', 'collect_profession', 'active', 0.0, 4, '{"required_fields": ["profession"], "collected": 0}')
2025-08-02 00:47:17,148 - sqlalchemy.engine.Engine - INFO - [cached since 66.92s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066834', 'collect_profession', 'active', 0.0, 4, '{"required_fields": ["profession"], "collected": 0}')
2025-08-02 00:47:17,150 INFO sqlalchemy.engine.Engine 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-08-02 00:47:17,150 - sqlalchemy.engine.Engine - INFO - 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-08-02 00:47:17,150 INFO sqlalchemy.engine.Engine [cached since 66.92s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066834', 'collect_personality', 'active', 0.0, 3, '{"required_fields": ["personality_traits"], "collected": 0}')
2025-08-02 00:47:17,150 - sqlalchemy.engine.Engine - INFO - [cached since 66.92s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066834', 'collect_personality', 'active', 0.0, 3, '{"required_fields": ["personality_traits"], "collected": 0}')
2025-08-02 00:47:17,152 INFO sqlalchemy.engine.Engine 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-08-02 00:47:17,152 - sqlalchemy.engine.Engine - INFO - 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-08-02 00:47:17,153 INFO sqlalchemy.engine.Engine [cached since 66.92s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066834', 'collect_interests', 'active', 0.0, 2, '{"required_fields": ["interests"], "collected": 0}')
2025-08-02 00:47:17,153 - sqlalchemy.engine.Engine - INFO - [cached since 66.92s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066834', 'collect_interests', 'active', 0.0, 2, '{"required_fields": ["interests"], "collected": 0}')
2025-08-02 00:47:17,159 INFO sqlalchemy.engine.Engine 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-08-02 00:47:17,159 - sqlalchemy.engine.Engine - INFO - 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-08-02 00:47:17,159 INFO sqlalchemy.engine.Engine [cached since 66.93s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066834', 'collect_relationships', 'active', 0.0, 1, '{"required_fields": ["relationship_preferences"], "collected": 0}')
2025-08-02 00:47:17,159 - sqlalchemy.engine.Engine - INFO - [cached since 66.93s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066834', 'collect_relationships', 'active', 0.0, 1, '{"required_fields": ["relationship_preferences"], "collected": 0}')
2025-08-02 00:47:17,160 INFO sqlalchemy.engine.Engine COMMIT
2025-08-02 00:47:17,160 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-02 00:47:17,165 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-02 00:47:17,165 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-02 00:47:17,165 INFO sqlalchemy.engine.Engine 
                    INSERT INTO agent_memory 
                    (call_sid, memory_type, memory_data)
                    VALUES ($1, $2, $3)
                    ON CONFLICT (call_sid, memory_type) DO UPDATE SET
                        memory_data = EXCLUDED.memory_data,
                        updated_at = NOW()
                
2025-08-02 00:47:17,165 - sqlalchemy.engine.Engine - INFO - 
                    INSERT INTO agent_memory 
                    (call_sid, memory_type, memory_data)
                    VALUES ($1, $2, $3)
                    ON CONFLICT (call_sid, memory_type) DO UPDATE SET
                        memory_data = EXCLUDED.memory_data,
                        updated_at = NOW()
                
2025-08-02 00:47:17,165 INFO sqlalchemy.engine.Engine [cached since 66.92s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066834', 'short_term', '{"recent_exchanges": [{"timestamp": "2025-08-02T00:47:14.822146", "question": "", "response": "", "stage": "greeting"}, {"timestamp": "2025-08-02T00: ... (131 characters truncated) ... "stage": "greeting"}], "current_stage": "greeting", "last_user_input": "", "emotional_state": {"engagement": 0.5, "comfort": 0.5, "enthusiasm": 0.5}}')
2025-08-02 00:47:17,165 - sqlalchemy.engine.Engine - INFO - [cached since 66.92s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066834', 'short_term', '{"recent_exchanges": [{"timestamp": "2025-08-02T00:47:14.822146", "question": "", "response": "", "stage": "greeting"}, {"timestamp": "2025-08-02T00: ... (131 characters truncated) ... "stage": "greeting"}], "current_stage": "greeting", "last_user_input": "", "emotional_state": {"engagement": 0.5, "comfort": 0.5, "enthusiasm": 0.5}}')
2025-08-02 00:47:17,167 INFO sqlalchemy.engine.Engine COMMIT
2025-08-02 00:47:17,167 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-02 00:47:17,171 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-02 00:47:17,171 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-02 00:47:17,171 INFO sqlalchemy.engine.Engine 
                    INSERT INTO agent_metrics (call_sid, metric_name, metric_value, metric_unit)
                    VALUES ($1, $2, $3, $4)
                
2025-08-02 00:47:17,171 - sqlalchemy.engine.Engine - INFO - 
                    INSERT INTO agent_metrics (call_sid, metric_name, metric_value, metric_unit)
                    VALUES ($1, $2, $3, $4)
                
2025-08-02 00:47:17,171 INFO sqlalchemy.engine.Engine [cached since 66.92s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066834', 'decision_confidence', 0.8, 'score')
2025-08-02 00:47:17,171 - sqlalchemy.engine.Engine - INFO - [cached since 66.92s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066834', 'decision_confidence', 0.8, 'score')
2025-08-02 00:47:17,172 INFO sqlalchemy.engine.Engine 
                    INSERT INTO agent_metrics (call_sid, metric_name, metric_value, metric_unit)
                    VALUES ($1, $2, $3, $4)
                
2025-08-02 00:47:17,172 - sqlalchemy.engine.Engine - INFO - 
                    INSERT INTO agent_metrics (call_sid, metric_name, metric_value, metric_unit)
                    VALUES ($1, $2, $3, $4)
                
2025-08-02 00:47:17,172 INFO sqlalchemy.engine.Engine [cached since 66.92s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066834', 'user_engagement', 0.5, 'score')
2025-08-02 00:47:17,172 - sqlalchemy.engine.Engine - INFO - [cached since 66.92s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066834', 'user_engagement', 0.5, 'score')
2025-08-02 00:47:17,173 INFO sqlalchemy.engine.Engine 
                    INSERT INTO agent_metrics (call_sid, metric_name, metric_value, metric_unit)
                    VALUES ($1, $2, $3, $4)
                
2025-08-02 00:47:17,173 - sqlalchemy.engine.Engine - INFO - 
                    INSERT INTO agent_metrics (call_sid, metric_name, metric_value, metric_unit)
                    VALUES ($1, $2, $3, $4)
                
2025-08-02 00:47:17,173 INFO sqlalchemy.engine.Engine [cached since 66.92s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066834', 'info_collection_rate', 0.0, 'rate')
2025-08-02 00:47:17,173 - sqlalchemy.engine.Engine - INFO - [cached since 66.92s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066834', 'info_collection_rate', 0.0, 'rate')
2025-08-02 00:47:17,174 INFO sqlalchemy.engine.Engine COMMIT
2025-08-02 00:47:17,174 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-02 00:47:17,178 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-02 00:47:17,178 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-02 00:47:17,178 INFO sqlalchemy.engine.Engine 
                    INSERT INTO agent_strategies 
                    (call_sid, strategy_name, strategy_config, is_active)
                    VALUES ($1, $2, $3, $4)
                    ON CONFLICT (call_sid, strategy_name) DO UPDATE SET
                        strategy_config = EXCLUDED.strategy_config,
                        is_active = EXCLUDED.is_active,
                        updated_at = NOW()
                
2025-08-02 00:47:17,178 - sqlalchemy.engine.Engine - INFO - 
                    INSERT INTO agent_strategies 
                    (call_sid, strategy_name, strategy_config, is_active)
                    VALUES ($1, $2, $3, $4)
                    ON CONFLICT (call_sid, strategy_name) DO UPDATE SET
                        strategy_config = EXCLUDED.strategy_config,
                        is_active = EXCLUDED.is_active,
                        updated_at = NOW()
                
2025-08-02 00:47:17,178 INFO sqlalchemy.engine.Engine [cached since 66.92s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066834', 'info_collection', '{"missing_fields": ["basic_info", "profession", "personality", "interests", "relationships"], "collection_order": ["basic_info", "profession", "personality", "interests", "relationships"], "current_stage": "greeting"}', True)
2025-08-02 00:47:17,178 - sqlalchemy.engine.Engine - INFO - [cached since 66.92s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066834', 'info_collection', '{"missing_fields": ["basic_info", "profession", "personality", "interests", "relationships"], "collection_order": ["basic_info", "profession", "personality", "interests", "relationships"], "current_stage": "greeting"}', True)
2025-08-02 00:47:17,180 INFO sqlalchemy.engine.Engine COMMIT
2025-08-02 00:47:17,180 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-02 00:47:17,184 - agent.services.conversation_agent - INFO - ⏱️  save_agent_data completed in 0.063s
2025-08-02 00:47:17,436 - api.conversation_relay - INFO - Received ConversationRelay message: interrupt
2025-08-02 00:47:17,437 - api.conversation_relay - INFO - User interrupted in session: VXc04cb73dc5f4dc6acd4fcce5264c2b67
2025-08-02 00:47:18,255 - api.conversation_relay - INFO - Received ConversationRelay message: prompt
2025-08-02 00:47:18,256 - api.conversation_relay - INFO - Processing user input:  (confidence: 1.0)
2025-08-02 00:47:18,256 - agent.services.voice_service - INFO - Processing ConversationRelay input for user 8beb42d4-23b6-4dbf-a3ea-869044eb8881: ''
2025-08-02 00:47:18,256 - agent.services.voice_service - INFO - Session not in memory, loading from database: relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066838
2025-08-02 00:47:18,262 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-02 00:47:18,262 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-02 00:47:18,263 INFO sqlalchemy.engine.Engine 
                        SELECT session_data, user_id, from_number, current_stage,
                               questions_asked, responses_received, last_activity
                        FROM voice_sessions
                        WHERE twilio_call_sid = $1
                        AND session_status = 'active'
                        AND last_activity > NOW() - INTERVAL '2 hours'
                    
2025-08-02 00:47:18,263 - sqlalchemy.engine.Engine - INFO - 
                        SELECT session_data, user_id, from_number, current_stage,
                               questions_asked, responses_received, last_activity
                        FROM voice_sessions
                        WHERE twilio_call_sid = $1
                        AND session_status = 'active'
                        AND last_activity > NOW() - INTERVAL '2 hours'
                    
2025-08-02 00:47:18,263 INFO sqlalchemy.engine.Engine [cached since 71.77s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066838',)
2025-08-02 00:47:18,263 - sqlalchemy.engine.Engine - INFO - [cached since 71.77s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066838',)
2025-08-02 00:47:18,268 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-02 00:47:18,268 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-02 00:47:18,269 - agent.services.voice_service - WARNING - Session not found in memory or database: relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066838
2025-08-02 00:47:18,271 - agent.core.database - ERROR - Failed to save active session: Object of type UUID is not JSON serializable
2025-08-02 00:47:18,271 - agent.services.voice_service - WARNING - Failed to save session to database: relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066838
2025-08-02 00:47:18,271 - agent.services.voice_service - INFO - Using ConversationAgent for call: relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066838
2025-08-02 00:47:18,272 - agent.services.ai_service - INFO - Generating AI question for domain: greeting
2025-08-02 00:47:19,667 - agent.services.ai_service - INFO - AI question generated: What’s something you’re passionate about that you’d love to share with someone special?
2025-08-02 00:47:19,667 - agent.services.conversation_agent - INFO - ⏱️  ai_decision completed in 1.396s
2025-08-02 00:47:19,667 - agent.services.conversation_agent - INFO - ⏱️  make_smart_decision completed in 1.396s
2025-08-02 00:47:19,667 - agent.services.conversation_agent - INFO - ⏱️  process_input completed in 1.396s
2025-08-02 00:47:19,668 - agent.services.voice_service - INFO - Agent decision: ask_question - What’s something you’re passionate about that you’d love to share with someone special? (confidence: 0.8)
2025-08-02 00:47:19,669 - agent.core.database - ERROR - Failed to update session activity: Object of type UUID is not JSON serializable
2025-08-02 00:47:19,671 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-02 00:47:19,671 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-02 00:47:19,671 INFO sqlalchemy.engine.Engine 
                        INSERT INTO agent_decisions 
                        (call_sid, decision_type, decision_data, reasoning, confidence)
                        VALUES ($1, $2, $3, $4, $5)
                    
2025-08-02 00:47:19,671 - sqlalchemy.engine.Engine - INFO - 
                        INSERT INTO agent_decisions 
                        (call_sid, decision_type, decision_data, reasoning, confidence)
                        VALUES ($1, $2, $3, $4, $5)
                    
2025-08-02 00:47:19,671 INFO sqlalchemy.engine.Engine [cached since 69.46s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066838', 'question_generate', '{"action_type": "ask_question", "content": "What\\u2019s something you\\u2019re passionate about that you\\u2019d love to share with someone special? ... (70 characters truncated) ... ull, "emotional_state": {"engagement": 0.5, "comfort": 0.5, "enthusiasm": 0.5}, "collected_info_count": 0, "timestamp": "2025-08-02T00:47:19.668396"}', 'AI generated question based on conversation context', 0.8)
2025-08-02 00:47:19,671 - sqlalchemy.engine.Engine - INFO - [cached since 69.46s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066838', 'question_generate', '{"action_type": "ask_question", "content": "What\\u2019s something you\\u2019re passionate about that you\\u2019d love to share with someone special? ... (70 characters truncated) ... ull, "emotional_state": {"engagement": 0.5, "comfort": 0.5, "enthusiasm": 0.5}, "collected_info_count": 0, "timestamp": "2025-08-02T00:47:19.668396"}', 'AI generated question based on conversation context', 0.8)
2025-08-02 00:47:19,674 INFO sqlalchemy.engine.Engine COMMIT
2025-08-02 00:47:19,674 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-02 00:47:19,677 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-02 00:47:19,677 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-02 00:47:19,677 INFO sqlalchemy.engine.Engine 
                    INSERT INTO agent_conversations 
                    (call_sid, user_id, conversation_stage, topic_transitions, emotional_state,
                     engagement_score, conversation_quality, total_exchanges, successful_responses, timeout_count)
                    VALUES ($1, $2, $3, $4, $5,
                     $6, $7, $8, $9, $10)
                    ON CONFLICT (call_sid) DO UPDATE SET
                        conversation_stage = EXCLUDED.conversation_stage,
                        topic_transitions = EXCLUDED.topic_transitions,
                        emotional_state = EXCLUDED.emotional_state,
                        engagement_score = EXCLUDED.engagement_score,
                        conversation_quality = EXCLUDED.conversation_quality,
                        total_exchanges = EXCLUDED.total_exchanges,
                        successful_responses = EXCLUDED.successful_responses,
                        timeout_count = EXCLUDED.timeout_count,
                        updated_at = NOW()
                
2025-08-02 00:47:19,677 - sqlalchemy.engine.Engine - INFO - 
                    INSERT INTO agent_conversations 
                    (call_sid, user_id, conversation_stage, topic_transitions, emotional_state,
                     engagement_score, conversation_quality, total_exchanges, successful_responses, timeout_count)
                    VALUES ($1, $2, $3, $4, $5,
                     $6, $7, $8, $9, $10)
                    ON CONFLICT (call_sid) DO UPDATE SET
                        conversation_stage = EXCLUDED.conversation_stage,
                        topic_transitions = EXCLUDED.topic_transitions,
                        emotional_state = EXCLUDED.emotional_state,
                        engagement_score = EXCLUDED.engagement_score,
                        conversation_quality = EXCLUDED.conversation_quality,
                        total_exchanges = EXCLUDED.total_exchanges,
                        successful_responses = EXCLUDED.successful_responses,
                        timeout_count = EXCLUDED.timeout_count,
                        updated_at = NOW()
                
2025-08-02 00:47:19,677 INFO sqlalchemy.engine.Engine [cached since 69.46s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066838', UUID('8beb42d4-23b6-4dbf-a3ea-869044eb8881'), 'greeting', '["greeting"]', '{"engagement": 0.5, "comfort": 0.5, "enthusiasm": 0.5}', 0.5, 0.5, 2, 0, 0)
2025-08-02 00:47:19,677 - sqlalchemy.engine.Engine - INFO - [cached since 69.46s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066838', UUID('8beb42d4-23b6-4dbf-a3ea-869044eb8881'), 'greeting', '["greeting"]', '{"engagement": 0.5, "comfort": 0.5, "enthusiasm": 0.5}', 0.5, 0.5, 2, 0, 0)
2025-08-02 00:47:19,679 INFO sqlalchemy.engine.Engine COMMIT
2025-08-02 00:47:19,679 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-02 00:47:19,681 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-02 00:47:19,681 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-02 00:47:19,681 INFO sqlalchemy.engine.Engine 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-08-02 00:47:19,681 - sqlalchemy.engine.Engine - INFO - 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-08-02 00:47:19,682 INFO sqlalchemy.engine.Engine [cached since 69.45s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066838', 'collect_basic_info', 'active', 0.0, 5, '{"required_fields": ["name", "city"], "collected": 0}')
2025-08-02 00:47:19,682 - sqlalchemy.engine.Engine - INFO - [cached since 69.45s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066838', 'collect_basic_info', 'active', 0.0, 5, '{"required_fields": ["name", "city"], "collected": 0}')
2025-08-02 00:47:19,683 INFO sqlalchemy.engine.Engine 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-08-02 00:47:19,683 - sqlalchemy.engine.Engine - INFO - 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-08-02 00:47:19,683 INFO sqlalchemy.engine.Engine [cached since 69.45s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066838', 'collect_profession', 'active', 0.0, 4, '{"required_fields": ["profession"], "collected": 0}')
2025-08-02 00:47:19,683 - sqlalchemy.engine.Engine - INFO - [cached since 69.45s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066838', 'collect_profession', 'active', 0.0, 4, '{"required_fields": ["profession"], "collected": 0}')
2025-08-02 00:47:19,684 INFO sqlalchemy.engine.Engine 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-08-02 00:47:19,684 - sqlalchemy.engine.Engine - INFO - 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-08-02 00:47:19,684 INFO sqlalchemy.engine.Engine [cached since 69.45s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066838', 'collect_personality', 'active', 0.0, 3, '{"required_fields": ["personality_traits"], "collected": 0}')
2025-08-02 00:47:19,684 - sqlalchemy.engine.Engine - INFO - [cached since 69.45s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066838', 'collect_personality', 'active', 0.0, 3, '{"required_fields": ["personality_traits"], "collected": 0}')
2025-08-02 00:47:19,685 INFO sqlalchemy.engine.Engine 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-08-02 00:47:19,685 - sqlalchemy.engine.Engine - INFO - 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-08-02 00:47:19,685 INFO sqlalchemy.engine.Engine [cached since 69.45s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066838', 'collect_interests', 'active', 0.0, 2, '{"required_fields": ["interests"], "collected": 0}')
2025-08-02 00:47:19,685 - sqlalchemy.engine.Engine - INFO - [cached since 69.45s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066838', 'collect_interests', 'active', 0.0, 2, '{"required_fields": ["interests"], "collected": 0}')
2025-08-02 00:47:19,686 INFO sqlalchemy.engine.Engine 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-08-02 00:47:19,686 - sqlalchemy.engine.Engine - INFO - 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-08-02 00:47:19,686 INFO sqlalchemy.engine.Engine [cached since 69.46s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066838', 'collect_relationships', 'active', 0.0, 1, '{"required_fields": ["relationship_preferences"], "collected": 0}')
2025-08-02 00:47:19,686 - sqlalchemy.engine.Engine - INFO - [cached since 69.46s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066838', 'collect_relationships', 'active', 0.0, 1, '{"required_fields": ["relationship_preferences"], "collected": 0}')
2025-08-02 00:47:19,687 INFO sqlalchemy.engine.Engine COMMIT
2025-08-02 00:47:19,687 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-02 00:47:19,690 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-02 00:47:19,690 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-02 00:47:19,690 INFO sqlalchemy.engine.Engine 
                    INSERT INTO agent_memory 
                    (call_sid, memory_type, memory_data)
                    VALUES ($1, $2, $3)
                    ON CONFLICT (call_sid, memory_type) DO UPDATE SET
                        memory_data = EXCLUDED.memory_data,
                        updated_at = NOW()
                
2025-08-02 00:47:19,690 - sqlalchemy.engine.Engine - INFO - 
                    INSERT INTO agent_memory 
                    (call_sid, memory_type, memory_data)
                    VALUES ($1, $2, $3)
                    ON CONFLICT (call_sid, memory_type) DO UPDATE SET
                        memory_data = EXCLUDED.memory_data,
                        updated_at = NOW()
                
2025-08-02 00:47:19,690 INFO sqlalchemy.engine.Engine [cached since 69.44s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066838', 'short_term', '{"recent_exchanges": [{"timestamp": "2025-08-02T00:47:18.271852", "question": "", "response": "", "stage": "greeting"}, {"timestamp": "2025-08-02T00: ... (152 characters truncated) ... "stage": "greeting"}], "current_stage": "greeting", "last_user_input": "", "emotional_state": {"engagement": 0.5, "comfort": 0.5, "enthusiasm": 0.5}}')
2025-08-02 00:47:19,690 - sqlalchemy.engine.Engine - INFO - [cached since 69.44s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066838', 'short_term', '{"recent_exchanges": [{"timestamp": "2025-08-02T00:47:18.271852", "question": "", "response": "", "stage": "greeting"}, {"timestamp": "2025-08-02T00: ... (152 characters truncated) ... "stage": "greeting"}], "current_stage": "greeting", "last_user_input": "", "emotional_state": {"engagement": 0.5, "comfort": 0.5, "enthusiasm": 0.5}}')
2025-08-02 00:47:19,691 INFO sqlalchemy.engine.Engine COMMIT
2025-08-02 00:47:19,691 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-02 00:47:19,693 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-02 00:47:19,693 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-02 00:47:19,693 INFO sqlalchemy.engine.Engine 
                    INSERT INTO agent_metrics (call_sid, metric_name, metric_value, metric_unit)
                    VALUES ($1, $2, $3, $4)
                
2025-08-02 00:47:19,693 - sqlalchemy.engine.Engine - INFO - 
                    INSERT INTO agent_metrics (call_sid, metric_name, metric_value, metric_unit)
                    VALUES ($1, $2, $3, $4)
                
2025-08-02 00:47:19,693 INFO sqlalchemy.engine.Engine [cached since 69.44s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066838', 'decision_confidence', 0.8, 'score')
2025-08-02 00:47:19,693 - sqlalchemy.engine.Engine - INFO - [cached since 69.44s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066838', 'decision_confidence', 0.8, 'score')
2025-08-02 00:47:19,694 INFO sqlalchemy.engine.Engine 
                    INSERT INTO agent_metrics (call_sid, metric_name, metric_value, metric_unit)
                    VALUES ($1, $2, $3, $4)
                
2025-08-02 00:47:19,694 - sqlalchemy.engine.Engine - INFO - 
                    INSERT INTO agent_metrics (call_sid, metric_name, metric_value, metric_unit)
                    VALUES ($1, $2, $3, $4)
                
2025-08-02 00:47:19,694 INFO sqlalchemy.engine.Engine [cached since 69.44s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066838', 'user_engagement', 0.5, 'score')
2025-08-02 00:47:19,694 - sqlalchemy.engine.Engine - INFO - [cached since 69.44s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066838', 'user_engagement', 0.5, 'score')
2025-08-02 00:47:19,695 INFO sqlalchemy.engine.Engine 
                    INSERT INTO agent_metrics (call_sid, metric_name, metric_value, metric_unit)
                    VALUES ($1, $2, $3, $4)
                
2025-08-02 00:47:19,695 - sqlalchemy.engine.Engine - INFO - 
                    INSERT INTO agent_metrics (call_sid, metric_name, metric_value, metric_unit)
                    VALUES ($1, $2, $3, $4)
                
2025-08-02 00:47:19,695 INFO sqlalchemy.engine.Engine [cached since 69.44s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066838', 'info_collection_rate', 0.0, 'rate')
2025-08-02 00:47:19,695 - sqlalchemy.engine.Engine - INFO - [cached since 69.44s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066838', 'info_collection_rate', 0.0, 'rate')
2025-08-02 00:47:19,695 INFO sqlalchemy.engine.Engine COMMIT
2025-08-02 00:47:19,695 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-02 00:47:19,697 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-02 00:47:19,697 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-02 00:47:19,697 INFO sqlalchemy.engine.Engine 
                    INSERT INTO agent_strategies 
                    (call_sid, strategy_name, strategy_config, is_active)
                    VALUES ($1, $2, $3, $4)
                    ON CONFLICT (call_sid, strategy_name) DO UPDATE SET
                        strategy_config = EXCLUDED.strategy_config,
                        is_active = EXCLUDED.is_active,
                        updated_at = NOW()
                
2025-08-02 00:47:19,697 - sqlalchemy.engine.Engine - INFO - 
                    INSERT INTO agent_strategies 
                    (call_sid, strategy_name, strategy_config, is_active)
                    VALUES ($1, $2, $3, $4)
                    ON CONFLICT (call_sid, strategy_name) DO UPDATE SET
                        strategy_config = EXCLUDED.strategy_config,
                        is_active = EXCLUDED.is_active,
                        updated_at = NOW()
                
2025-08-02 00:47:19,697 INFO sqlalchemy.engine.Engine [cached since 69.44s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066838', 'info_collection', '{"missing_fields": ["basic_info", "profession", "personality", "interests", "relationships"], "collection_order": ["basic_info", "profession", "personality", "interests", "relationships"], "current_stage": "greeting"}', True)
2025-08-02 00:47:19,697 - sqlalchemy.engine.Engine - INFO - [cached since 69.44s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066838', 'info_collection', '{"missing_fields": ["basic_info", "profession", "personality", "interests", "relationships"], "collection_order": ["basic_info", "profession", "personality", "interests", "relationships"], "current_stage": "greeting"}', True)
2025-08-02 00:47:19,698 INFO sqlalchemy.engine.Engine COMMIT
2025-08-02 00:47:19,698 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-02 00:47:19,699 - agent.services.conversation_agent - INFO - ⏱️  save_agent_data completed in 0.031s
2025-08-02 00:47:30,045 - api.conversation_relay - INFO - Received ConversationRelay message: prompt
2025-08-02 00:47:30,045 - api.conversation_relay - INFO - Processing user input:  (confidence: 1.0)
2025-08-02 00:47:30,045 - agent.services.voice_service - INFO - Processing ConversationRelay input for user 8beb42d4-23b6-4dbf-a3ea-869044eb8881: ''
2025-08-02 00:47:30,045 - agent.services.voice_service - INFO - Session not in memory, loading from database: relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066850
2025-08-02 00:47:30,049 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-02 00:47:30,049 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-02 00:47:30,050 INFO sqlalchemy.engine.Engine 
                        SELECT session_data, user_id, from_number, current_stage,
                               questions_asked, responses_received, last_activity
                        FROM voice_sessions
                        WHERE twilio_call_sid = $1
                        AND session_status = 'active'
                        AND last_activity > NOW() - INTERVAL '2 hours'
                    
2025-08-02 00:47:30,050 - sqlalchemy.engine.Engine - INFO - 
                        SELECT session_data, user_id, from_number, current_stage,
                               questions_asked, responses_received, last_activity
                        FROM voice_sessions
                        WHERE twilio_call_sid = $1
                        AND session_status = 'active'
                        AND last_activity > NOW() - INTERVAL '2 hours'
                    
2025-08-02 00:47:30,050 INFO sqlalchemy.engine.Engine [cached since 83.56s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066850',)
2025-08-02 00:47:30,050 - sqlalchemy.engine.Engine - INFO - [cached since 83.56s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066850',)
2025-08-02 00:47:30,052 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-02 00:47:30,052 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-02 00:47:30,053 - agent.services.voice_service - WARNING - Session not found in memory or database: relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066850
2025-08-02 00:47:30,054 - agent.core.database - ERROR - Failed to save active session: Object of type UUID is not JSON serializable
2025-08-02 00:47:30,054 - agent.services.voice_service - WARNING - Failed to save session to database: relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066850
2025-08-02 00:47:30,054 - agent.services.voice_service - INFO - Using ConversationAgent for call: relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066850
2025-08-02 00:47:30,055 - agent.services.ai_service - INFO - Generating AI question for domain: greeting
2025-08-02 00:47:31,334 - agent.services.ai_service - INFO - AI question generated: What’s something you’re really passionate about or enjoy doing in your free time?
2025-08-02 00:47:31,334 - agent.services.conversation_agent - INFO - ⏱️  ai_decision completed in 1.280s
2025-08-02 00:47:31,334 - agent.services.conversation_agent - INFO - ⏱️  make_smart_decision completed in 1.280s
2025-08-02 00:47:31,334 - agent.services.conversation_agent - INFO - ⏱️  process_input completed in 1.280s
2025-08-02 00:47:31,335 - agent.services.voice_service - INFO - Agent decision: ask_question - What’s something you’re really passionate about or enjoy doing in your free time? (confidence: 0.8)
2025-08-02 00:47:31,335 - agent.core.database - ERROR - Failed to update session activity: Object of type UUID is not JSON serializable
2025-08-02 00:47:31,338 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-02 00:47:31,338 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-02 00:47:31,338 INFO sqlalchemy.engine.Engine 
                        INSERT INTO agent_decisions 
                        (call_sid, decision_type, decision_data, reasoning, confidence)
                        VALUES ($1, $2, $3, $4, $5)
                    
2025-08-02 00:47:31,338 - sqlalchemy.engine.Engine - INFO - 
                        INSERT INTO agent_decisions 
                        (call_sid, decision_type, decision_data, reasoning, confidence)
                        VALUES ($1, $2, $3, $4, $5)
                    
2025-08-02 00:47:31,338 INFO sqlalchemy.engine.Engine [cached since 81.13s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066850', 'question_generate', '{"action_type": "ask_question", "content": "What\\u2019s something you\\u2019re really passionate about or enjoy doing in your free time?", "user_inp ... (58 characters truncated) ... ull, "emotional_state": {"engagement": 0.5, "comfort": 0.5, "enthusiasm": 0.5}, "collected_info_count": 0, "timestamp": "2025-08-02T00:47:31.335359"}', 'AI generated question based on conversation context', 0.8)
2025-08-02 00:47:31,338 - sqlalchemy.engine.Engine - INFO - [cached since 81.13s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066850', 'question_generate', '{"action_type": "ask_question", "content": "What\\u2019s something you\\u2019re really passionate about or enjoy doing in your free time?", "user_inp ... (58 characters truncated) ... ull, "emotional_state": {"engagement": 0.5, "comfort": 0.5, "enthusiasm": 0.5}, "collected_info_count": 0, "timestamp": "2025-08-02T00:47:31.335359"}', 'AI generated question based on conversation context', 0.8)
2025-08-02 00:47:31,340 INFO sqlalchemy.engine.Engine COMMIT
2025-08-02 00:47:31,340 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-02 00:47:31,343 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-02 00:47:31,343 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-02 00:47:31,343 INFO sqlalchemy.engine.Engine 
                    INSERT INTO agent_conversations 
                    (call_sid, user_id, conversation_stage, topic_transitions, emotional_state,
                     engagement_score, conversation_quality, total_exchanges, successful_responses, timeout_count)
                    VALUES ($1, $2, $3, $4, $5,
                     $6, $7, $8, $9, $10)
                    ON CONFLICT (call_sid) DO UPDATE SET
                        conversation_stage = EXCLUDED.conversation_stage,
                        topic_transitions = EXCLUDED.topic_transitions,
                        emotional_state = EXCLUDED.emotional_state,
                        engagement_score = EXCLUDED.engagement_score,
                        conversation_quality = EXCLUDED.conversation_quality,
                        total_exchanges = EXCLUDED.total_exchanges,
                        successful_responses = EXCLUDED.successful_responses,
                        timeout_count = EXCLUDED.timeout_count,
                        updated_at = NOW()
                
2025-08-02 00:47:31,343 - sqlalchemy.engine.Engine - INFO - 
                    INSERT INTO agent_conversations 
                    (call_sid, user_id, conversation_stage, topic_transitions, emotional_state,
                     engagement_score, conversation_quality, total_exchanges, successful_responses, timeout_count)
                    VALUES ($1, $2, $3, $4, $5,
                     $6, $7, $8, $9, $10)
                    ON CONFLICT (call_sid) DO UPDATE SET
                        conversation_stage = EXCLUDED.conversation_stage,
                        topic_transitions = EXCLUDED.topic_transitions,
                        emotional_state = EXCLUDED.emotional_state,
                        engagement_score = EXCLUDED.engagement_score,
                        conversation_quality = EXCLUDED.conversation_quality,
                        total_exchanges = EXCLUDED.total_exchanges,
                        successful_responses = EXCLUDED.successful_responses,
                        timeout_count = EXCLUDED.timeout_count,
                        updated_at = NOW()
                
2025-08-02 00:47:31,344 INFO sqlalchemy.engine.Engine [cached since 81.12s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066850', UUID('8beb42d4-23b6-4dbf-a3ea-869044eb8881'), 'greeting', '["greeting"]', '{"engagement": 0.5, "comfort": 0.5, "enthusiasm": 0.5}', 0.5, 0.5, 2, 0, 0)
2025-08-02 00:47:31,344 - sqlalchemy.engine.Engine - INFO - [cached since 81.12s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066850', UUID('8beb42d4-23b6-4dbf-a3ea-869044eb8881'), 'greeting', '["greeting"]', '{"engagement": 0.5, "comfort": 0.5, "enthusiasm": 0.5}', 0.5, 0.5, 2, 0, 0)
2025-08-02 00:47:31,345 INFO sqlalchemy.engine.Engine COMMIT
2025-08-02 00:47:31,345 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-02 00:47:31,349 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-02 00:47:31,349 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-02 00:47:31,349 INFO sqlalchemy.engine.Engine 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-08-02 00:47:31,349 - sqlalchemy.engine.Engine - INFO - 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-08-02 00:47:31,349 INFO sqlalchemy.engine.Engine [cached since 81.12s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066850', 'collect_basic_info', 'active', 0.0, 5, '{"required_fields": ["name", "city"], "collected": 0}')
2025-08-02 00:47:31,349 - sqlalchemy.engine.Engine - INFO - [cached since 81.12s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066850', 'collect_basic_info', 'active', 0.0, 5, '{"required_fields": ["name", "city"], "collected": 0}')
2025-08-02 00:47:31,351 INFO sqlalchemy.engine.Engine 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-08-02 00:47:31,351 - sqlalchemy.engine.Engine - INFO - 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-08-02 00:47:31,351 INFO sqlalchemy.engine.Engine [cached since 81.12s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066850', 'collect_profession', 'active', 0.0, 4, '{"required_fields": ["profession"], "collected": 0}')
2025-08-02 00:47:31,351 - sqlalchemy.engine.Engine - INFO - [cached since 81.12s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066850', 'collect_profession', 'active', 0.0, 4, '{"required_fields": ["profession"], "collected": 0}')
2025-08-02 00:47:31,352 INFO sqlalchemy.engine.Engine 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-08-02 00:47:31,352 - sqlalchemy.engine.Engine - INFO - 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-08-02 00:47:31,353 INFO sqlalchemy.engine.Engine [cached since 81.12s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066850', 'collect_personality', 'active', 0.0, 3, '{"required_fields": ["personality_traits"], "collected": 0}')
2025-08-02 00:47:31,353 - sqlalchemy.engine.Engine - INFO - [cached since 81.12s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066850', 'collect_personality', 'active', 0.0, 3, '{"required_fields": ["personality_traits"], "collected": 0}')
2025-08-02 00:47:31,354 INFO sqlalchemy.engine.Engine 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-08-02 00:47:31,354 - sqlalchemy.engine.Engine - INFO - 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-08-02 00:47:31,355 INFO sqlalchemy.engine.Engine [cached since 81.12s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066850', 'collect_interests', 'active', 0.0, 2, '{"required_fields": ["interests"], "collected": 0}')
2025-08-02 00:47:31,355 - sqlalchemy.engine.Engine - INFO - [cached since 81.12s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066850', 'collect_interests', 'active', 0.0, 2, '{"required_fields": ["interests"], "collected": 0}')
2025-08-02 00:47:31,360 INFO sqlalchemy.engine.Engine 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-08-02 00:47:31,360 - sqlalchemy.engine.Engine - INFO - 
                        INSERT INTO agent_goals 
                        (call_sid, goal_name, goal_status, goal_progress, goal_priority, goal_data)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (call_sid, goal_name) DO UPDATE SET
                            goal_status = EXCLUDED.goal_status,
                            goal_progress = EXCLUDED.goal_progress,
                            updated_at = NOW()
                    
2025-08-02 00:47:31,360 INFO sqlalchemy.engine.Engine [cached since 81.13s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066850', 'collect_relationships', 'active', 0.0, 1, '{"required_fields": ["relationship_preferences"], "collected": 0}')
2025-08-02 00:47:31,360 - sqlalchemy.engine.Engine - INFO - [cached since 81.13s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066850', 'collect_relationships', 'active', 0.0, 1, '{"required_fields": ["relationship_preferences"], "collected": 0}')
2025-08-02 00:47:31,361 INFO sqlalchemy.engine.Engine COMMIT
2025-08-02 00:47:31,361 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-02 00:47:31,365 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-02 00:47:31,365 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-02 00:47:31,366 INFO sqlalchemy.engine.Engine 
                    INSERT INTO agent_memory 
                    (call_sid, memory_type, memory_data)
                    VALUES ($1, $2, $3)
                    ON CONFLICT (call_sid, memory_type) DO UPDATE SET
                        memory_data = EXCLUDED.memory_data,
                        updated_at = NOW()
                
2025-08-02 00:47:31,366 - sqlalchemy.engine.Engine - INFO - 
                    INSERT INTO agent_memory 
                    (call_sid, memory_type, memory_data)
                    VALUES ($1, $2, $3)
                    ON CONFLICT (call_sid, memory_type) DO UPDATE SET
                        memory_data = EXCLUDED.memory_data,
                        updated_at = NOW()
                
2025-08-02 00:47:31,366 INFO sqlalchemy.engine.Engine [cached since 81.12s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066850', 'short_term', '{"recent_exchanges": [{"timestamp": "2025-08-02T00:47:30.054674", "question": "", "response": "", "stage": "greeting"}, {"timestamp": "2025-08-02T00: ... (140 characters truncated) ... "stage": "greeting"}], "current_stage": "greeting", "last_user_input": "", "emotional_state": {"engagement": 0.5, "comfort": 0.5, "enthusiasm": 0.5}}')
2025-08-02 00:47:31,366 - sqlalchemy.engine.Engine - INFO - [cached since 81.12s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066850', 'short_term', '{"recent_exchanges": [{"timestamp": "2025-08-02T00:47:30.054674", "question": "", "response": "", "stage": "greeting"}, {"timestamp": "2025-08-02T00: ... (140 characters truncated) ... "stage": "greeting"}], "current_stage": "greeting", "last_user_input": "", "emotional_state": {"engagement": 0.5, "comfort": 0.5, "enthusiasm": 0.5}}')
2025-08-02 00:47:31,370 INFO sqlalchemy.engine.Engine COMMIT
2025-08-02 00:47:31,370 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-02 00:47:31,387 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-02 00:47:31,387 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-02 00:47:31,390 INFO sqlalchemy.engine.Engine 
                    INSERT INTO agent_metrics (call_sid, metric_name, metric_value, metric_unit)
                    VALUES ($1, $2, $3, $4)
                
2025-08-02 00:47:31,390 - sqlalchemy.engine.Engine - INFO - 
                    INSERT INTO agent_metrics (call_sid, metric_name, metric_value, metric_unit)
                    VALUES ($1, $2, $3, $4)
                
2025-08-02 00:47:31,390 INFO sqlalchemy.engine.Engine [cached since 81.14s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066850', 'decision_confidence', 0.8, 'score')
2025-08-02 00:47:31,390 - sqlalchemy.engine.Engine - INFO - [cached since 81.14s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066850', 'decision_confidence', 0.8, 'score')
2025-08-02 00:47:31,395 INFO sqlalchemy.engine.Engine 
                    INSERT INTO agent_metrics (call_sid, metric_name, metric_value, metric_unit)
                    VALUES ($1, $2, $3, $4)
                
2025-08-02 00:47:31,395 - sqlalchemy.engine.Engine - INFO - 
                    INSERT INTO agent_metrics (call_sid, metric_name, metric_value, metric_unit)
                    VALUES ($1, $2, $3, $4)
                
2025-08-02 00:47:31,396 INFO sqlalchemy.engine.Engine [cached since 81.14s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066850', 'user_engagement', 0.5, 'score')
2025-08-02 00:47:31,396 - sqlalchemy.engine.Engine - INFO - [cached since 81.14s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066850', 'user_engagement', 0.5, 'score')
2025-08-02 00:47:31,397 INFO sqlalchemy.engine.Engine 
                    INSERT INTO agent_metrics (call_sid, metric_name, metric_value, metric_unit)
                    VALUES ($1, $2, $3, $4)
                
2025-08-02 00:47:31,397 - sqlalchemy.engine.Engine - INFO - 
                    INSERT INTO agent_metrics (call_sid, metric_name, metric_value, metric_unit)
                    VALUES ($1, $2, $3, $4)
                
2025-08-02 00:47:31,397 INFO sqlalchemy.engine.Engine [cached since 81.15s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066850', 'info_collection_rate', 0.0, 'rate')
2025-08-02 00:47:31,397 - sqlalchemy.engine.Engine - INFO - [cached since 81.15s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066850', 'info_collection_rate', 0.0, 'rate')
2025-08-02 00:47:31,398 INFO sqlalchemy.engine.Engine COMMIT
2025-08-02 00:47:31,398 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-02 00:47:31,413 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-02 00:47:31,413 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-02 00:47:31,413 INFO sqlalchemy.engine.Engine 
                    INSERT INTO agent_strategies 
                    (call_sid, strategy_name, strategy_config, is_active)
                    VALUES ($1, $2, $3, $4)
                    ON CONFLICT (call_sid, strategy_name) DO UPDATE SET
                        strategy_config = EXCLUDED.strategy_config,
                        is_active = EXCLUDED.is_active,
                        updated_at = NOW()
                
2025-08-02 00:47:31,413 - sqlalchemy.engine.Engine - INFO - 
                    INSERT INTO agent_strategies 
                    (call_sid, strategy_name, strategy_config, is_active)
                    VALUES ($1, $2, $3, $4)
                    ON CONFLICT (call_sid, strategy_name) DO UPDATE SET
                        strategy_config = EXCLUDED.strategy_config,
                        is_active = EXCLUDED.is_active,
                        updated_at = NOW()
                
2025-08-02 00:47:31,413 INFO sqlalchemy.engine.Engine [cached since 81.15s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066850', 'info_collection', '{"missing_fields": ["basic_info", "profession", "personality", "interests", "relationships"], "collection_order": ["basic_info", "profession", "personality", "interests", "relationships"], "current_stage": "greeting"}', True)
2025-08-02 00:47:31,413 - sqlalchemy.engine.Engine - INFO - [cached since 81.15s ago] ('relay_8beb42d4-23b6-4dbf-a3ea-869044eb8881_1754066850', 'info_collection', '{"missing_fields": ["basic_info", "profession", "personality", "interests", "relationships"], "collection_order": ["basic_info", "profession", "personality", "interests", "relationships"], "current_stage": "greeting"}', True)
2025-08-02 00:47:31,416 INFO sqlalchemy.engine.Engine COMMIT
2025-08-02 00:47:31,416 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-02 00:47:31,418 - agent.services.conversation_agent - INFO - ⏱️  save_agent_data completed in 0.083s
2025-08-02 00:47:38,362 - api.conversation_relay - INFO - ConversationRelay disconnected: VXc04cb73dc5f4dc6acd4fcce5264c2b67
INFO:     connection closed
2025-08-02 00:47:38,423 - api.voice - INFO - Call status webhook: {'Called': '+***********', 'ToState': 'CA', 'CallerCountry': 'US', 'Direction': 'outbound-api', 'Timestamp': 'Fri, 01 Aug 2025 16:47:38 +0000', 'CallbackSource': 'call-progress-events', 'SipResponseCode': '200', 'CallerState': 'CA', 'ToZip': '', 'SequenceNumber': '3', 'CallSid': 'CA5a847236d7b5751b50bef741d1723cd1', 'To': '+***********', 'CallerZip': '', 'ToCountry': 'US', 'CalledZip': '', 'ApiVersion': '2010-04-01', 'CalledCity': '', 'CallStatus': 'completed', 'Duration': '2', 'From': '+***********', 'CallDuration': '108', 'AccountSid': 'AC98fddc0d9bf796ef17233cf22bbe31f5', 'CalledCountry': 'US', 'CallerCity': 'GUADALUPE', 'ToCity': '', 'FromCountry': 'US', 'Caller': '+***********', 'FromCity': 'GUADALUPE', 'CalledState': 'CA', 'FromZip': '', 'FromState': 'CA'}
2025-08-02 00:47:38,423 - agent.services.voice_service - INFO - Call status update: CA5a847236d7b5751b50bef741d1723cd1 -> completed (duration: 108)
2025-08-02 00:47:38,424 - agent.services.voice_service - INFO - Processing call completion via status webhook: CA5a847236d7b5751b50bef741d1723cd1 -> completed
2025-08-02 00:47:38,424 - agent.services.voice_service - INFO - Handling call completion: CA5a847236d7b5751b50bef741d1723cd1, status: completed, duration: 108
2025-08-02 00:47:38,424 - agent.services.voice_service - WARNING - Insufficient responses for analysis: 8beb42d4-23b6-4dbf-a3ea-869044eb8881
2025-08-02 00:47:38,428 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-02 00:47:38,428 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-02 00:47:38,430 INFO sqlalchemy.engine.Engine 
                        UPDATE voice_sessions SET
                            session_status = 'completed',
                            call_duration = $1,
                            analysis_data = $2,
                            completed_at = NOW()
                        WHERE twilio_call_sid = $3
                    
2025-08-02 00:47:38,430 - sqlalchemy.engine.Engine - INFO - 
                        UPDATE voice_sessions SET
                            session_status = 'completed',
                            call_duration = $1,
                            analysis_data = $2,
                            completed_at = NOW()
                        WHERE twilio_call_sid = $3
                    
2025-08-02 00:47:38,430 INFO sqlalchemy.engine.Engine [generated in 0.00033s] (129, '{"transcript": "", "collected_info": {}, "stage_progression": [], "conversation_transcript": [], "quality_score": 0.129, "questions_asked": 0, "responses_received": 0, "call_status": "completed", "completion_status": "insufficient_responses"}', 'CA5a847236d7b5751b50bef741d1723cd1')
2025-08-02 00:47:38,430 - sqlalchemy.engine.Engine - INFO - [generated in 0.00033s] (129, '{"transcript": "", "collected_info": {}, "stage_progression": [], "conversation_transcript": [], "quality_score": 0.129, "questions_asked": 0, "responses_received": 0, "call_status": "completed", "completion_status": "insufficient_responses"}', 'CA5a847236d7b5751b50bef741d1723cd1')
2025-08-02 00:47:38,442 INFO sqlalchemy.engine.Engine COMMIT
2025-08-02 00:47:38,442 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-02 00:47:38,445 - agent.services.voice_service - INFO - Partial voice session saved to database: CA5a847236d7b5751b50bef741d1723cd1
INFO:     *************:0 - "POST /voice/webhook/status HTTP/1.1" 200 OK
