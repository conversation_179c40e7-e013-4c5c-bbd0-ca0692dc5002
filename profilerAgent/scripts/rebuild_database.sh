#!/bin/bash

# AI Agent数据库重建脚本
# 删除旧数据库，创建新的AI Agent架构

set -e  # 遇到错误立即退出

echo "🔄 开始重建AI Agent数据库..."

# 检查是否在正确的目录
if [ ! -f "docker-compose.yml" ]; then
    echo "❌ 错误：请在profilerAgent目录下运行此脚本"
    exit 1
fi

# 停止现有服务
echo "⏹️  停止现有Docker服务..."
docker-compose down -v

# 删除现有数据卷
echo "🗑️  删除现有数据卷..."
docker volume rm profiler_agent_postgres_data 2>/dev/null || true
docker volume rm profiler_agent_redis_data 2>/dev/null || true

# 删除旧的容器和镜像（如果存在）
echo "🧹 清理旧容器..."
docker container rm ai_agent_postgres ai_agent_redis ai_agent_pgadmin ai_agent_redis_commander 2>/dev/null || true

# 启动新的数据库服务
echo "🚀 启动新的AI Agent数据库..."
docker-compose up -d postgres redis

# 等待数据库启动
echo "⏳ 等待数据库启动..."
sleep 10

# 检查数据库连接
echo "🔍 检查数据库连接..."
max_attempts=30
attempt=1

while [ $attempt -le $max_attempts ]; do
    if docker-compose exec -T postgres pg_isready -U ai_agent_user -d ai_agent_db > /dev/null 2>&1; then
        echo "✅ 数据库连接成功！"
        break
    fi
    
    if [ $attempt -eq $max_attempts ]; then
        echo "❌ 数据库连接失败，请检查配置"
        exit 1
    fi
    
    echo "⏳ 等待数据库启动... (尝试 $attempt/$max_attempts)"
    sleep 2
    ((attempt++))
done

# 检查Redis连接
echo "🔍 检查Redis连接..."
if docker-compose exec -T redis redis-cli ping > /dev/null 2>&1; then
    echo "✅ Redis连接成功！"
else
    echo "❌ Redis连接失败，请检查配置"
    exit 1
fi

# 验证数据库schema
echo "🔍 验证数据库schema..."
table_count=$(docker-compose exec -T postgres psql -U ai_agent_user -d ai_agent_db -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';" | tr -d ' ')

if [ "$table_count" -gt 5 ]; then
    echo "✅ 数据库schema创建成功！创建了 $table_count 个表"
else
    echo "❌ 数据库schema创建可能有问题，只创建了 $table_count 个表"
    exit 1
fi

# 显示创建的表
echo "📋 创建的数据库表："
docker-compose exec -T postgres psql -U ai_agent_user -d ai_agent_db -c "\dt"

# 启动管理工具（可选）
echo "🛠️  启动管理工具..."
docker-compose up -d pgadmin redis-commander

echo ""
echo "🎉 AI Agent数据库重建完成！"
echo ""
echo "📊 服务信息："
echo "   - PostgreSQL: localhost:5432"
echo "   - Redis: localhost:6380"
echo "   - pgAdmin: http://localhost:8080 (<EMAIL> / admin123)"
echo "   - Redis Commander: http://localhost:8081"
echo ""
echo "🗄️  数据库信息："
echo "   - 数据库名: ai_agent_db"
echo "   - 用户名: ai_agent_user"
echo "   - 密码: ai_agent_password"
echo ""
echo "📝 下一步："
echo "   1. 更新应用配置文件中的数据库连接信息"
echo "   2. 重启应用服务"
echo "   3. 测试AI Agent功能"
echo ""
