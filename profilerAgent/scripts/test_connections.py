#!/usr/bin/env python3
"""
测试AI Agent数据库和Redis连接
"""

import os
import sys
import asyncio
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

async def test_database_connection():
    """测试PostgreSQL数据库连接"""
    try:
        from backend.database.connection import get_db_engine
        
        print("🔍 测试PostgreSQL连接...")
        engine = get_db_engine()
        
        # 测试连接
        from sqlalchemy import text
        with engine.connect() as conn:
            result = conn.execute(text("SELECT version();"))
            version = result.fetchone()[0]
            print(f"✅ PostgreSQL连接成功!")
            print(f"   版本: {version[:50]}...")

        # 测试表是否存在
        with engine.connect() as conn:
            result = conn.execute(text("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';"))
            table_count = result.fetchone()[0]
            print(f"✅ 数据库表数量: {table_count}")
            
        return True
        
    except Exception as e:
        print(f"❌ PostgreSQL连接失败: {e}")
        return False

async def test_redis_connection():
    """测试Redis连接"""
    try:
        from backend.cache.ai_agent_cache import AIAgentCache
        
        print("🔍 测试Redis连接...")
        cache = AIAgentCache()
        
        # 测试基本连接
        conn = await cache._get_connection(0)
        await conn.ping()
        print("✅ Redis连接成功!")
        
        # 测试写入和读取
        test_key = "test:connection"
        await conn.set(test_key, "Hello AI Agent!", ex=10)
        value = await conn.get(test_key)
        print(f"✅ Redis读写测试成功: {value}")
        
        # 清理测试数据
        await conn.delete(test_key)
        
        # 关闭连接
        await cache.close()
        return True
        
    except Exception as e:
        print(f"❌ Redis连接失败: {e}")
        return False

async def test_ai_agent_cache():
    """测试AI Agent缓存功能"""
    try:
        from backend.cache.ai_agent_cache import AIAgentCache
        
        print("🔍 测试AI Agent缓存功能...")
        cache = AIAgentCache()
        
        # 测试用户上下文缓存
        test_user_id = "test_user_123"
        test_context = {
            "short_term_memory": [
                {"role": "user", "content": "Hello"},
                {"role": "assistant", "content": "Hi there!"}
            ],
            "conversation_summary": "User greeted the assistant",
            "active_conversation_id": "conv_123"
        }
        
        # 保存上下文
        success = await cache.update_user_context(test_user_id, test_context)
        print(f"✅ 用户上下文保存: {'成功' if success else '失败'}")
        
        # 读取上下文
        retrieved_context = await cache.get_user_context(test_user_id)
        print(f"✅ 用户上下文读取: {len(retrieved_context['short_term_memory'])} 条消息")
        
        # 测试短期记忆添加
        success = await cache.add_to_short_term_memory(test_user_id, "user", "How are you?")
        print(f"✅ 短期记忆添加: {'成功' if success else '失败'}")
        
        # 清理测试数据
        await cache.clear_user_data(test_user_id)
        print("✅ 测试数据清理完成")
        
        await cache.close()
        return True
        
    except Exception as e:
        print(f"❌ AI Agent缓存测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 开始AI Agent连接测试...")
    print("=" * 50)
    
    # 显示配置信息
    print("📋 当前配置:")
    print(f"   DATABASE_URL: {os.getenv('DATABASE_URL')}")
    print(f"   REDIS_URL: {os.getenv('REDIS_URL')}")
    print()
    
    # 测试数据库连接
    db_success = await test_database_connection()
    print()
    
    # 测试Redis连接
    redis_success = await test_redis_connection()
    print()
    
    # 测试AI Agent缓存
    cache_success = await test_ai_agent_cache()
    print()
    
    # 总结
    print("=" * 50)
    if db_success and redis_success and cache_success:
        print("🎉 所有连接测试通过！AI Agent数据库架构准备就绪！")
        return 0
    else:
        print("❌ 部分测试失败，请检查配置和服务状态")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
