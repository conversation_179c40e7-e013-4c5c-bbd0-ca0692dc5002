#!/bin/bash

# AI Dating App - 服务启动脚本

echo "🚀 启动AI Dating App基础服务..."

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker未运行，请先启动Docker"
    exit 1
fi

# 检查docker-compose是否存在
if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose未安装，请先安装docker-compose"
    exit 1
fi

# 停止现有服务（如果有）
echo "🛑 停止现有服务..."
docker-compose down

# 启动服务
echo "🔄 启动PostgreSQL和Redis..."
docker-compose up -d postgres redis

# 等待数据库启动
echo "⏳ 等待数据库启动..."
sleep 10

# 检查服务状态
echo "🔍 检查服务状态..."
docker-compose ps

# 测试数据库连接
echo "🧪 测试数据库连接..."
if docker-compose exec -T postgres pg_isready -U dating_app_user -d dating_app_db; then
    echo "✅ PostgreSQL连接成功"
else
    echo "❌ PostgreSQL连接失败"
fi

# 测试Redis连接
echo "🧪 测试Redis连接..."
if docker-compose exec -T redis redis-cli ping | grep -q PONG; then
    echo "✅ Redis连接成功"
else
    echo "❌ Redis连接失败"
fi

echo ""
echo "🎉 基础服务启动完成！"
echo ""
echo "📊 服务信息："
echo "  PostgreSQL: localhost:5432"
echo "  Redis: localhost:6380"
echo "  pgAdmin: http://localhost:8080 (<EMAIL> / admin123)"
echo "  Redis Commander: http://localhost:8081"
echo ""
echo "🔧 管理命令："
echo "  查看日志: docker-compose logs -f"
echo "  停止服务: docker-compose down"
echo "  重启服务: docker-compose restart"
echo "  查看状态: docker-compose ps"
echo ""
echo "💡 下一步："
echo "  1. 配置backend连接数据库"
echo "  2. 运行数据库迁移"
echo "  3. 启动FastAPI应用"
echo "  4. 启动前端开发服务器"
