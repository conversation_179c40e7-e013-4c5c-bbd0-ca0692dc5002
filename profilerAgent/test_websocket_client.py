#!/usr/bin/env python3
"""
WebSocket 客户端测试脚本
用于测试 WebSocket 连接和消息交互
"""

import asyncio
import websockets
import json
import sys
from datetime import datetime

class WebSocketTester:
    def __init__(self, url):
        self.url = url
        self.websocket = None
        
    async def connect(self):
        """连接到 WebSocket 服务器"""
        try:
            print(f"Connecting to {self.url}...")
            self.websocket = await websockets.connect(self.url)
            print("✅ Connected successfully!")
            return True
        except Exception as e:
            print(f"❌ Connection failed: {e}")
            return False
    
    async def disconnect(self):
        """断开 WebSocket 连接"""
        if self.websocket:
            await self.websocket.close()
            print("🔌 Disconnected")
    
    async def send_message(self, message):
        """发送消息"""
        if not self.websocket:
            print("❌ Not connected")
            return
        
        try:
            await self.websocket.send(json.dumps(message))
            print(f"📤 Sent: {json.dumps(message, indent=2)}")
        except Exception as e:
            print(f"❌ Send failed: {e}")
    
    async def receive_messages(self):
        """接收消息循环"""
        if not self.websocket:
            print("❌ Not connected")
            return
        
        try:
            async for message in self.websocket:
                try:
                    data = json.loads(message)
                    print(f"📥 Received: {json.dumps(data, indent=2)}")
                except json.JSONDecodeError:
                    print(f"📥 Received (raw): {message}")
        except websockets.exceptions.ConnectionClosed:
            print("🔌 Connection closed by server")
        except Exception as e:
            print(f"❌ Receive error: {e}")

async def test_basic_websocket():
    """测试基础 WebSocket 功能"""
    print("🧪 Testing Basic WebSocket...")
    
    tester = WebSocketTester("ws://localhost:8000/ws-test/basic")
    
    if not await tester.connect():
        return
    
    # 启动接收消息的任务
    receive_task = asyncio.create_task(tester.receive_messages())
    
    try:
        # 等待欢迎消息
        await asyncio.sleep(1)
        
        # 测试 ping
        print("\n🏓 Testing ping...")
        await tester.send_message({"type": "ping"})
        await asyncio.sleep(1)
        
        # 测试 echo
        print("\n🔄 Testing echo...")
        await tester.send_message({
            "type": "echo", 
            "message": "Hello WebSocket!"
        })
        await asyncio.sleep(1)
        
        # 测试状态查询
        print("\n📊 Testing status...")
        await tester.send_message({"type": "status"})
        await asyncio.sleep(1)
        
        # 测试广播
        print("\n📢 Testing broadcast...")
        await tester.send_message({
            "type": "broadcast",
            "message": "This is a test broadcast message"
        })
        await asyncio.sleep(1)
        
        # 测试无效消息
        print("\n❌ Testing invalid message...")
        await tester.send_message({"type": "unknown_type"})
        await asyncio.sleep(1)
        
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
    finally:
        receive_task.cancel()
        await tester.disconnect()

async def test_conversation_relay_mock():
    """测试 ConversationRelay 模拟功能"""
    print("🧪 Testing ConversationRelay Mock...")
    
    tester = WebSocketTester("ws://localhost:8000/ws-test/conversation-relay-mock")
    
    if not await tester.connect():
        return
    
    # 启动接收消息的任务
    receive_task = asyncio.create_task(tester.receive_messages())
    
    try:
        # 等待连接稳定
        await asyncio.sleep(1)
        
        # 模拟 setup 消息
        print("\n🚀 Sending setup message...")
        await tester.send_message({
            "type": "setup",
            "sessionId": "test_session_123",
            "callSid": "test_call_456",
            "accountSid": "test_account",
            "from": "+**********",
            "to": "+**********"
        })
        await asyncio.sleep(2)
        
        # 模拟用户输入
        print("\n💬 Simulating user conversation...")
        
        conversation_inputs = [
            "Hello there!",
            "My name is John",
            "I like hiking and reading books",
            "I also enjoy cooking and traveling"
        ]
        
        for user_input in conversation_inputs:
            print(f"\n👤 User says: {user_input}")
            await tester.send_message({
                "type": "prompt",
                "text": user_input,
                "confidence": 0.95,
                "timestamp": datetime.now().isoformat()
            })
            await asyncio.sleep(3)  # 等待AI回复
        
        # 测试中断
        print("\n✋ Testing interrupt...")
        await tester.send_message({
            "type": "interrupt",
            "timestamp": datetime.now().isoformat()
        })
        await asyncio.sleep(2)
        
        # 测试 DTMF
        print("\n📞 Testing DTMF...")
        await tester.send_message({
            "type": "dtmf",
            "digit": "*",
            "timestamp": datetime.now().isoformat()
        })
        await asyncio.sleep(2)
        
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
    finally:
        receive_task.cancel()
        await tester.disconnect()

async def interactive_test():
    """交互式测试模式"""
    print("🎮 Interactive WebSocket Test Mode")
    print("Available endpoints:")
    print("1. Basic WebSocket Test")
    print("2. ConversationRelay Mock Test")
    
    choice = input("Choose test (1 or 2): ").strip()
    
    if choice == "1":
        url = "ws://localhost:8000/ws-test/basic"
    elif choice == "2":
        url = "ws://localhost:8000/ws-test/conversation-relay-mock"
    else:
        print("Invalid choice")
        return
    
    tester = WebSocketTester(url)
    
    if not await tester.connect():
        return
    
    # 启动接收消息的任务
    receive_task = asyncio.create_task(tester.receive_messages())
    
    print("\n💡 Interactive mode started!")
    print("Commands:")
    print("  ping - Send ping message")
    print("  echo <message> - Send echo message")
    print("  status - Get status")
    print("  broadcast <message> - Send broadcast")
    print("  setup - Send ConversationRelay setup (for mock)")
    print("  prompt <text> - Send user prompt (for mock)")
    print("  quit - Exit")
    
    try:
        while True:
            command = input("\n> ").strip()
            
            if command == "quit":
                break
            elif command == "ping":
                await tester.send_message({"type": "ping"})
            elif command == "status":
                await tester.send_message({"type": "status"})
            elif command.startswith("echo "):
                message = command[5:]
                await tester.send_message({"type": "echo", "message": message})
            elif command.startswith("broadcast "):
                message = command[10:]
                await tester.send_message({"type": "broadcast", "message": message})
            elif command == "setup":
                await tester.send_message({
                    "type": "setup",
                    "sessionId": "interactive_session",
                    "callSid": "interactive_call"
                })
            elif command.startswith("prompt "):
                text = command[7:]
                await tester.send_message({
                    "type": "prompt",
                    "text": text,
                    "confidence": 0.95
                })
            else:
                print("Unknown command")
                
    except KeyboardInterrupt:
        print("\n⏹️  Interactive mode interrupted")
    finally:
        receive_task.cancel()
        await tester.disconnect()

async def main():
    """主函数"""
    if len(sys.argv) > 1:
        mode = sys.argv[1]
        if mode == "basic":
            await test_basic_websocket()
        elif mode == "mock":
            await test_conversation_relay_mock()
        elif mode == "interactive":
            await interactive_test()
        else:
            print("Usage: python test_websocket_client.py [basic|mock|interactive]")
    else:
        print("🧪 WebSocket Test Suite")
        print("=" * 50)
        
        print("\n1️⃣  Running Basic WebSocket Test...")
        await test_basic_websocket()
        
        print("\n" + "=" * 50)
        print("2️⃣  Running ConversationRelay Mock Test...")
        await test_conversation_relay_mock()
        
        print("\n✅ All tests completed!")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Tests interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
